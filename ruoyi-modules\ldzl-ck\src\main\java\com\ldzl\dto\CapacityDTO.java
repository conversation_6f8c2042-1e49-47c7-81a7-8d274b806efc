package com.ldzl.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 容量信息
 */
@Data
public class CapacityDTO extends BaseEntity implements Serializable {

    /**
     * 仓库id
     */
    private Long warehouse_id;

    /**
     * 库区id
     */
    private Long location_id;

    /**
     * 库位id
     */
    private Long area_id;

    /**
     * 最大容量
     */
    private BigDecimal max_weight;

    /**
     * 已使用容量
     */
    private BigDecimal used_weight;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
