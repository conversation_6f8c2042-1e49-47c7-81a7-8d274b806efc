package com.ruoyi.system.api.domain;

import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class BasicStationDto extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private Long station_id; // 工位id
    private Long factory_id; // 工厂id
    private String factory_name; // 所属工厂
    private Long workshop_id; // 车间id
    private String workshop_name; // 所属车间
    private Long production_line_id; // 生产线id
    private String production_line_name; // 所属生产线
    private Long process_id; // 工序id
    private String station_code; // 工位编码
    private String station_name; // 工位名称
    private String remarks; // 备注
} 