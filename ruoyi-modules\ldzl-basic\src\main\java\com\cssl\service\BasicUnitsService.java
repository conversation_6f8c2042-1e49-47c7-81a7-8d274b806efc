package com.cssl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cssl.pojo.BasicUnits;
import com.cssl.pojo.BasicWlfl;

import java.util.List;

public interface BasicUnitsService extends IService<BasicUnits> {
    //查询所有单位
    public List<BasicUnits> listBasicUnits(BasicUnits basicUnits);

    //添加主单位
    public int addBasicUnits(BasicUnits basicUnits);

    //查询正在启用的单位
    public List<BasicUnits>findByBasicUnits();

    //修改单位
    public int updateBasicUnits(BasicUnits basicUnits);

    //删除单位
    public int delBasicUnits(Long unit_id);

    //批量删除
    public int delBatchBasicUnits(List<Long> unit_ids);
}
