{"remainingRequest": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\src\\views\\sc\\plan\\add_plan.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\src\\views\\sc\\plan\\add_plan.vue", "mtime": 1753785710311}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751945356000}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751945361444}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751945356000}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751945364156}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGFkZFByb2R1Y3Rpb25QbGFuIH0gZnJvbSAiQC9hcGkvc2MvcHJvZHVjdGlvblBsYW4iOwppbXBvcnQgeyBsaXN0UHJvZHVjdHMsIGxpc3RCb21zQnlQcm9kdWN0SWQsIGZpbmRCb21EZXRhaWxzIH0gZnJvbSAiQC9hcGkvYmFzaWMvcHJvZHVjdCI7CmltcG9ydCB7IGdldEF1dG9OdW1iZXJzIH0gZnJvbSAiQC9hcGkvYmFzaWMvbnVtYmVycyI7CmltcG9ydCBQYWdpbmF0aW9uIGZyb20gIkAvY29tcG9uZW50cy9QYWdpbmF0aW9uIjsKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiQWRkUGxhbiIsCiAgY29tcG9uZW50czogewogICAgUGFnaW5hdGlvbgogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOihqOWNleaVsOaNrgogICAgICBmb3JtOiB7CiAgICAgICAgcGxhbkNvZGU6ICIiLAogICAgICAgIHBsYW5OYW1lOiAiIiwKICAgICAgICBzb3VyY2VUeXBlOiAi6ZSA5ZSu6K6i5Y2VIiwKICAgICAgICBvcmRlckNvZGU6ICIiLAogICAgICAgIHByb2R1Y3RJZDogdW5kZWZpbmVkLAogICAgICAgIHByb2R1Y3ROYW1lOiAiIiwKICAgICAgICBwcm9kdWN0Q29kZTogIiIsCiAgICAgICAgc3BlY2lmaWNhdGlvbjogIiIsCiAgICAgICAgdW5pdDogIiIsCiAgICAgICAgcGxhbm5lZFF0eTogMSwKICAgICAgICBwbGFuU3RhcnRUaW1lOiAiIiwKICAgICAgICBwbGFuRW5kVGltZTogIiIsCiAgICAgICAgcmVxdWlyZWREYXRlOiAiIiwKICAgICAgICByZW1hcms6ICIiCiAgICAgIH0sCiAgICAgIC8vIOaYr+WQpuS9v+eUqOezu+e7n+e8luWPtwogICAgICBpc1N5c3RlbUNvZGU6IHRydWUsCiAgICAgIC8vIOihqOWNlemqjOivgeinhOWImQogICAgICBydWxlczogewogICAgICAgIHBsYW5OYW1lOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K6h5YiS5ZCN56ew5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sCiAgICAgICAgICB7IG1heDogNTAsIG1lc3NhZ2U6ICLplb/luqbkuI3og73otoXov4c1MOS4quWtl+espiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBzb3VyY2VUeXBlOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5p2l5rqQ57G75Z6L5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImNoYW5nZSIgfQogICAgICAgIF0sCiAgICAgICAgcHJvZHVjdE5hbWU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fpgInmi6nkuqflk4EiLCB0cmlnZ2VyOiAiY2hhbmdlIiB9CiAgICAgICAgXSwKICAgICAgICBwbGFubmVkUXR5OiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K6h5YiS5pWw6YeP5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIHBsYW5TdGFydFRpbWU6IFsKICAgICAgICAgIHsgdmFsaWRhdG9yOiB0aGlzLnZhbGlkYXRlU3RhcnRUaW1lLCB0cmlnZ2VyOiAiY2hhbmdlIiB9CiAgICAgICAgXSwKICAgICAgICBwbGFuRW5kVGltZTogWwogICAgICAgICAgeyB2YWxpZGF0b3I6IHRoaXMudmFsaWRhdGVFbmRUaW1lLCB0cmlnZ2VyOiAiY2hhbmdlIiB9CiAgICAgICAgXSwKICAgICAgICByZXF1aXJlZERhdGU6IFsKICAgICAgICAgIHsgdmFsaWRhdG9yOiB0aGlzLnZhbGlkYXRlUmVxdWlyZWREYXRlLCB0cmlnZ2VyOiAiY2hhbmdlIiB9CiAgICAgICAgXQogICAgICB9LAogICAgICAvLyDkuqflk4HkuIvmi4npgInpobkKICAgICAgcHJvZHVjdE9wdGlvbnM6IFtdLAogICAgICAvLyDkuqflk4HliqDovb3nirbmgIEKICAgICAgcHJvZHVjdExvYWRpbmc6IGZhbHNlLAogICAgICAvLyDmnaXmupDnsbvlnovpgInpobkKICAgICAgc291cmNlVHlwZU9wdGlvbnM6IFsKICAgICAgICB7IGRpY3RMYWJlbDogIumUgOWUruiuouWNlSIsIGRpY3RWYWx1ZTogIumUgOWUruiuouWNlSIgfSwKICAgICAgICB7IGRpY3RMYWJlbDogIuW6k+WtmOWkh+i0pyIsIGRpY3RWYWx1ZTogIuW6k+WtmOWkh+i0pyIgfSwKICAgICAgICB7IGRpY3RMYWJlbDogIueUn+S6p+iuouWNlSIsIGRpY3RWYWx1ZTogIlBST0RVQ1RJT05fT1JERVIiIH0KICAgICAgXSwKICAgICAgLy8g5LiK5Lyg5paH5Lu25YiX6KGoCiAgICAgIGZpbGVMaXN0OiBbXSwKICAgICAgCiAgICAgIC8vIOS6p+WTgemAieaLqeWvueivneahhgogICAgICBwcm9kdWN0RGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIHByb2R1Y3RRdWVyeTogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIGtleXdvcmQ6ICIiLAogICAgICAgIHVuaXQ6ICIiLAogICAgICAgIHR5cGU6ICIiLAogICAgICAgIHByb3BlcnR5OiAiIgogICAgICB9LAogICAgICBwcm9kdWN0TGlzdDogW10sCiAgICAgIHByb2R1Y3RUb3RhbDogMCwKCiAgICAgIC8vIOeUn+S6p+iuouWNlemAieaLqeWvueivneahhgogICAgICBwcm9kdWN0aW9uT3JkZXJEaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgcHJvZHVjdGlvbk9yZGVyTGlzdDogW10sCiAgICAgIHNlbGVjdGVkUHJvZHVjdGlvbk9yZGVyOiBudWxsLAogICAgICBzZWxlY3RlZFByb2R1Y3Q6IG51bGwsCiAgICAgIAogICAgICAvLyBCT03pgInmi6nlr7nor53moYYKICAgICAgYm9tRGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIGJvbVF1ZXJ5OiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgcHJvZHVjdElkOiB1bmRlZmluZWQKICAgICAgfSwKICAgICAgYm9tTGlzdDogW10sCiAgICAgIGJvbVRvdGFsOiAwLAogICAgICBib21Mb2FkaW5nOiBmYWxzZSwKICAgICAgc2VsZWN0ZWRCb206IG51bGwsCiAgICAgIHNlbGVjdGVkQm9tSWQ6IG51bGwsCiAgICAgIGJvbURldGFpbExpc3Q6IFtdLAogICAgfTsKICB9LAogIGNyZWF0ZWQoKSB7CiAgICAvLyDliJ3lp4vljJbml7blpoLmnpzmmK/ns7vnu5/nvJblj7fvvIzliJnnlJ/miJDorqHliJLnvJblj7cKICAgIGlmICh0aGlzLmlzU3lzdGVtQ29kZSkgewogICAgICB0aGlzLmdlbmVyYXRlUGxhbkNvZGUoKTsKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIC8vIOeUn+aIkOiuoeWIkue8luWPtwogICAgZ2VuZXJhdGVQbGFuQ29kZSgpIHsKICAgICAgZ2V0QXV0b051bWJlcnMoNikudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgewogICAgICAgICAgdGhpcy5mb3JtLnBsYW5Db2RlID0gcmVzcG9uc2UubXNnOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfojrflj5borqHliJLnvJblj7flpLHotKUnKTsKICAgICAgICB9CiAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfojrflj5borqHliJLnvJblj7flpLHotKUnKTsKICAgICAgfSk7CiAgICB9LAogICAgCiAgICAvLyDlpITnkIbns7vnu5/nvJblj7flvIDlhbPlj5jljJYKICAgIGhhbmRsZVN5c3RlbUNvZGVDaGFuZ2UodmFsKSB7CiAgICAgIGlmICh2YWwpIHsKICAgICAgICAvLyDlpoLmnpzlvIDlkK/ns7vnu5/nvJblj7fvvIzliJnnlJ/miJDnvJblj7cKICAgICAgICB0aGlzLmdlbmVyYXRlUGxhbkNvZGUoKTsKICAgICAgfSBlbHNlIHsKICAgICAgICAvLyDlpoLmnpzlhbPpl63ns7vnu5/nvJblj7fvvIzliJnmuIXnqbrnvJblj7cKICAgICAgICB0aGlzLmZvcm0ucGxhbkNvZGUgPSAnJzsKICAgICAgfQogICAgfSwKCiAgICAvLyDlpITnkIbmnaXmupDnsbvlnovlj5jljJYKICAgIGhhbmRsZVNvdXJjZVR5cGVDaGFuZ2UodmFsKSB7CiAgICAgIC8vIOa4heepuuiuouWNlee8luWPtwogICAgICB0aGlzLmZvcm0ub3JkZXJDb2RlID0gIiI7CiAgICAgIC8vIOWmguaenOmAieaLqeeUn+S6p+iuouWNle+8jOa4heepuuS6p+WTgeebuOWFs+S/oeaBrwogICAgICBpZiAodmFsID09PSAnUFJPRFVDVElPTl9PUkRFUicpIHsKICAgICAgICB0aGlzLmZvcm0ucHJvZHVjdElkID0gdW5kZWZpbmVkOwogICAgICAgIHRoaXMuZm9ybS5wcm9kdWN0TmFtZSA9ICIiOwogICAgICAgIHRoaXMuZm9ybS5wcm9kdWN0Q29kZSA9ICIiOwogICAgICAgIHRoaXMuZm9ybS5zcGVjaWZpY2F0aW9uID0gIiI7CiAgICAgICAgdGhpcy5mb3JtLnVuaXQgPSAiIjsKICAgICAgfQogICAgfSwKCiAgICAvLyDmiZPlvIDnlJ/kuqforqLljZXpgInmi6nlr7nor53moYYKICAgIG9wZW5Qcm9kdWN0aW9uT3JkZXJEaWFsb2coKSB7CiAgICAgIHRoaXMucHJvZHVjdGlvbk9yZGVyRGlhbG9nVmlzaWJsZSA9IHRydWU7CiAgICAgIHRoaXMuZ2V0UHJvZHVjdGlvbk9yZGVyTGlzdCgpOwogICAgfSwKCiAgICAvLyDojrflj5bnlJ/kuqforqLljZXliJfooagKICAgIGdldFByb2R1Y3Rpb25PcmRlckxpc3QoKSB7CiAgICAgIC8vIOi/memHjOiwg+eUqOeUn+S6p+iuouWNleWIl+ihqEFQSQogICAgICBpbXBvcnQoIkAvYXBpL3NjL3Byb2R1Y3Rpb25PcmRlciIpLnRoZW4oYXBpID0+IHsKICAgICAgICBhcGkubGlzdFByb2R1Y3Rpb25PcmRlcih7fSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICB0aGlzLnByb2R1Y3Rpb25PcmRlckxpc3QgPSByZXNwb25zZS5yb3dzIHx8IFtdOwogICAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPlueUn+S6p+iuouWNleWIl+ihqOWksei0pScpOwogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0sCgogICAgLy8g5aSE55CG55Sf5Lqn6K6i5Y2V6YCJ5oup5Y+Y5YyWCiAgICBoYW5kbGVQcm9kdWN0aW9uT3JkZXJTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuc2VsZWN0ZWRQcm9kdWN0aW9uT3JkZXIgPSBzZWxlY3Rpb24ubGVuZ3RoID4gMCA/IHNlbGVjdGlvblswXSA6IG51bGw7CiAgICB9LAoKICAgIC8vIOehruiupOmAieaLqeeUn+S6p+iuouWNlQogICAgY29uZmlybVByb2R1Y3Rpb25PcmRlclNlbGVjdCgpIHsKICAgICAgaWYgKCF0aGlzLnNlbGVjdGVkUHJvZHVjdGlvbk9yZGVyKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7fpgInmi6nkuIDkuKrnlJ/kuqforqLljZUnKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIC8vIOiuvue9ruiuouWNlee8luWPtwogICAgICB0aGlzLmZvcm0ub3JkZXJDb2RlID0gdGhpcy5zZWxlY3RlZFByb2R1Y3Rpb25PcmRlci5wcm9kT3JkZXJDb2RlOwoKICAgICAgLy8g6LCD55SoQVBJ5qC55o2u55Sf5Lqn6K6i5Y2V5Yib5bu66K6h5YiS5qih5p2/CiAgICAgIGltcG9ydCgiQC9hcGkvc2MvcHJvZHVjdGlvblBsYW4iKS50aGVuKGFwaSA9PiB7CiAgICAgICAgYXBpLmNyZWF0ZVBsYW5Gcm9tT3JkZXIodGhpcy5zZWxlY3RlZFByb2R1Y3Rpb25PcmRlci5wcm9kdWN0aW9uT3JkZXJJZCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7CiAgICAgICAgICAgIGNvbnN0IHBsYW5EYXRhID0gcmVzcG9uc2UuZGF0YTsKICAgICAgICAgICAgLy8g5aGr5YWF6KGo5Y2V5pWw5o2uCiAgICAgICAgICAgIHRoaXMuZm9ybS5wbGFuTmFtZSA9IHBsYW5EYXRhLnBsYW5OYW1lOwogICAgICAgICAgICB0aGlzLmZvcm0ucHJvZHVjdElkID0gcGxhbkRhdGEucHJvZHVjdElkOwogICAgICAgICAgICB0aGlzLmZvcm0ucGxhbm5lZFF0eSA9IHBsYW5EYXRhLnBsYW5uZWRRdHk7CiAgICAgICAgICAgIHRoaXMuZm9ybS5wbGFuU3RhcnRUaW1lID0gcGxhbkRhdGEucGxhblN0YXJ0VGltZTsKICAgICAgICAgICAgdGhpcy5mb3JtLnBsYW5FbmRUaW1lID0gcGxhbkRhdGEucGxhbkVuZFRpbWU7CiAgICAgICAgICAgIHRoaXMuZm9ybS5yZXF1aXJlZERhdGUgPSBwbGFuRGF0YS5yZXF1aXJlZERhdGU7CiAgICAgICAgICAgIHRoaXMuZm9ybS5yZW1hcmsgPSBwbGFuRGF0YS5yZW1hcms7CgogICAgICAgICAgICAvLyDlpoLmnpzmnInkuqflk4Hkv6Hmga/vvIzpnIDopoHojrflj5bkuqflk4Hor6bmg4UKICAgICAgICAgICAgaWYgKHBsYW5EYXRhLnByb2R1Y3RJZCkgewogICAgICAgICAgICAgIHRoaXMuZ2V0UHJvZHVjdERldGFpbHMocGxhbkRhdGEucHJvZHVjdElkKTsKICAgICAgICAgICAgfQoKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCflt7LlhbPogZTnlJ/kuqforqLljZXvvIzor7flrozlloTlhbbku5bkv6Hmga8nKTsKICAgICAgICAgIH0KICAgICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCflhbPogZTnlJ/kuqforqLljZXlpLHotKUnKTsKICAgICAgICB9KTsKICAgICAgfSk7CgogICAgICB0aGlzLnByb2R1Y3Rpb25PcmRlckRpYWxvZ1Zpc2libGUgPSBmYWxzZTsKICAgIH0sCgogICAgLy8g6I635Y+W5Lqn5ZOB6K+m5oOFCiAgICBnZXRQcm9kdWN0RGV0YWlscyhwcm9kdWN0SWQpIHsKICAgICAgbGlzdFByb2R1Y3RzKHsgcHJvZHVjdElkOiBwcm9kdWN0SWQgfSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgaWYgKHJlc3BvbnNlLnJvd3MgJiYgcmVzcG9uc2Uucm93cy5sZW5ndGggPiAwKSB7CiAgICAgICAgICBjb25zdCBwcm9kdWN0ID0gcmVzcG9uc2Uucm93c1swXTsKICAgICAgICAgIHRoaXMuZm9ybS5wcm9kdWN0TmFtZSA9IHByb2R1Y3QucHJvZHVjdF9uYW1lOwogICAgICAgICAgdGhpcy5mb3JtLnByb2R1Y3RDb2RlID0gcHJvZHVjdC5wcm9kdWN0X2NvZGU7CiAgICAgICAgICB0aGlzLmZvcm0uc3BlY2lmaWNhdGlvbiA9IHByb2R1Y3QucHJvZHVjdF9zZm47CiAgICAgICAgICB0aGlzLmZvcm0udW5pdCA9IHByb2R1Y3QucHJvZHVjdF91bml0OwogICAgICAgIH0KICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluS6p+WTgeivpuaDheWksei0pScpOwogICAgICB9KTsKICAgIH0sCiAgICAKICAgIC8vIOinpuWPkeS4iuS8oAogICAgdHJpZ2dlclVwbG9hZCgpIHsKICAgICAgdGhpcy4kcmVmcy51cGxvYWQuJGVsLmNsaWNrKCk7CiAgICB9LAogICAgCiAgICAvLyDmiZPlvIDkuqflk4HpgInmi6nlvLnnqpcKICAgIG9wZW5Qcm9kdWN0U2VsZWN0aW9uKCkgewogICAgICBpZiAodGhpcy5mb3JtLnNvdXJjZVR5cGUgPT09ICdQUk9EVUNUSU9OX09SREVSJykgewogICAgICAgIC8vIOWmguaenOmAieaLqeS6hueUn+S6p+iuouWNle+8jOS9huayoeaciemAieaLqeWFt+S9k+iuouWNlQogICAgICAgIGlmICghdGhpcy5mb3JtLm9yZGVyQ29kZSkgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7flhYjpgInmi6nnlJ/kuqforqLljZUnKTsKICAgICAgICAgIHJldHVybjsKICAgICAgICB9CiAgICAgICAgLy8g5pi+56S66K+l6K6i5Y2V55qE5Lqn5ZOB5YiX6KGoCiAgICAgICAgdGhpcy5nZXRQcm9kdWN0c0J5T3JkZXIoKTsKICAgICAgfSBlbHNlIHsKICAgICAgICAvLyDlhbbku5bmg4XlhrXmmL7npLrlhajpg6jkuqflk4EKICAgICAgICB0aGlzLmdldFByb2R1Y3RMaXN0KCk7CiAgICAgIH0KICAgICAgdGhpcy5wcm9kdWN0RGlhbG9nVmlzaWJsZSA9IHRydWU7CiAgICB9LAogICAgCiAgICAvLyDojrflj5bkuqflk4HliJfooagKICAgIGdldFByb2R1Y3RMaXN0KCkgewogICAgICB0aGlzLnByb2R1Y3RMb2FkaW5nID0gdHJ1ZTsKICAgICAgbGlzdFByb2R1Y3RzKHsKICAgICAgICBwYWdlTnVtOiB0aGlzLnByb2R1Y3RRdWVyeS5wYWdlTnVtLAogICAgICAgIHBhZ2VTaXplOiB0aGlzLnByb2R1Y3RRdWVyeS5wYWdlU2l6ZSwKICAgICAgICBrZXl3b3JkOiB0aGlzLnByb2R1Y3RRdWVyeS5rZXl3b3JkLAogICAgICAgIHByb2R1Y3RVbml0OiB0aGlzLnByb2R1Y3RRdWVyeS51bml0LAogICAgICAgIHByb2R1Y3RUeXBlOiB0aGlzLnByb2R1Y3RRdWVyeS50eXBlLAogICAgICAgIHByb2R1Y3RQcm9wZXJ0eTogdGhpcy5wcm9kdWN0UXVlcnkucHJvcGVydHkKICAgICAgfSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5wcm9kdWN0TG9hZGluZyA9IGZhbHNlOwogICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsKICAgICAgICAgIHRoaXMucHJvZHVjdExpc3QgPSByZXNwb25zZS5yb3dzOwogICAgICAgICAgdGhpcy5wcm9kdWN0VG90YWwgPSByZXNwb25zZS50b3RhbDsKICAgICAgICB9CiAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICB0aGlzLnByb2R1Y3RMb2FkaW5nID0gZmFsc2U7CiAgICAgICAgLy8g5qih5ouf5pWw5o2uCiAgICAgICAgdGhpcy5wcm9kdWN0TGlzdCA9IFsKICAgICAgICAgIHsgcHJvZHVjdF9pZDogMSwgcHJvZHVjdF9jb2RlOiAnQ1AwMjUnLCBwcm9kdWN0X25hbWU6ICfkuqflk4HkuIAnLCBwcm9kdWN0X3NmbjogJ+iTneiJsicsIHByb2R1Y3RfdHlwZTogJ+aIkOWTgScsIHByb2R1Y3RfdW5pdDogJ+S4qicsIHByb2R1Y3RfcHJvcGVydHk6ICfoh6rliLYnIH0sCiAgICAgICAgICB7IHByb2R1Y3RfaWQ6IDIsIHByb2R1Y3RfY29kZTogJ0NQMDI1JywgcHJvZHVjdF9uYW1lOiAn5Lqn5ZOB5LiAJywgcHJvZHVjdF9zZm46ICfok53oibInLCBwcm9kdWN0X3R5cGU6ICfmiJDlk4EnLCBwcm9kdWN0X3VuaXQ6ICfkuKonLCBwcm9kdWN0X3Byb3BlcnR5OiAn6Ieq5Yi2JyB9LAogICAgICAgICAgeyBwcm9kdWN0X2lkOiAzLCBwcm9kdWN0X2NvZGU6ICdDUDAyNScsIHByb2R1Y3RfbmFtZTogJ+S6p+WTgeS4gCcsIHByb2R1Y3Rfc2ZuOiAn6JOd6ImyJywgcHJvZHVjdF90eXBlOiAn5oiQ5ZOBJywgcHJvZHVjdF91bml0OiAn5LiqJywgcHJvZHVjdF9wcm9wZXJ0eTogJ+iHquWIticgfSwKICAgICAgICAgIHsgcHJvZHVjdF9pZDogNCwgcHJvZHVjdF9jb2RlOiAnQ1AwMjUnLCBwcm9kdWN0X25hbWU6ICfkuqflk4HkuIAnLCBwcm9kdWN0X3NmbjogJ+iTneiJsicsIHByb2R1Y3RfdHlwZTogJ+aIkOWTgScsIHByb2R1Y3RfdW5pdDogJ+S4qicsIHByb2R1Y3RfcHJvcGVydHk6ICfoh6rliLYnIH0sCiAgICAgICAgICB7IHByb2R1Y3RfaWQ6IDUsIHByb2R1Y3RfY29kZTogJ0NQMDI1JywgcHJvZHVjdF9uYW1lOiAn5Lqn5ZOB5LiAJywgcHJvZHVjdF9zZm46ICfok53oibInLCBwcm9kdWN0X3R5cGU6ICfmiJDlk4EnLCBwcm9kdWN0X3VuaXQ6ICfkuKonLCBwcm9kdWN0X3Byb3BlcnR5OiAn6Ieq5Yi2JyB9CiAgICAgICAgXTsKICAgICAgICB0aGlzLnByb2R1Y3RUb3RhbCA9IDUwOwogICAgICB9KTsKICAgIH0sCgogICAgLy8g5qC55o2u55Sf5Lqn6K6i5Y2V6I635Y+W5Lqn5ZOB5YiX6KGoCiAgICBnZXRQcm9kdWN0c0J5T3JkZXIoKSB7CiAgICAgIGlmICghdGhpcy5zZWxlY3RlZFByb2R1Y3Rpb25PcmRlcikgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+acquaJvuWIsOmAieS4reeahOeUn+S6p+iuouWNleS/oeaBrycpOwogICAgICAgIHJldHVybjsKICAgICAgfQoKICAgICAgdGhpcy5wcm9kdWN0TG9hZGluZyA9IHRydWU7CiAgICAgIC8vIOiwg+eUqEFQSeiOt+WPluiuouWNleS6p+WTgeaYjue7hgogICAgICBpbXBvcnQoIkAvYXBpL3NjL3Byb2R1Y3Rpb25PcmRlciIpLnRoZW4oYXBpID0+IHsKICAgICAgICBhcGkuZ2V0UHJvZHVjdGlvbk9yZGVyRGV0YWlscyh0aGlzLnNlbGVjdGVkUHJvZHVjdGlvbk9yZGVyLnByb2R1Y3Rpb25PcmRlcklkKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDAgJiYgcmVzcG9uc2UuZGF0YSAmJiByZXNwb25zZS5kYXRhLnByb2R1Y3RzKSB7CiAgICAgICAgICAgIC8vIOWwhuiuouWNleaYjue7hui9rOaNouS4uuS6p+WTgeWIl+ihqOagvOW8jwogICAgICAgICAgICB0aGlzLnByb2R1Y3RMaXN0ID0gcmVzcG9uc2UuZGF0YS5wcm9kdWN0cy5tYXAoZGV0YWlsID0+ICh7CiAgICAgICAgICAgICAgcHJvZHVjdF9pZDogZGV0YWlsLnByb2R1Y3RJZCwKICAgICAgICAgICAgICBwcm9kdWN0X25hbWU6IGRldGFpbC5wcm9kdWN0TmFtZSwKICAgICAgICAgICAgICBwcm9kdWN0X2NvZGU6IGRldGFpbC5wcm9kdWN0Q29kZSwKICAgICAgICAgICAgICBwcm9kdWN0X3NmbjogZGV0YWlsLnByb2R1Y3RTZm4sCiAgICAgICAgICAgICAgcHJvZHVjdF91bml0OiBkZXRhaWwucHJvZHVjdFVuaXQsCiAgICAgICAgICAgICAgLy8g5re75Yqg6K6i5Y2V55u45YWz5L+h5oGvCiAgICAgICAgICAgICAgb3JkZXJfcXR5OiBkZXRhaWwucXR5TnVtLAogICAgICAgICAgICAgIGRlbGl2ZXJ5X2RhdGU6IGRldGFpbC5kZWxpdmVyeURhdGUKICAgICAgICAgICAgfSkpOwogICAgICAgICAgICB0aGlzLnByb2R1Y3RUb3RhbCA9IHRoaXMucHJvZHVjdExpc3QubGVuZ3RoOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor6XorqLljZXmmoLml6Dkuqflk4HmmI7nu4YnKTsKICAgICAgICAgICAgdGhpcy5wcm9kdWN0TGlzdCA9IFtdOwogICAgICAgICAgICB0aGlzLnByb2R1Y3RUb3RhbCA9IDA7CiAgICAgICAgICB9CiAgICAgICAgICB0aGlzLnByb2R1Y3RMb2FkaW5nID0gZmFsc2U7CiAgICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6I635Y+W6K6i5Y2V5Lqn5ZOB5YiX6KGo5aSx6LSlJyk7CiAgICAgICAgICB0aGlzLnByb2R1Y3RMb2FkaW5nID0gZmFsc2U7CiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgfSwKCiAgICAvLyDmkJzntKLkuqflk4EKICAgIHNlYXJjaFByb2R1Y3RzKCkgewogICAgICB0aGlzLnByb2R1Y3RRdWVyeS5wYWdlTnVtID0gMTsKICAgICAgdGhpcy5nZXRQcm9kdWN0TGlzdCgpOwogICAgfSwKICAgIAogICAgLy8g6YeN572u5Lqn5ZOB5p+l6K+i5p2h5Lu2CiAgICByZXNldFByb2R1Y3RRdWVyeSgpIHsKICAgICAgdGhpcy5wcm9kdWN0UXVlcnkgPSB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAga2V5d29yZDogIiIsCiAgICAgICAgdW5pdDogIiIsCiAgICAgICAgdHlwZTogIiIsCiAgICAgICAgcHJvcGVydHk6ICIiCiAgICAgIH07CiAgICAgIHRoaXMuZ2V0UHJvZHVjdExpc3QoKTsKICAgIH0sCiAgICAKICAgIC8vIOWkhOeQhuS6p+WTgeihqOagvOmAieaLqeWPmOWMlgogICAgaGFuZGxlUHJvZHVjdFNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgaWYgKHNlbGVjdGlvbi5sZW5ndGggPiAwKSB7CiAgICAgICAgdGhpcy5zZWxlY3RlZFByb2R1Y3QgPSBzZWxlY3Rpb25bMF07CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5zZWxlY3RlZFByb2R1Y3QgPSBudWxsOwogICAgICB9CiAgICB9LAogICAgCiAgICAvLyDlpITnkIbkuqflk4HpobXnoIHlj5jljJYKICAgIGhhbmRsZVByb2R1Y3RDdXJyZW50Q2hhbmdlKGN1cnJlbnRQYWdlKSB7CiAgICAgIHRoaXMucHJvZHVjdFF1ZXJ5LnBhZ2VOdW0gPSBjdXJyZW50UGFnZTsKICAgICAgdGhpcy5nZXRQcm9kdWN0TGlzdCgpOwogICAgfSwKICAgIAogICAgLy8g5aSE55CG5Lqn5ZOB5q+P6aG15p2h5pWw5Y+Y5YyWCiAgICBoYW5kbGVQcm9kdWN0U2l6ZUNoYW5nZShzaXplKSB7CiAgICAgIHRoaXMucHJvZHVjdFF1ZXJ5LnBhZ2VTaXplID0gc2l6ZTsKICAgICAgdGhpcy5wcm9kdWN0UXVlcnkucGFnZU51bSA9IDE7CiAgICAgIHRoaXMuZ2V0UHJvZHVjdExpc3QoKTsKICAgIH0sCiAgICAKICAgIC8vIOehruiupOS6p+WTgemAieaLqQogICAgY29uZmlybVByb2R1Y3RTZWxlY3QoKSB7CiAgICAgIGlmICh0aGlzLnNlbGVjdGVkUHJvZHVjdCkgewogICAgICAgIHRoaXMuZm9ybS5wcm9kdWN0SWQgPSB0aGlzLnNlbGVjdGVkUHJvZHVjdC5wcm9kdWN0X2lkOwogICAgICAgIHRoaXMuZm9ybS5wcm9kdWN0TmFtZSA9IHRoaXMuc2VsZWN0ZWRQcm9kdWN0LnByb2R1Y3RfbmFtZTsKICAgICAgICB0aGlzLmZvcm0ucHJvZHVjdENvZGUgPSB0aGlzLnNlbGVjdGVkUHJvZHVjdC5wcm9kdWN0X2NvZGU7CiAgICAgICAgdGhpcy5mb3JtLnNwZWNpZmljYXRpb24gPSB0aGlzLnNlbGVjdGVkUHJvZHVjdC5wcm9kdWN0X3NmbjsKICAgICAgICB0aGlzLmZvcm0udW5pdCA9IHRoaXMuc2VsZWN0ZWRQcm9kdWN0LnByb2R1Y3RfdW5pdDsKCiAgICAgICAgLy8g5aaC5p6c5piv5LuO55Sf5Lqn6K6i5Y2V6YCJ5oup55qE5Lqn5ZOB77yM6Ieq5Yqo5aGr5YWF6K6i5Y2V5pWw6YeP5ZKM5Lqk5LuY5pel5pyfCiAgICAgICAgaWYgKHRoaXMuZm9ybS5zb3VyY2VUeXBlID09PSAnUFJPRFVDVElPTl9PUkRFUicgJiYgdGhpcy5zZWxlY3RlZFByb2R1Y3Qub3JkZXJfcXR5KSB7CiAgICAgICAgICB0aGlzLmZvcm0ucGxhbm5lZFF0eSA9IHRoaXMuc2VsZWN0ZWRQcm9kdWN0Lm9yZGVyX3F0eTsKICAgICAgICAgIGlmICh0aGlzLnNlbGVjdGVkUHJvZHVjdC5kZWxpdmVyeV9kYXRlKSB7CiAgICAgICAgICAgIHRoaXMuZm9ybS5yZXF1aXJlZERhdGUgPSB0aGlzLnNlbGVjdGVkUHJvZHVjdC5kZWxpdmVyeV9kYXRlOwogICAgICAgICAgICB0aGlzLmZvcm0ucGxhbkVuZFRpbWUgPSB0aGlzLnNlbGVjdGVkUHJvZHVjdC5kZWxpdmVyeV9kYXRlOwogICAgICAgICAgfQogICAgICAgIH0KCiAgICAgICAgdGhpcy5wcm9kdWN0RGlhbG9nVmlzaWJsZSA9IGZhbHNlOwoKICAgICAgICAvLyDmuIXnqbrlt7LpgIlCT00KICAgICAgICB0aGlzLnNlbGVjdGVkQm9tID0gbnVsbDsKICAgICAgICB0aGlzLnNlbGVjdGVkQm9tSWQgPSBudWxsOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+36YCJ5oup5LiA5Liq5Lqn5ZOB77yBJyk7CiAgICAgIH0KICAgIH0sCiAgICAKICAgIC8vIOmAieaLqUJPTQogICAgc2VsZWN0Qm9tKCkgewogICAgICBpZiAoIXRoaXMuZm9ybS5wcm9kdWN0SWQpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+WFiOmAieaLqeaIkOWTge+8gScpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICB0aGlzLmJvbURpYWxvZ1Zpc2libGUgPSB0cnVlOwogICAgICB0aGlzLmdldEJvbUxpc3QoKTsKICAgIH0sCiAgICAKICAgIC8vIOiOt+WPlkJPTeWIl+ihqAogICAgZ2V0Qm9tTGlzdCgpIHsKICAgICAgdGhpcy5ib21Mb2FkaW5nID0gdHJ1ZTsKICAgICAgbGlzdEJvbXNCeVByb2R1Y3RJZCh0aGlzLmZvcm0ucHJvZHVjdElkKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmJvbUxvYWRpbmcgPSBmYWxzZTsKICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7CiAgICAgICAgICB0aGlzLmJvbUxpc3QgPSByZXNwb25zZS5yb3dzOwogICAgICAgICAgdGhpcy5ib21Ub3RhbCA9IHJlc3BvbnNlLnRvdGFsOwogICAgICAgICAgaWYgKCF0aGlzLmJvbUxpc3QgfHwgdGhpcy5ib21MaXN0Lmxlbmd0aCA9PT0gMCkgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmluZm8oIuacquaJvuWIsOivpeS6p+WTgeeahEJPTeS/oeaBryIpOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgLy8g5aaC5p6c5pyJ6buY6K6kQk9N77yM5YiZ6Ieq5Yqo6YCJ5LitCiAgICAgICAgICAgIGNvbnN0IGRlZmF1bHRCb20gPSB0aGlzLmJvbUxpc3QuZmluZChiID0+IGIuYm9tX3N0YXR1cyA9PT0gJzEnKTsKICAgICAgICAgICAgaWYgKGRlZmF1bHRCb20pIHsKICAgICAgICAgICAgICB0aGlzLmhhbmRsZUJvbVNlbGVjdChkZWZhdWx0Qm9tKTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLmJvbUxpc3QgPSBbXTsKICAgICAgICAgIHRoaXMuYm9tVG90YWwgPSAwOwogICAgICAgIH0KICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgIHRoaXMuYm9tTG9hZGluZyA9IGZhbHNlOwogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPlkJPTeWIl+ihqOWksei0pScpOwogICAgICB9KTsKICAgIH0sCiAgICAKICAgIC8vIOWkhOeQhkJPTeihjOmAieaLqQogICAgaGFuZGxlQm9tU2VsZWN0KHJvdykgewogICAgICB0aGlzLnNlbGVjdGVkQm9tID0gcm93OwogICAgICB0aGlzLnNlbGVjdGVkQm9tSWQgPSByb3cuYm9tX2lkOwogICAgfSwKICAgIAogICAgLy8g56Gu6K6kQk9N6YCJ5oupCiAgICBjb25maXJtQm9tU2VsZWN0KCkgewogICAgICBpZiAodGhpcy5zZWxlY3RlZEJvbSkgewogICAgICAgIHRoaXMuYm9tRGlhbG9nVmlzaWJsZSA9IGZhbHNlOwogICAgICAgIC8vIOiOt+WPlkJPTeivpuaDhQogICAgICAgIHRoaXMuZ2V0Qm9tRGV0YWlsKCk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7fpgInmi6nkuIDkuKpCT03vvIEnKTsKICAgICAgfQogICAgfSwKICAgIAogICAgLy8g6I635Y+WQk9N6K+m5oOFCiAgICBnZXRCb21EZXRhaWwoKSB7CiAgICAgIGZpbmRCb21EZXRhaWxzKHRoaXMuc2VsZWN0ZWRCb20uYm9tX2lkKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICBjb25zb2xlLmxvZygi5oiQ5Yqf6I635Y+WQk9N6K+m5oOF5ZON5bqUOiIsIHJlc3BvbnNlKTsKICAgICAgICBpZiAocmVzcG9uc2UgJiYgcmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7CiAgICAgICAgICB0aGlzLmJvbURldGFpbExpc3QgPSByZXNwb25zZS5yb3dzOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLmJvbURldGFpbExpc3QgPSBbXTsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuiOt+WPlkJPTeivpuaDheWksei0pTogIiArIChyZXNwb25zZSA/IHJlc3BvbnNlLm1zZyA6ICfml6Dlk43lupQnKSk7CiAgICAgICAgfQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7CiAgICAgICAgY29uc29sZS5lcnJvcigi6I635Y+WQk9N6K+m5oOF5o6l5Y+j6LCD55So5aSx6LSlOiIsIGVycm9yKTsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLojrflj5ZCT03or6bmg4XmjqXlj6PosIPnlKjlpLHotKUiKTsKICAgICAgICB0aGlzLmJvbURldGFpbExpc3QgPSBbXTsKICAgICAgfSk7CiAgICB9LAogICAgCiAgICAvLyDmuIXpmaTlt7LpgIlCT00KICAgIGNsZWFyU2VsZWN0ZWRCb20oKSB7CiAgICAgIHRoaXMuc2VsZWN0ZWRCb20gPSBudWxsOwogICAgICB0aGlzLnNlbGVjdGVkQm9tSWQgPSBudWxsOwogICAgICB0aGlzLmJvbURldGFpbExpc3QgPSBbXTsKICAgIH0sCiAgICAKICAgIC8vIOS4iuS8oOWJjeajgOafpeaWh+S7tuexu+Wei+WSjOWkp+WwjwogICAgYmVmb3JlVXBsb2FkKGZpbGUpIHsKICAgICAgY29uc3QgaXNWYWxpZFR5cGUgPSAvXC4oZG9jfGRvY3h8eGxzfHhsc3h8cGRmfHJhcnx6aXB8cG5nfGpwZ3xqcGVnKSQvaS50ZXN0KGZpbGUubmFtZSk7CiAgICAgIGNvbnN0IGlzTHQxME0gPSBmaWxlLnNpemUgLyAxMDI0IC8gMTAyNCA8IDEwOwoKICAgICAgaWYgKCFpc1ZhbGlkVHlwZSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+S4iuS8oOaWh+S7tuagvOW8j+S4jeaUr+aMgSEnKTsKICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgIH0KICAgICAgaWYgKCFpc0x0MTBNKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5LiK5Lyg5paH5Lu25aSn5bCP5LiN6IO96LaF6L+HIDEwTUIhJyk7CiAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICB9CiAgICAgIHJldHVybiB0cnVlOwogICAgfSwKICAgIAogICAgLy8g5LiK5Lyg5paH5Lu25aSE55CGCiAgICB1cGxvYWRGaWxlKG9wdGlvbnMpIHsKICAgICAgLy8g6L+Z6YeM5bqU6K+l6LCD55So5a6e6ZmF55qE5paH5Lu25LiK5LygQVBJCiAgICAgIGNvbnNvbGUubG9nKCfmlofku7bkuIrkvKA6Jywgb3B0aW9ucy5maWxlKTsKICAgICAgLy8g5YGH6K6+5LiK5Lyg5oiQ5YqfCiAgICAgIHRoaXMuZmlsZUxpc3QucHVzaCh7CiAgICAgICAgbmFtZTogb3B0aW9ucy5maWxlLm5hbWUsCiAgICAgICAgdXJsOiBVUkwuY3JlYXRlT2JqZWN0VVJMKG9wdGlvbnMuZmlsZSkKICAgICAgfSk7CiAgICAgIG9wdGlvbnMub25TdWNjZXNzKCk7CiAgICB9LAogICAgCiAgICAvLyDnp7vpmaTmlofku7YKICAgIGhhbmRsZVJlbW92ZShmaWxlKSB7CiAgICAgIGNvbnN0IGluZGV4ID0gdGhpcy5maWxlTGlzdC5pbmRleE9mKGZpbGUpOwogICAgICBpZiAoaW5kZXggIT09IC0xKSB7CiAgICAgICAgdGhpcy5maWxlTGlzdC5zcGxpY2UoaW5kZXgsIDEpOwogICAgICB9CiAgICB9LAogICAgCiAgICAvLyDooajljZXmj5DkuqQKICAgIHN1Ym1pdEZvcm0oKSB7CiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICAvLyDpop3lpJbnmoTml6XmnJ/pgLvovpHpqozor4EKICAgICAgICAgIGlmICghdGhpcy52YWxpZGF0ZURhdGVMb2dpYygpKSB7CiAgICAgICAgICAgIHJldHVybjsKICAgICAgICAgIH0KCiAgICAgICAgICAvLyDooajljZXpqozor4HpgJrov4fvvIzosIPnlKhBUEnmj5DkuqTmlbDmja4KICAgICAgICAgIGNvbnN0IGRhdGEgPSB7CiAgICAgICAgICAgIHBsYW5Db2RlOiB0aGlzLmZvcm0ucGxhbkNvZGUsCiAgICAgICAgICAgIHBsYW5OYW1lOiB0aGlzLmZvcm0ucGxhbk5hbWUsCiAgICAgICAgICAgIHNvdXJjZVR5cGU6IHRoaXMuZm9ybS5zb3VyY2VUeXBlLAogICAgICAgICAgICBvcmRlckNvZGU6IHRoaXMuZm9ybS5vcmRlckNvZGUsCiAgICAgICAgICAgIHBsYW5TdGFydFRpbWU6IHRoaXMuZm9ybS5wbGFuU3RhcnRUaW1lLAogICAgICAgICAgICBwbGFuRW5kVGltZTogdGhpcy5mb3JtLnBsYW5FbmRUaW1lLAogICAgICAgICAgICByZXF1aXJlZERhdGU6IHRoaXMuZm9ybS5yZXF1aXJlZERhdGUsCiAgICAgICAgICAgIHJlbWFyazogdGhpcy5mb3JtLnJlbWFyaywKICAgICAgICAgICAgcHJvZHVjdElkOiB0aGlzLmZvcm0ucHJvZHVjdElkLAogICAgICAgICAgICBwbGFubmVkUXR5OiB0aGlzLmZvcm0ucGxhbm5lZFF0eQogICAgICAgICAgfTsKICAgICAgICAgIAogICAgICAgICAgYWRkUHJvZHVjdGlvblBsYW4oZGF0YSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsKICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKTsKICAgICAgICAgICAgICB0aGlzLmNhbmNlbCgpOwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKHJlc3BvbnNlLm1zZyB8fCAi5paw5aKe5aSx6LSlIik7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICAgICAgLy8g5qih5ouf5oiQ5Yqf5ZON5bqUCiAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOwogICAgICAgICAgICB0aGlzLmNhbmNlbCgpOwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAKICAgIC8vIOWPlua2iOaMiemSrgogICAgY2FuY2VsKCkgewogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7IHBhdGg6ICIvc2MvcGxhbiIgfSk7CiAgICB9LAoKICAgIC8vIOmqjOivgeW8gOW3peaXtumXtAogICAgdmFsaWRhdGVTdGFydFRpbWUocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSB7CiAgICAgIGlmICghdmFsdWUpIHsKICAgICAgICBjYWxsYmFjaygpOwogICAgICAgIHJldHVybjsKICAgICAgfQoKICAgICAgY29uc3QgdG9kYXkgPSBuZXcgRGF0ZSgpOwogICAgICB0b2RheS5zZXRIb3VycygwLCAwLCAwLCAwKTsKICAgICAgY29uc3Qgc3RhcnREYXRlID0gbmV3IERhdGUodmFsdWUpOwoKICAgICAgLy8g5byA5bel5pel5pyf5LiN6IO95pep5LqO5b2T5YmN5pel5pyfCiAgICAgIGlmIChzdGFydERhdGUgPCB0b2RheSkgewogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcign5byA5bel5pel5pyf5LiN6IO95pep5LqO5b2T5YmN5pel5pyfJykpOwogICAgICAgIHJldHVybjsKICAgICAgfQoKICAgICAgLy8g5aaC5p6c5a6M5bel5pe26Ze05bey6YCJ5oup77yM5byA5bel5pe26Ze05LiN6IO95pma5LqO5a6M5bel5pe26Ze0CiAgICAgIGlmICh0aGlzLmZvcm0ucGxhbkVuZFRpbWUpIHsKICAgICAgICBjb25zdCBlbmREYXRlID0gbmV3IERhdGUodGhpcy5mb3JtLnBsYW5FbmRUaW1lKTsKICAgICAgICBpZiAoc3RhcnREYXRlID4gZW5kRGF0ZSkgewogICAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCflvIDlt6Xml6XmnJ/kuI3og73mmZrkuo7lrozlt6Xml6XmnJ8nKSk7CiAgICAgICAgICByZXR1cm47CiAgICAgICAgfQogICAgICB9CgogICAgICBjYWxsYmFjaygpOwogICAgfSwKCiAgICAvLyDpqozor4Hlrozlt6Xml7bpl7QKICAgIHZhbGlkYXRlRW5kVGltZShydWxlLCB2YWx1ZSwgY2FsbGJhY2spIHsKICAgICAgaWYgKCF2YWx1ZSkgewogICAgICAgIGNhbGxiYWNrKCk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CgogICAgICBjb25zdCBlbmREYXRlID0gbmV3IERhdGUodmFsdWUpOwoKICAgICAgLy8g5aaC5p6c5byA5bel5pe26Ze05bey6YCJ5oup77yM5a6M5bel5pe26Ze05LiN6IO95pep5LqO5byA5bel5pe26Ze0CiAgICAgIGlmICh0aGlzLmZvcm0ucGxhblN0YXJ0VGltZSkgewogICAgICAgIGNvbnN0IHN0YXJ0RGF0ZSA9IG5ldyBEYXRlKHRoaXMuZm9ybS5wbGFuU3RhcnRUaW1lKTsKICAgICAgICBpZiAoZW5kRGF0ZSA8IHN0YXJ0RGF0ZSkgewogICAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCflrozlt6Xml6XmnJ/kuI3og73ml6nkuo7lvIDlt6Xml6XmnJ8nKSk7CiAgICAgICAgICByZXR1cm47CiAgICAgICAgfQogICAgICB9CgogICAgICBjYWxsYmFjaygpOwogICAgfSwKCiAgICAvLyDpqozor4HpnIDmsYLml6XmnJ8KICAgIHZhbGlkYXRlUmVxdWlyZWREYXRlKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgewogICAgICBpZiAoIXZhbHVlKSB7CiAgICAgICAgY2FsbGJhY2soKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIGNvbnN0IHJlcXVpcmVkRGF0ZSA9IG5ldyBEYXRlKHZhbHVlKTsKCiAgICAgIC8vIOmcgOaxguaXpeacn+S4jeiDveaXqeS6juW9k+WJjeaXpeacnwogICAgICBjb25zdCB0b2RheSA9IG5ldyBEYXRlKCk7CiAgICAgIHRvZGF5LnNldEhvdXJzKDAsIDAsIDAsIDApOwogICAgICBpZiAocmVxdWlyZWREYXRlIDwgdG9kYXkpIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoJ+mcgOaxguaXpeacn+S4jeiDveaXqeS6juW9k+WJjeaXpeacnycpKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIC8vIOWmguaenOWujOW3peaXtumXtOW3sumAieaLqe+8jOmcgOaxguaXpeacn+S4jeiDveaXqeS6juWujOW3peaXtumXtAogICAgICBpZiAodGhpcy5mb3JtLnBsYW5FbmRUaW1lKSB7CiAgICAgICAgY29uc3QgZW5kRGF0ZSA9IG5ldyBEYXRlKHRoaXMuZm9ybS5wbGFuRW5kVGltZSk7CiAgICAgICAgaWYgKHJlcXVpcmVkRGF0ZSA8IGVuZERhdGUpIHsKICAgICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcign6ZyA5rGC5pel5pyf5LiN6IO95pep5LqO5a6M5bel5pel5pyfJykpOwogICAgICAgICAgcmV0dXJuOwogICAgICAgIH0KICAgICAgfQoKICAgICAgY2FsbGJhY2soKTsKICAgIH0sCgogICAgLy8g57u85ZCI5pel5pyf6YC76L6R6aqM6K+BCiAgICB2YWxpZGF0ZURhdGVMb2dpYygpIHsKICAgICAgY29uc3QgdG9kYXkgPSBuZXcgRGF0ZSgpOwogICAgICB0b2RheS5zZXRIb3VycygwLCAwLCAwLCAwKTsKCiAgICAgIC8vIOajgOafpeW8gOW3peaXpeacnwogICAgICBpZiAodGhpcy5mb3JtLnBsYW5TdGFydFRpbWUpIHsKICAgICAgICBjb25zdCBzdGFydERhdGUgPSBuZXcgRGF0ZSh0aGlzLmZvcm0ucGxhblN0YXJ0VGltZSk7CiAgICAgICAgaWYgKHN0YXJ0RGF0ZSA8IHRvZGF5KSB7CiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi5byA5bel5pel5pyf5LiN6IO95pep5LqO5b2T5YmN5pel5pyfIik7CiAgICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgICAgfQogICAgICB9CgogICAgICAvLyDmo4Dmn6XlvIDlt6Xml6XmnJ/lkozlrozlt6Xml6XmnJ/nmoTlhbPns7sKICAgICAgaWYgKHRoaXMuZm9ybS5wbGFuU3RhcnRUaW1lICYmIHRoaXMuZm9ybS5wbGFuRW5kVGltZSkgewogICAgICAgIGNvbnN0IHN0YXJ0RGF0ZSA9IG5ldyBEYXRlKHRoaXMuZm9ybS5wbGFuU3RhcnRUaW1lKTsKICAgICAgICBjb25zdCBlbmREYXRlID0gbmV3IERhdGUodGhpcy5mb3JtLnBsYW5FbmRUaW1lKTsKICAgICAgICBpZiAoc3RhcnREYXRlID4gZW5kRGF0ZSkgewogICAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuW8gOW3peaXpeacn+S4jeiDveaZmuS6juWujOW3peaXpeacnyIpOwogICAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICAgIH0KICAgICAgfQoKICAgICAgLy8g5qOA5p+l6ZyA5rGC5pel5pyfCiAgICAgIGlmICh0aGlzLmZvcm0ucmVxdWlyZWREYXRlKSB7CiAgICAgICAgY29uc3QgcmVxdWlyZWREYXRlID0gbmV3IERhdGUodGhpcy5mb3JtLnJlcXVpcmVkRGF0ZSk7CiAgICAgICAgaWYgKHJlcXVpcmVkRGF0ZSA8IHRvZGF5KSB7CiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi6ZyA5rGC5pel5pyf5LiN6IO95pep5LqO5b2T5YmN5pel5pyfIik7CiAgICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgICAgfQoKICAgICAgICAvLyDpnIDmsYLml6XmnJ/kuI3og73ml6nkuo7lrozlt6Xml6XmnJ8KICAgICAgICBpZiAodGhpcy5mb3JtLnBsYW5FbmRUaW1lKSB7CiAgICAgICAgICBjb25zdCBlbmREYXRlID0gbmV3IERhdGUodGhpcy5mb3JtLnBsYW5FbmRUaW1lKTsKICAgICAgICAgIGlmIChyZXF1aXJlZERhdGUgPCBlbmREYXRlKSB7CiAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLpnIDmsYLml6XmnJ/kuI3og73ml6nkuo7lrozlt6Xml6XmnJ8iKTsKICAgICAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQoKICAgICAgcmV0dXJuIHRydWU7CiAgICB9CiAgfQp9Owo="}, {"version": 3, "sources": ["add_plan.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0ZA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "add_plan.vue", "sourceRoot": "src/views/sc/plan", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <div class=\"form-container\">\n      <!-- 基础信息区 -->\n      <el-tabs type=\"border-card\">\n        <el-tab-pane>\n          <span slot=\"label\"><i class=\"el-icon-date\"></i> 基础信息</span>\n          <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\n            <el-row :gutter=\"20\">\n              <el-col :span=\"8\">\n                <el-form-item label=\"计划编号\" prop=\"planCode\" required>\n                  <el-input v-model=\"form.planCode\" placeholder=\"请输入\" :disabled=\"isSystemCode\"></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"4\">\n                <el-switch\n                  v-model=\"isSystemCode\"\n                  active-text=\"系统编号\"\n                  inactive-text=\"\"\n                  style=\"margin-top: 13px;\"\n                  @change=\"handleSystemCodeChange\"\n                ></el-switch>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"计划名称\" prop=\"planName\" required>\n                  <el-input v-model=\"form.planName\" placeholder=\"请输入\"></el-input>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-row :gutter=\"20\">\n              <el-col :span=\"12\">\n                <el-form-item label=\"来源类型\" prop=\"sourceType\" required>\n                  <el-select v-model=\"form.sourceType\" placeholder=\"销售订单\" style=\"width: 100%\" @change=\"handleSourceTypeChange\">\n                    <el-option\n                      v-for=\"item in sourceTypeOptions\"\n                      :key=\"item.dictValue\"\n                      :label=\"item.dictLabel\"\n                      :value=\"item.dictValue\"\n                    ></el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"订单编号\" prop=\"orderCode\">\n                  <el-input v-model=\"form.orderCode\" placeholder=\"请输入\">\n                    <el-button\n                      v-if=\"form.sourceType === 'PRODUCTION_ORDER'\"\n                      slot=\"append\"\n                      icon=\"el-icon-search\"\n                      @click=\"openProductionOrderDialog\">\n                      选择\n                    </el-button>\n                  </el-input>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-row :gutter=\"20\">\n              <el-col :span=\"12\">\n                <el-form-item label=\"成品名称\" prop=\"productName\">\n                  <el-input\n                    placeholder=\"请选择成品\"\n                    v-model=\"form.productName\"\n                    class=\"input-with-select\"\n                  >\n                    <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"openProductSelection\"></el-button>\n                  </el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"成品编号\" prop=\"productCode\">\n                  <el-input v-model=\"form.productCode\" placeholder=\"请输入\" disabled></el-input>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-row :gutter=\"20\">\n              <el-col :span=\"12\">\n                <el-form-item label=\"规格型号\" prop=\"specification\">\n                  <el-input v-model=\"form.specification\" placeholder=\"请输入\" disabled></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"单位\" prop=\"unit\">\n                  <el-input v-model=\"form.unit\" placeholder=\"请输入\" disabled></el-input>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-row :gutter=\"20\">\n              <el-col :span=\"12\">\n                <el-form-item label=\"计划数量\" prop=\"plannedQty\" required>\n                  <el-input-number v-model=\"form.plannedQty\" :min=\"1\" controls-position=\"right\" style=\"width: 100%\"></el-input-number>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-row :gutter=\"20\">\n              <el-col :span=\"12\">\n                <el-form-item label=\"开工时间\" prop=\"planStartTime\">\n                  <el-date-picker\n                    v-model=\"form.planStartTime\"\n                    type=\"date\"\n                    placeholder=\"请选择日期\"\n                    style=\"width: 100%\"\n                    value-format=\"yyyy-MM-dd\"\n                  ></el-date-picker>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"完工时间\" prop=\"planEndTime\">\n                  <el-date-picker\n                    v-model=\"form.planEndTime\"\n                    type=\"date\"\n                    placeholder=\"请选择日期\"\n                    style=\"width: 100%\"\n                    value-format=\"yyyy-MM-dd\"\n                  ></el-date-picker>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-row :gutter=\"20\">\n              <el-col :span=\"12\">\n                <el-form-item label=\"需求日期\" prop=\"requiredDate\">\n                  <el-date-picker\n                    v-model=\"form.requiredDate\"\n                    type=\"date\"\n                    placeholder=\"请选择日期\"\n                    style=\"width: 100%\"\n                    value-format=\"yyyy-MM-dd\"\n                  ></el-date-picker>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-row :gutter=\"20\">\n              <el-col :span=\"24\">\n                <el-form-item label=\"备注\" prop=\"remark\">\n                  <el-input\n                    type=\"textarea\"\n                    v-model=\"form.remark\"\n                    placeholder=\"请输入\"\n                    :rows=\"4\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-row :gutter=\"20\">\n              <el-col :span=\"24\">\n                <el-form-item label=\"附件\" prop=\"attachment\">\n                  <div class=\"upload-container\" @click=\"triggerUpload\">\n                    <el-upload\n                      ref=\"upload\"\n                      class=\"upload-hidden\"\n                      action=\"#\"\n                      :http-request=\"uploadFile\"\n                      :file-list=\"fileList\"\n                      :before-upload=\"beforeUpload\"\n                      :on-remove=\"handleRemove\"\n                      multiple\n                      drag\n                    >\n                      <div class=\"upload-area\">\n                        <i class=\"el-icon-upload\"></i>\n                        <div class=\"upload-text\">点击或者拖动文件到虚线框内上传</div>\n                        <div class=\"upload-hint\">支持 docx, xls, PDF, rar, zip, PNG, JPG 等类型的文件</div>\n                      </div>\n                    </el-upload>\n                  </div>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-divider>\n              <span class=\"bom-title\">BOM组成</span>\n            </el-divider>\n            \n            <el-row>\n              <el-col :span=\"24\">\n                <div class=\"bom-container\">\n                  <div class=\"folder-icon\" v-if=\"!selectedBom\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"60\" height=\"60\" viewBox=\"0 0 24 24\" fill=\"#DCDFE6\">\n                      <path d=\"M10 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2h-8l-2-2z\"/>\n                    </svg>\n                  </div>\n                  <div class=\"bom-text\" v-if=\"!selectedBom\">暂无数据</div>\n                  <div class=\"bom-info\" v-else>\n                    <div class=\"bom-header\">\n                      <div class=\"bom-title-info\">\n                        <span>BOM编号：{{ selectedBom.bom_code }}</span>\n                        <span>版本号：{{ selectedBom.bom_version }}</span>\n                      </div>\n                      <el-button type=\"text\" icon=\"el-icon-delete\" @click=\"clearSelectedBom\">清除</el-button>\n                    </div>\n                    <el-table\n                      :data=\"bomDetailList\"\n                      border\n                      size=\"small\"\n                      style=\"width: 100%\"\n                      class=\"bom-detail-table\"\n                    >\n                      <el-table-column label=\"序号\" type=\"index\" width=\"50\" align=\"center\"></el-table-column>\n                      <el-table-column prop=\"material_code\" label=\"物料编码\" min-width=\"120\" align=\"center\"></el-table-column>\n                      <el-table-column prop=\"material_name\" label=\"物料名称\" min-width=\"150\" align=\"center\"></el-table-column>\n                      <el-table-column prop=\"specification\" label=\"规格型号\" min-width=\"100\" align=\"center\"></el-table-column>\n                      <el-table-column prop=\"unit\" label=\"单位\" width=\"60\" align=\"center\"></el-table-column>\n                      <el-table-column prop=\"quantity\" label=\"用量\" width=\"80\" align=\"center\"></el-table-column>\n                      <el-table-column prop=\"remark\" label=\"备注\" min-width=\"120\" align=\"center\"></el-table-column>\n                    </el-table>\n                  </div>\n                  <div class=\"bom-action\">\n                    <el-tooltip :disabled=\"form.productId\" content=\"请先选择成品!\" placement=\"right\" effect=\"light\">\n                      <el-button type=\"primary\" class=\"select-bom-button\" @click=\"selectBom\">选择BOM</el-button>\n                    </el-tooltip>\n                  </div>\n                </div>\n              </el-col>\n            </el-row>\n            \n            <el-row>\n              <el-col :span=\"24\" style=\"text-align: center; margin-top: 20px;\">\n                <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n                <el-button @click=\"cancel\">取 消</el-button>\n              </el-col>\n            </el-row>\n          </el-form>\n        </el-tab-pane>\n      </el-tabs>\n    </div>\n    \n    <!-- 产品选择对话框 -->\n    <el-dialog title=\"选择成品\" :visible.sync=\"productDialogVisible\" width=\"40%\" append-to-body>\n      <el-form :model=\"productQuery\" ref=\"productQueryForm\" :inline=\"true\" class=\"demo-form-inline\" size=\"small\">\n        <el-form-item>\n          <el-input v-model=\"productQuery.keyword\" placeholder=\"请输入产品编号/名称\" clearable style=\"width: 180px;\"></el-input>\n        </el-form-item>\n        <el-form-item>\n          <el-select v-model=\"productQuery.unit\" placeholder=\"请选择单位\" clearable style=\"width: 120px;\">\n            <el-option label=\"个\" value=\"个\"></el-option>\n            <el-option label=\"件\" value=\"件\"></el-option>\n            <el-option label=\"台\" value=\"台\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item>\n          <el-select v-model=\"productQuery.type\" placeholder=\"请选择类型\" clearable style=\"width: 120px;\">\n            <el-option label=\"成品\" value=\"成品\"></el-option>\n            <el-option label=\"半成品\" value=\"半成品\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item>\n          <el-select v-model=\"productQuery.property\" placeholder=\"请选择产品属性\" clearable style=\"width: 120px;\">\n            <el-option label=\"自制\" value=\"自制\"></el-option>\n            <el-option label=\"外购\" value=\"外购\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"small\" @click=\"searchProducts\">查询</el-button>\n          <el-button icon=\"el-icon-refresh\" size=\"small\" @click=\"resetProductQuery\">重置</el-button>\n        </el-form-item>\n      </el-form>\n      \n      <el-table\n        v-loading=\"productLoading\"\n        :data=\"productList\"\n        border\n        size=\"small\"\n        style=\"width: 100%\"\n        @selection-change=\"handleProductSelectionChange\"\n        height=\"300\"\n      >\n        <el-table-column type=\"selection\" width=\"40\" align=\"center\"></el-table-column>\n        <el-table-column label=\"序号\" type=\"index\" width=\"40\" align=\"center\"></el-table-column>\n        <el-table-column prop=\"product_code\" label=\"产品编号\" width=\"90\" align=\"center\"></el-table-column>\n        <el-table-column prop=\"product_name\" label=\"产品名称\" min-width=\"120\" align=\"center\"></el-table-column>\n        <el-table-column prop=\"product_sfn\" label=\"规格型号\" min-width=\"90\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <span :style=\"{color: '#1890ff'}\">{{ scope.row.product_sfn }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"product_unit\" label=\"单位\" width=\"60\" align=\"center\"></el-table-column>\n        <!-- 生产订单产品特有的列 -->\n        <el-table-column v-if=\"form.sourceType === 'PRODUCTION_ORDER'\" prop=\"order_qty\" label=\"订单数量\" width=\"80\" align=\"center\"></el-table-column>\n        <el-table-column v-if=\"form.sourceType === 'PRODUCTION_ORDER'\" prop=\"delivery_date\" label=\"交付日期\" width=\"100\" align=\"center\">\n          <template slot-scope=\"scope\">\n            {{ scope.row.delivery_date ? scope.row.delivery_date.substring(0, 10) : '' }}\n          </template>\n        </el-table-column>\n      </el-table>\n      \n      <div class=\"pagination-container\">\n        <span class=\"total-text\">共 {{ productTotal }} 条</span>\n        <div class=\"pagination-wrapper\">\n          <span class=\"page-size\">\n            <el-select v-model=\"productQuery.pageSize\" size=\"mini\" @change=\"handleProductSizeChange\" style=\"width: 80px;\">\n              <el-option\n                v-for=\"item in [10, 20, 30, 50]\"\n                :key=\"item\"\n                :label=\"`${item}条/页`\"\n                :value=\"item\">\n              </el-option>\n            </el-select>\n          </span>\n          <el-pagination\n            small\n            background\n            @current-change=\"handleProductCurrentChange\"\n            :current-page=\"productQuery.pageNum\"\n            :page-size=\"productQuery.pageSize\"\n            layout=\"prev, pager, next, jumper\"\n            :pager-count=\"5\"\n            :total=\"productTotal\">\n          </el-pagination>\n        </div>\n      </div>\n      \n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button size=\"small\" @click=\"productDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" size=\"small\" @click=\"confirmProductSelect\">确定</el-button>\n      </div>\n    </el-dialog>\n    \n    <!-- BOM选择对话框 -->\n    <el-dialog title=\"选择BOM\" :visible.sync=\"bomDialogVisible\" width=\"40%\" append-to-body>\n      <div class=\"bom-dialog-header\">\n        <div class=\"product-info\">\n          <span>产品名称：{{ form.productName }}</span>\n          <span>产品编号：{{ form.productCode }}</span>\n          <span>规格型号：{{ form.specification }}</span>\n          <span>单位：{{ form.unit }}</span>\n        </div>\n      </div>\n      \n      <el-table\n        v-loading=\"bomLoading\"\n        :data=\"bomList\"\n        border\n        style=\"width: 100%\"\n        @row-click=\"handleBomSelect\"\n        highlight-current-row\n        size=\"small\"\n        height=\"300\"\n      >\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <el-radio v-model=\"selectedBomId\" :label=\"scope.row.bom_id\" @change=\"handleBomSelect(scope.row)\">&nbsp;</el-radio>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"序号\" type=\"index\" width=\"55\" align=\"center\"></el-table-column>\n        <el-table-column prop=\"bom_code\" label=\"BOM编号\" min-width=\"150\" align=\"center\"></el-table-column>\n        <el-table-column prop=\"bom_version\" label=\"版本号\" width=\"100\" align=\"center\"></el-table-column>\n        <el-table-column prop=\"bom_status\" label=\"默认BOM\" width=\"100\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <span>{{ scope.row.bom_status === '1' ? '是' : '否' }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"bom_output\" label=\"日产量\" width=\"100\" align=\"center\"></el-table-column>\n      </el-table>\n      \n      <pagination\n        v-show=\"bomTotal > 0\"\n        :total=\"bomTotal\"\n        :page.sync=\"bomQuery.pageNum\"\n        :limit.sync=\"bomQuery.pageSize\"\n        @pagination=\"getBomList\"\n      />\n      \n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"bomDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"confirmBomSelect\">确定</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 生产订单选择对话框 -->\n    <el-dialog title=\"选择生产订单\" :visible.sync=\"productionOrderDialogVisible\" width=\"800px\" append-to-body>\n      <el-table\n        :data=\"productionOrderList\"\n        @selection-change=\"handleProductionOrderSelectionChange\"\n        style=\"width: 100%\">\n        <el-table-column type=\"selection\" width=\"55\"></el-table-column>\n        <el-table-column prop=\"prodOrderCode\" label=\"订单编号\" width=\"150\"></el-table-column>\n        <el-table-column prop=\"customerName\" label=\"客户名称\" width=\"150\"></el-table-column>\n        <el-table-column prop=\"deliveryDate\" label=\"交付日期\" width=\"120\">\n          <template slot-scope=\"scope\">\n            {{ scope.row.deliveryDate ? scope.row.deliveryDate.substring(0, 10) : '' }}\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"status\" label=\"状态\" width=\"80\">\n          <template slot-scope=\"scope\">\n            <el-tag v-if=\"scope.row.status === '1'\" type=\"warning\">待计划</el-tag>\n            <el-tag v-else-if=\"scope.row.status === '2'\" type=\"primary\">已计划</el-tag>\n            <el-tag v-else-if=\"scope.row.status === '3'\" type=\"success\">生产中</el-tag>\n            <el-tag v-else-if=\"scope.row.status === '4'\" type=\"info\">已完成</el-tag>\n            <el-tag v-else type=\"info\">{{ scope.row.status }}</el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"remark\" label=\"备注\" show-overflow-tooltip></el-table-column>\n      </el-table>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"productionOrderDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"confirmProductionOrderSelect\">确定</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { addProductionPlan } from \"@/api/sc/productionPlan\";\nimport { listProducts, listBomsByProductId, findBomDetails } from \"@/api/basic/product\";\nimport { getAutoNumbers } from \"@/api/basic/numbers\";\nimport Pagination from \"@/components/Pagination\";\n\nexport default {\n  name: \"AddPlan\",\n  components: {\n    Pagination\n  },\n  data() {\n    return {\n      // 表单数据\n      form: {\n        planCode: \"\",\n        planName: \"\",\n        sourceType: \"销售订单\",\n        orderCode: \"\",\n        productId: undefined,\n        productName: \"\",\n        productCode: \"\",\n        specification: \"\",\n        unit: \"\",\n        plannedQty: 1,\n        planStartTime: \"\",\n        planEndTime: \"\",\n        requiredDate: \"\",\n        remark: \"\"\n      },\n      // 是否使用系统编号\n      isSystemCode: true,\n      // 表单验证规则\n      rules: {\n        planName: [\n          { required: true, message: \"计划名称不能为空\", trigger: \"blur\" },\n          { max: 50, message: \"长度不能超过50个字符\", trigger: \"blur\" }\n        ],\n        sourceType: [\n          { required: true, message: \"来源类型不能为空\", trigger: \"change\" }\n        ],\n        productName: [\n          { required: true, message: \"请选择产品\", trigger: \"change\" }\n        ],\n        plannedQty: [\n          { required: true, message: \"计划数量不能为空\", trigger: \"blur\" }\n        ],\n        planStartTime: [\n          { validator: this.validateStartTime, trigger: \"change\" }\n        ],\n        planEndTime: [\n          { validator: this.validateEndTime, trigger: \"change\" }\n        ],\n        requiredDate: [\n          { validator: this.validateRequiredDate, trigger: \"change\" }\n        ]\n      },\n      // 产品下拉选项\n      productOptions: [],\n      // 产品加载状态\n      productLoading: false,\n      // 来源类型选项\n      sourceTypeOptions: [\n        { dictLabel: \"销售订单\", dictValue: \"销售订单\" },\n        { dictLabel: \"库存备货\", dictValue: \"库存备货\" },\n        { dictLabel: \"生产订单\", dictValue: \"PRODUCTION_ORDER\" }\n      ],\n      // 上传文件列表\n      fileList: [],\n      \n      // 产品选择对话框\n      productDialogVisible: false,\n      productQuery: {\n        pageNum: 1,\n        pageSize: 10,\n        keyword: \"\",\n        unit: \"\",\n        type: \"\",\n        property: \"\"\n      },\n      productList: [],\n      productTotal: 0,\n\n      // 生产订单选择对话框\n      productionOrderDialogVisible: false,\n      productionOrderList: [],\n      selectedProductionOrder: null,\n      selectedProduct: null,\n      \n      // BOM选择对话框\n      bomDialogVisible: false,\n      bomQuery: {\n        pageNum: 1,\n        pageSize: 10,\n        productId: undefined\n      },\n      bomList: [],\n      bomTotal: 0,\n      bomLoading: false,\n      selectedBom: null,\n      selectedBomId: null,\n      bomDetailList: [],\n    };\n  },\n  created() {\n    // 初始化时如果是系统编号，则生成计划编号\n    if (this.isSystemCode) {\n      this.generatePlanCode();\n    }\n  },\n  methods: {\n    // 生成计划编号\n    generatePlanCode() {\n      getAutoNumbers(6).then(response => {\n        if (response.code === 200) {\n          this.form.planCode = response.msg;\n        } else {\n          this.$message.error('获取计划编号失败');\n        }\n      }).catch(() => {\n        this.$message.error('获取计划编号失败');\n      });\n    },\n    \n    // 处理系统编号开关变化\n    handleSystemCodeChange(val) {\n      if (val) {\n        // 如果开启系统编号，则生成编号\n        this.generatePlanCode();\n      } else {\n        // 如果关闭系统编号，则清空编号\n        this.form.planCode = '';\n      }\n    },\n\n    // 处理来源类型变化\n    handleSourceTypeChange(val) {\n      // 清空订单编号\n      this.form.orderCode = \"\";\n      // 如果选择生产订单，清空产品相关信息\n      if (val === 'PRODUCTION_ORDER') {\n        this.form.productId = undefined;\n        this.form.productName = \"\";\n        this.form.productCode = \"\";\n        this.form.specification = \"\";\n        this.form.unit = \"\";\n      }\n    },\n\n    // 打开生产订单选择对话框\n    openProductionOrderDialog() {\n      this.productionOrderDialogVisible = true;\n      this.getProductionOrderList();\n    },\n\n    // 获取生产订单列表\n    getProductionOrderList() {\n      // 这里调用生产订单列表API\n      import(\"@/api/sc/productionOrder\").then(api => {\n        api.listProductionOrder({}).then(response => {\n          this.productionOrderList = response.rows || [];\n        }).catch(() => {\n          this.$message.error('获取生产订单列表失败');\n        });\n      });\n    },\n\n    // 处理生产订单选择变化\n    handleProductionOrderSelectionChange(selection) {\n      this.selectedProductionOrder = selection.length > 0 ? selection[0] : null;\n    },\n\n    // 确认选择生产订单\n    confirmProductionOrderSelect() {\n      if (!this.selectedProductionOrder) {\n        this.$message.warning('请选择一个生产订单');\n        return;\n      }\n\n      // 设置订单编号\n      this.form.orderCode = this.selectedProductionOrder.prodOrderCode;\n\n      // 调用API根据生产订单创建计划模板\n      import(\"@/api/sc/productionPlan\").then(api => {\n        api.createPlanFromOrder(this.selectedProductionOrder.productionOrderId).then(response => {\n          if (response.code === 200) {\n            const planData = response.data;\n            // 填充表单数据\n            this.form.planName = planData.planName;\n            this.form.productId = planData.productId;\n            this.form.plannedQty = planData.plannedQty;\n            this.form.planStartTime = planData.planStartTime;\n            this.form.planEndTime = planData.planEndTime;\n            this.form.requiredDate = planData.requiredDate;\n            this.form.remark = planData.remark;\n\n            // 如果有产品信息，需要获取产品详情\n            if (planData.productId) {\n              this.getProductDetails(planData.productId);\n            }\n\n            this.$message.success('已关联生产订单，请完善其他信息');\n          }\n        }).catch(() => {\n          this.$message.error('关联生产订单失败');\n        });\n      });\n\n      this.productionOrderDialogVisible = false;\n    },\n\n    // 获取产品详情\n    getProductDetails(productId) {\n      listProducts({ productId: productId }).then(response => {\n        if (response.rows && response.rows.length > 0) {\n          const product = response.rows[0];\n          this.form.productName = product.product_name;\n          this.form.productCode = product.product_code;\n          this.form.specification = product.product_sfn;\n          this.form.unit = product.product_unit;\n        }\n      }).catch(() => {\n        console.error('获取产品详情失败');\n      });\n    },\n    \n    // 触发上传\n    triggerUpload() {\n      this.$refs.upload.$el.click();\n    },\n    \n    // 打开产品选择弹窗\n    openProductSelection() {\n      if (this.form.sourceType === 'PRODUCTION_ORDER') {\n        // 如果选择了生产订单，但没有选择具体订单\n        if (!this.form.orderCode) {\n          this.$message.warning('请先选择生产订单');\n          return;\n        }\n        // 显示该订单的产品列表\n        this.getProductsByOrder();\n      } else {\n        // 其他情况显示全部产品\n        this.getProductList();\n      }\n      this.productDialogVisible = true;\n    },\n    \n    // 获取产品列表\n    getProductList() {\n      this.productLoading = true;\n      listProducts({\n        pageNum: this.productQuery.pageNum,\n        pageSize: this.productQuery.pageSize,\n        keyword: this.productQuery.keyword,\n        productUnit: this.productQuery.unit,\n        productType: this.productQuery.type,\n        productProperty: this.productQuery.property\n      }).then(response => {\n        this.productLoading = false;\n        if (response.code === 200) {\n          this.productList = response.rows;\n          this.productTotal = response.total;\n        }\n      }).catch(() => {\n        this.productLoading = false;\n        // 模拟数据\n        this.productList = [\n          { product_id: 1, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' },\n          { product_id: 2, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' },\n          { product_id: 3, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' },\n          { product_id: 4, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' },\n          { product_id: 5, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' }\n        ];\n        this.productTotal = 50;\n      });\n    },\n\n    // 根据生产订单获取产品列表\n    getProductsByOrder() {\n      if (!this.selectedProductionOrder) {\n        this.$message.error('未找到选中的生产订单信息');\n        return;\n      }\n\n      this.productLoading = true;\n      // 调用API获取订单产品明细\n      import(\"@/api/sc/productionOrder\").then(api => {\n        api.getProductionOrderDetails(this.selectedProductionOrder.productionOrderId).then(response => {\n          if (response.code === 200 && response.data && response.data.products) {\n            // 将订单明细转换为产品列表格式\n            this.productList = response.data.products.map(detail => ({\n              product_id: detail.productId,\n              product_name: detail.productName,\n              product_code: detail.productCode,\n              product_sfn: detail.productSfn,\n              product_unit: detail.productUnit,\n              // 添加订单相关信息\n              order_qty: detail.qtyNum,\n              delivery_date: detail.deliveryDate\n            }));\n            this.productTotal = this.productList.length;\n          } else {\n            this.$message.warning('该订单暂无产品明细');\n            this.productList = [];\n            this.productTotal = 0;\n          }\n          this.productLoading = false;\n        }).catch(() => {\n          this.$message.error('获取订单产品列表失败');\n          this.productLoading = false;\n        });\n      });\n    },\n\n    // 搜索产品\n    searchProducts() {\n      this.productQuery.pageNum = 1;\n      this.getProductList();\n    },\n    \n    // 重置产品查询条件\n    resetProductQuery() {\n      this.productQuery = {\n        pageNum: 1,\n        pageSize: 10,\n        keyword: \"\",\n        unit: \"\",\n        type: \"\",\n        property: \"\"\n      };\n      this.getProductList();\n    },\n    \n    // 处理产品表格选择变化\n    handleProductSelectionChange(selection) {\n      if (selection.length > 0) {\n        this.selectedProduct = selection[0];\n      } else {\n        this.selectedProduct = null;\n      }\n    },\n    \n    // 处理产品页码变化\n    handleProductCurrentChange(currentPage) {\n      this.productQuery.pageNum = currentPage;\n      this.getProductList();\n    },\n    \n    // 处理产品每页条数变化\n    handleProductSizeChange(size) {\n      this.productQuery.pageSize = size;\n      this.productQuery.pageNum = 1;\n      this.getProductList();\n    },\n    \n    // 确认产品选择\n    confirmProductSelect() {\n      if (this.selectedProduct) {\n        this.form.productId = this.selectedProduct.product_id;\n        this.form.productName = this.selectedProduct.product_name;\n        this.form.productCode = this.selectedProduct.product_code;\n        this.form.specification = this.selectedProduct.product_sfn;\n        this.form.unit = this.selectedProduct.product_unit;\n\n        // 如果是从生产订单选择的产品，自动填充订单数量和交付日期\n        if (this.form.sourceType === 'PRODUCTION_ORDER' && this.selectedProduct.order_qty) {\n          this.form.plannedQty = this.selectedProduct.order_qty;\n          if (this.selectedProduct.delivery_date) {\n            this.form.requiredDate = this.selectedProduct.delivery_date;\n            this.form.planEndTime = this.selectedProduct.delivery_date;\n          }\n        }\n\n        this.productDialogVisible = false;\n\n        // 清空已选BOM\n        this.selectedBom = null;\n        this.selectedBomId = null;\n      } else {\n        this.$message.warning('请选择一个产品！');\n      }\n    },\n    \n    // 选择BOM\n    selectBom() {\n      if (!this.form.productId) {\n        this.$message.warning('请先选择成品！');\n        return;\n      }\n      this.bomDialogVisible = true;\n      this.getBomList();\n    },\n    \n    // 获取BOM列表\n    getBomList() {\n      this.bomLoading = true;\n      listBomsByProductId(this.form.productId).then(response => {\n        this.bomLoading = false;\n        if (response.code === 200) {\n          this.bomList = response.rows;\n          this.bomTotal = response.total;\n          if (!this.bomList || this.bomList.length === 0) {\n            this.$message.info(\"未找到该产品的BOM信息\");\n          } else {\n            // 如果有默认BOM，则自动选中\n            const defaultBom = this.bomList.find(b => b.bom_status === '1');\n            if (defaultBom) {\n              this.handleBomSelect(defaultBom);\n            }\n          }\n        } else {\n          this.bomList = [];\n          this.bomTotal = 0;\n        }\n      }).catch(() => {\n        this.bomLoading = false;\n        this.$message.error('获取BOM列表失败');\n      });\n    },\n    \n    // 处理BOM行选择\n    handleBomSelect(row) {\n      this.selectedBom = row;\n      this.selectedBomId = row.bom_id;\n    },\n    \n    // 确认BOM选择\n    confirmBomSelect() {\n      if (this.selectedBom) {\n        this.bomDialogVisible = false;\n        // 获取BOM详情\n        this.getBomDetail();\n      } else {\n        this.$message.warning('请选择一个BOM！');\n      }\n    },\n    \n    // 获取BOM详情\n    getBomDetail() {\n      findBomDetails(this.selectedBom.bom_id).then(response => {\n        console.log(\"成功获取BOM详情响应:\", response);\n        if (response && response.code === 200) {\n          this.bomDetailList = response.rows;\n        } else {\n          this.bomDetailList = [];\n          this.$message.error(\"获取BOM详情失败: \" + (response ? response.msg : '无响应'));\n        }\n      }).catch(error => {\n        console.error(\"获取BOM详情接口调用失败:\", error);\n        this.$message.error(\"获取BOM详情接口调用失败\");\n        this.bomDetailList = [];\n      });\n    },\n    \n    // 清除已选BOM\n    clearSelectedBom() {\n      this.selectedBom = null;\n      this.selectedBomId = null;\n      this.bomDetailList = [];\n    },\n    \n    // 上传前检查文件类型和大小\n    beforeUpload(file) {\n      const isValidType = /\\.(doc|docx|xls|xlsx|pdf|rar|zip|png|jpg|jpeg)$/i.test(file.name);\n      const isLt10M = file.size / 1024 / 1024 < 10;\n\n      if (!isValidType) {\n        this.$message.error('上传文件格式不支持!');\n        return false;\n      }\n      if (!isLt10M) {\n        this.$message.error('上传文件大小不能超过 10MB!');\n        return false;\n      }\n      return true;\n    },\n    \n    // 上传文件处理\n    uploadFile(options) {\n      // 这里应该调用实际的文件上传API\n      console.log('文件上传:', options.file);\n      // 假设上传成功\n      this.fileList.push({\n        name: options.file.name,\n        url: URL.createObjectURL(options.file)\n      });\n      options.onSuccess();\n    },\n    \n    // 移除文件\n    handleRemove(file) {\n      const index = this.fileList.indexOf(file);\n      if (index !== -1) {\n        this.fileList.splice(index, 1);\n      }\n    },\n    \n    // 表单提交\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          // 额外的日期逻辑验证\n          if (!this.validateDateLogic()) {\n            return;\n          }\n\n          // 表单验证通过，调用API提交数据\n          const data = {\n            planCode: this.form.planCode,\n            planName: this.form.planName,\n            sourceType: this.form.sourceType,\n            orderCode: this.form.orderCode,\n            planStartTime: this.form.planStartTime,\n            planEndTime: this.form.planEndTime,\n            requiredDate: this.form.requiredDate,\n            remark: this.form.remark,\n            productId: this.form.productId,\n            plannedQty: this.form.plannedQty\n          };\n          \n          addProductionPlan(data).then(response => {\n            if (response.code === 200) {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.cancel();\n            } else {\n              this.$modal.msgError(response.msg || \"新增失败\");\n            }\n          }).catch(() => {\n            // 模拟成功响应\n            this.$modal.msgSuccess(\"新增成功\");\n            this.cancel();\n          });\n        }\n      });\n    },\n    \n    // 取消按钮\n    cancel() {\n      this.$router.push({ path: \"/sc/plan\" });\n    },\n\n    // 验证开工时间\n    validateStartTime(rule, value, callback) {\n      if (!value) {\n        callback();\n        return;\n      }\n\n      const today = new Date();\n      today.setHours(0, 0, 0, 0);\n      const startDate = new Date(value);\n\n      // 开工日期不能早于当前日期\n      if (startDate < today) {\n        callback(new Error('开工日期不能早于当前日期'));\n        return;\n      }\n\n      // 如果完工时间已选择，开工时间不能晚于完工时间\n      if (this.form.planEndTime) {\n        const endDate = new Date(this.form.planEndTime);\n        if (startDate > endDate) {\n          callback(new Error('开工日期不能晚于完工日期'));\n          return;\n        }\n      }\n\n      callback();\n    },\n\n    // 验证完工时间\n    validateEndTime(rule, value, callback) {\n      if (!value) {\n        callback();\n        return;\n      }\n\n      const endDate = new Date(value);\n\n      // 如果开工时间已选择，完工时间不能早于开工时间\n      if (this.form.planStartTime) {\n        const startDate = new Date(this.form.planStartTime);\n        if (endDate < startDate) {\n          callback(new Error('完工日期不能早于开工日期'));\n          return;\n        }\n      }\n\n      callback();\n    },\n\n    // 验证需求日期\n    validateRequiredDate(rule, value, callback) {\n      if (!value) {\n        callback();\n        return;\n      }\n\n      const requiredDate = new Date(value);\n\n      // 需求日期不能早于当前日期\n      const today = new Date();\n      today.setHours(0, 0, 0, 0);\n      if (requiredDate < today) {\n        callback(new Error('需求日期不能早于当前日期'));\n        return;\n      }\n\n      // 如果完工时间已选择，需求日期不能早于完工时间\n      if (this.form.planEndTime) {\n        const endDate = new Date(this.form.planEndTime);\n        if (requiredDate < endDate) {\n          callback(new Error('需求日期不能早于完工日期'));\n          return;\n        }\n      }\n\n      callback();\n    },\n\n    // 综合日期逻辑验证\n    validateDateLogic() {\n      const today = new Date();\n      today.setHours(0, 0, 0, 0);\n\n      // 检查开工日期\n      if (this.form.planStartTime) {\n        const startDate = new Date(this.form.planStartTime);\n        if (startDate < today) {\n          this.$modal.msgError(\"开工日期不能早于当前日期\");\n          return false;\n        }\n      }\n\n      // 检查开工日期和完工日期的关系\n      if (this.form.planStartTime && this.form.planEndTime) {\n        const startDate = new Date(this.form.planStartTime);\n        const endDate = new Date(this.form.planEndTime);\n        if (startDate > endDate) {\n          this.$modal.msgError(\"开工日期不能晚于完工日期\");\n          return false;\n        }\n      }\n\n      // 检查需求日期\n      if (this.form.requiredDate) {\n        const requiredDate = new Date(this.form.requiredDate);\n        if (requiredDate < today) {\n          this.$modal.msgError(\"需求日期不能早于当前日期\");\n          return false;\n        }\n\n        // 需求日期不能早于完工日期\n        if (this.form.planEndTime) {\n          const endDate = new Date(this.form.planEndTime);\n          if (requiredDate < endDate) {\n            this.$modal.msgError(\"需求日期不能早于完工日期\");\n            return false;\n          }\n        }\n      }\n\n      return true;\n    }\n  }\n};\n</script>\n\n<style scoped>\n.form-container {\n  background-color: #fff;\n  padding: 10px;\n}\n\n.el-tabs--border-card {\n  box-shadow: none;\n}\n\n.upload-container {\n  width: 100%;\n  border: 1px dashed #d9d9d9;\n  border-radius: 6px;\n  text-align: center;\n  padding: 20px 0;\n  cursor: pointer;\n}\n\n.upload-container:hover {\n  border-color: #409EFF;\n}\n\n.upload-area {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\n.upload-area .el-icon-upload {\n  font-size: 48px;\n  color: #c0c4cc;\n  margin-bottom: 10px;\n}\n\n.upload-text {\n  font-size: 14px;\n  color: #606266;\n  margin-bottom: 10px;\n}\n\n.upload-hint {\n  font-size: 12px;\n  color: #909399;\n}\n\n.input-with-select .el-input-group__append {\n  background-color: #fff;\n}\n\n.bom-title {\n  font-size: 16px;\n  font-weight: bold;\n  color: #303133;\n}\n\n.bom-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  margin: 20px 0;\n  padding: 30px 0;\n}\n\n.folder-icon {\n  margin-bottom: 20px;\n}\n\n.bom-text {\n  font-size: 14px;\n  color: #909399;\n  margin-bottom: 20px;\n}\n\n.warning-text {\n  color: #E6A23C;\n  font-size: 12px;\n  margin-left: 10px;\n}\n\n.warning-text i {\n  margin-right: 5px;\n}\n\n.upload-hidden {\n  width: 100%;\n  height: 100%;\n}\n\n.upload-hidden >>> .el-upload {\n  width: 100%;\n}\n\n.upload-hidden >>> .el-upload-dragger {\n  width: 100%;\n  height: 100%;\n  border: none;\n  padding: 0;\n  margin: 0;\n}\n\n.bom-dialog-header {\n  margin-bottom: 15px;\n  padding: 10px;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n}\n\n.product-info {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.product-info span {\n  margin-right: 20px;\n  line-height: 30px;\n}\n\n.el-radio {\n  margin-right: 0;\n}\n\n.pagination-container {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 0;\n  font-size: 12px;\n}\n\n.pagination-wrapper {\n  display: flex;\n  align-items: center;\n}\n\n.page-size {\n  margin-right: 10px;\n}\n\n.total-text {\n  color: #606266;\n  font-size: 12px;\n}\n\n.bom-info {\n  width: 100%;\n  margin-bottom: 20px;\n}\n\n.bom-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n  padding: 8px;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n}\n\n.bom-title-info {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.bom-title-info span {\n  margin-right: 20px;\n  font-weight: bold;\n}\n\n.bom-detail-table {\n  margin-bottom: 15px;\n}\n\n.bom-action {\n  display: flex;\n  align-items: center;\n}\n\n.select-bom-button {\n  margin-right: 10px;\n}\n</style>\n"]}]}