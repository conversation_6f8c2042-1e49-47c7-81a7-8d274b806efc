{"remainingRequest": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\src\\views\\sc\\plan\\add_plan.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\src\\views\\sc\\plan\\add_plan.vue", "mtime": 1753787109492}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751945356000}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751945361444}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751945356000}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751945364156}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGFkZFByb2R1Y3Rpb25QbGFuIH0gZnJvbSAiQC9hcGkvc2MvcHJvZHVjdGlvblBsYW4iOwppbXBvcnQgeyBsaXN0UHJvZHVjdHMsIGxpc3RCb21zQnlQcm9kdWN0SWQsIGZpbmRCb21EZXRhaWxzIH0gZnJvbSAiQC9hcGkvYmFzaWMvcHJvZHVjdCI7CmltcG9ydCB7IGdldEF1dG9OdW1iZXJzIH0gZnJvbSAiQC9hcGkvYmFzaWMvbnVtYmVycyI7CmltcG9ydCBQYWdpbmF0aW9uIGZyb20gIkAvY29tcG9uZW50cy9QYWdpbmF0aW9uIjsKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiQWRkUGxhbiIsCiAgY29tcG9uZW50czogewogICAgUGFnaW5hdGlvbgogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOihqOWNleaVsOaNrgogICAgICBmb3JtOiB7CiAgICAgICAgcGxhbkNvZGU6ICIiLAogICAgICAgIHBsYW5OYW1lOiAiIiwKICAgICAgICBzb3VyY2VUeXBlOiAiUFJPRFVDVElPTl9PUkRFUiIsCiAgICAgICAgb3JkZXJDb2RlOiAiIiwKICAgICAgICBwcm9kdWN0SWQ6IHVuZGVmaW5lZCwKICAgICAgICBwcm9kdWN0TmFtZTogIiIsCiAgICAgICAgcHJvZHVjdENvZGU6ICIiLAogICAgICAgIHNwZWNpZmljYXRpb246ICIiLAogICAgICAgIHVuaXQ6ICIiLAogICAgICAgIHBsYW5uZWRRdHk6IDEsCiAgICAgICAgcGxhblN0YXJ0VGltZTogIiIsCiAgICAgICAgcGxhbkVuZFRpbWU6ICIiLAogICAgICAgIHJlcXVpcmVkRGF0ZTogIiIsCiAgICAgICAgcmVtYXJrOiAiIiwKICAgICAgICBvcmRlclF0eTogMCAgLy8g5re75Yqg6K6i5Y2V5pWw6YeP5a2X5q61CiAgICAgIH0sCiAgICAgIC8vIOaYr+WQpuS9v+eUqOezu+e7n+e8luWPtwogICAgICBpc1N5c3RlbUNvZGU6IHRydWUsCiAgICAgIC8vIOihqOWNlemqjOivgeinhOWImQogICAgICBydWxlczogewogICAgICAgIHBsYW5OYW1lOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K6h5YiS5ZCN56ew5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sCiAgICAgICAgICB7IG1heDogNTAsIG1lc3NhZ2U6ICLplb/luqbkuI3og73otoXov4c1MOS4quWtl+espiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBzb3VyY2VUeXBlOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5p2l5rqQ57G75Z6L5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImNoYW5nZSIgfQogICAgICAgIF0sCiAgICAgICAgcHJvZHVjdE5hbWU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fpgInmi6nkuqflk4EiLCB0cmlnZ2VyOiAiY2hhbmdlIiB9CiAgICAgICAgXSwKICAgICAgICBwbGFubmVkUXR5OiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K6h5YiS5pWw6YeP5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sCiAgICAgICAgICB7IHZhbGlkYXRvcjogdGhpcy52YWxpZGF0ZVBsYW5uZWRRdHksIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBwbGFuU3RhcnRUaW1lOiBbCiAgICAgICAgICB7IHZhbGlkYXRvcjogdGhpcy52YWxpZGF0ZVN0YXJ0VGltZSwgdHJpZ2dlcjogImNoYW5nZSIgfQogICAgICAgIF0sCiAgICAgICAgcGxhbkVuZFRpbWU6IFsKICAgICAgICAgIHsgdmFsaWRhdG9yOiB0aGlzLnZhbGlkYXRlRW5kVGltZSwgdHJpZ2dlcjogImNoYW5nZSIgfQogICAgICAgIF0sCiAgICAgICAgcmVxdWlyZWREYXRlOiBbCiAgICAgICAgICB7IHZhbGlkYXRvcjogdGhpcy52YWxpZGF0ZVJlcXVpcmVkRGF0ZSwgdHJpZ2dlcjogImNoYW5nZSIgfQogICAgICAgIF0KICAgICAgfSwKICAgICAgLy8g5Lqn5ZOB5LiL5ouJ6YCJ6aG5CiAgICAgIHByb2R1Y3RPcHRpb25zOiBbXSwKICAgICAgLy8g5Lqn5ZOB5Yqg6L2954q25oCBCiAgICAgIHByb2R1Y3RMb2FkaW5nOiBmYWxzZSwKICAgICAgLy8g5p2l5rqQ57G75Z6L6YCJ6aG5CiAgICAgIHNvdXJjZVR5cGVPcHRpb25zOiBbCiAgICAgICAgeyBkaWN0TGFiZWw6ICLnlJ/kuqforqLljZUiLCBkaWN0VmFsdWU6ICJQUk9EVUNUSU9OX09SREVSIiB9CiAgICAgIF0sCiAgICAgIC8vIOS4iuS8oOaWh+S7tuWIl+ihqAogICAgICBmaWxlTGlzdDogW10sCiAgICAgIAogICAgICAvLyDkuqflk4HpgInmi6nlr7nor53moYYKICAgICAgcHJvZHVjdERpYWxvZ1Zpc2libGU6IGZhbHNlLAogICAgICBwcm9kdWN0UXVlcnk6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBrZXl3b3JkOiAiIiwKICAgICAgICB1bml0OiAiIiwKICAgICAgICB0eXBlOiAiIiwKICAgICAgICBwcm9wZXJ0eTogIiIKICAgICAgfSwKICAgICAgcHJvZHVjdExpc3Q6IFtdLAogICAgICBwcm9kdWN0VG90YWw6IDAsCgogICAgICAvLyDnlJ/kuqforqLljZXpgInmi6nlr7nor53moYYKICAgICAgcHJvZHVjdGlvbk9yZGVyRGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIHByb2R1Y3Rpb25PcmRlckxpc3Q6IFtdLAogICAgICBzZWxlY3RlZFByb2R1Y3Rpb25PcmRlcjogbnVsbCwKICAgICAgc2VsZWN0ZWRQcm9kdWN0OiBudWxsLAogICAgICAKICAgICAgLy8gQk9N6YCJ5oup5a+56K+d5qGGCiAgICAgIGJvbURpYWxvZ1Zpc2libGU6IGZhbHNlLAogICAgICBib21RdWVyeTogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIHByb2R1Y3RJZDogdW5kZWZpbmVkCiAgICAgIH0sCiAgICAgIGJvbUxpc3Q6IFtdLAogICAgICBib21Ub3RhbDogMCwKICAgICAgYm9tTG9hZGluZzogZmFsc2UsCiAgICAgIHNlbGVjdGVkQm9tOiBudWxsLAogICAgICBzZWxlY3RlZEJvbUlkOiBudWxsLAogICAgICBib21EZXRhaWxMaXN0OiBbXSwKICAgIH07CiAgfSwKICBjcmVhdGVkKCkgewogICAgLy8g5Yid5aeL5YyW5pe25aaC5p6c5piv57O757uf57yW5Y+377yM5YiZ55Sf5oiQ6K6h5YiS57yW5Y+3CiAgICBpZiAodGhpcy5pc1N5c3RlbUNvZGUpIHsKICAgICAgdGhpcy5nZW5lcmF0ZVBsYW5Db2RlKCk7CiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICAvLyDnlJ/miJDorqHliJLnvJblj7cKICAgIGdlbmVyYXRlUGxhbkNvZGUoKSB7CiAgICAgIGdldEF1dG9OdW1iZXJzKDYpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsKICAgICAgICAgIHRoaXMuZm9ybS5wbGFuQ29kZSA9IHJlc3BvbnNlLm1zZzsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6I635Y+W6K6h5YiS57yW5Y+35aSx6LSlJyk7CiAgICAgICAgfQogICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6I635Y+W6K6h5YiS57yW5Y+35aSx6LSlJyk7CiAgICAgIH0pOwogICAgfSwKICAgIAogICAgLy8g5aSE55CG57O757uf57yW5Y+35byA5YWz5Y+Y5YyWCiAgICBoYW5kbGVTeXN0ZW1Db2RlQ2hhbmdlKHZhbCkgewogICAgICBpZiAodmFsKSB7CiAgICAgICAgLy8g5aaC5p6c5byA5ZCv57O757uf57yW5Y+377yM5YiZ55Sf5oiQ57yW5Y+3CiAgICAgICAgdGhpcy5nZW5lcmF0ZVBsYW5Db2RlKCk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgLy8g5aaC5p6c5YWz6Zet57O757uf57yW5Y+377yM5YiZ5riF56m657yW5Y+3CiAgICAgICAgdGhpcy5mb3JtLnBsYW5Db2RlID0gJyc7CiAgICAgIH0KICAgIH0sCgogICAgLy8g5aSE55CG5p2l5rqQ57G75Z6L5Y+Y5YyWCiAgICBoYW5kbGVTb3VyY2VUeXBlQ2hhbmdlKHZhbCkgewogICAgICAvLyDmuIXnqbrorqLljZXnvJblj7cKICAgICAgdGhpcy5mb3JtLm9yZGVyQ29kZSA9ICIiOwogICAgICAvLyDlpoLmnpzpgInmi6nnlJ/kuqforqLljZXvvIzmuIXnqbrkuqflk4Hnm7jlhbPkv6Hmga8KICAgICAgaWYgKHZhbCA9PT0gJ1BST0RVQ1RJT05fT1JERVInKSB7CiAgICAgICAgdGhpcy5mb3JtLnByb2R1Y3RJZCA9IHVuZGVmaW5lZDsKICAgICAgICB0aGlzLmZvcm0ucHJvZHVjdE5hbWUgPSAiIjsKICAgICAgICB0aGlzLmZvcm0ucHJvZHVjdENvZGUgPSAiIjsKICAgICAgICB0aGlzLmZvcm0uc3BlY2lmaWNhdGlvbiA9ICIiOwogICAgICAgIHRoaXMuZm9ybS51bml0ID0gIiI7CiAgICAgIH0KICAgIH0sCgogICAgLy8g5omT5byA55Sf5Lqn6K6i5Y2V6YCJ5oup5a+56K+d5qGGCiAgICBvcGVuUHJvZHVjdGlvbk9yZGVyRGlhbG9nKCkgewogICAgICB0aGlzLnByb2R1Y3Rpb25PcmRlckRpYWxvZ1Zpc2libGUgPSB0cnVlOwogICAgICB0aGlzLmdldFByb2R1Y3Rpb25PcmRlckxpc3QoKTsKICAgIH0sCgogICAgLy8g6I635Y+W55Sf5Lqn6K6i5Y2V5YiX6KGoCiAgICBnZXRQcm9kdWN0aW9uT3JkZXJMaXN0KCkgewogICAgICAvLyDov5nph4zosIPnlKjnlJ/kuqforqLljZXliJfooahBUEkKICAgICAgaW1wb3J0KCJAL2FwaS9zYy9wcm9kdWN0aW9uT3JkZXIiKS50aGVuKGFwaSA9PiB7CiAgICAgICAgYXBpLmxpc3RQcm9kdWN0aW9uT3JkZXIoe30pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgdGhpcy5wcm9kdWN0aW9uT3JkZXJMaXN0ID0gcmVzcG9uc2Uucm93cyB8fCBbXTsKICAgICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfojrflj5bnlJ/kuqforqLljZXliJfooajlpLHotKUnKTsKICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9LAoKICAgIC8vIOWkhOeQhueUn+S6p+iuouWNlemAieaLqeWPmOWMlgogICAgaGFuZGxlUHJvZHVjdGlvbk9yZGVyU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgewogICAgICB0aGlzLnNlbGVjdGVkUHJvZHVjdGlvbk9yZGVyID0gc2VsZWN0aW9uLmxlbmd0aCA+IDAgPyBzZWxlY3Rpb25bMF0gOiBudWxsOwogICAgfSwKCiAgICAvLyDnoa7orqTpgInmi6nnlJ/kuqforqLljZUKICAgIGNvbmZpcm1Qcm9kdWN0aW9uT3JkZXJTZWxlY3QoKSB7CiAgICAgIGlmICghdGhpcy5zZWxlY3RlZFByb2R1Y3Rpb25PcmRlcikgewogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+36YCJ5oup5LiA5Liq55Sf5Lqn6K6i5Y2VJyk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CgogICAgICAvLyDorr7nva7orqLljZXnvJblj7cKICAgICAgdGhpcy5mb3JtLm9yZGVyQ29kZSA9IHRoaXMuc2VsZWN0ZWRQcm9kdWN0aW9uT3JkZXIucHJvZE9yZGVyQ29kZTsKCiAgICAgIC8vIOiwg+eUqEFQSeagueaNrueUn+S6p+iuouWNleWIm+W7uuiuoeWIkuaooeadvwogICAgICBpbXBvcnQoIkAvYXBpL3NjL3Byb2R1Y3Rpb25QbGFuIikudGhlbihhcGkgPT4gewogICAgICAgIGFwaS5jcmVhdGVQbGFuRnJvbU9yZGVyKHRoaXMuc2VsZWN0ZWRQcm9kdWN0aW9uT3JkZXIucHJvZHVjdGlvbk9yZGVySWQpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgewogICAgICAgICAgICBjb25zdCBwbGFuRGF0YSA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgICAgIC8vIOWhq+WFheihqOWNleaVsOaNrgogICAgICAgICAgICB0aGlzLmZvcm0ucGxhbk5hbWUgPSBwbGFuRGF0YS5wbGFuTmFtZTsKICAgICAgICAgICAgdGhpcy5mb3JtLnByb2R1Y3RJZCA9IHBsYW5EYXRhLnByb2R1Y3RJZDsKICAgICAgICAgICAgdGhpcy5mb3JtLnBsYW5uZWRRdHkgPSBwbGFuRGF0YS5wbGFubmVkUXR5OwogICAgICAgICAgICB0aGlzLmZvcm0ub3JkZXJRdHkgPSBwbGFuRGF0YS5wbGFubmVkUXR5OyAvLyDkv53lrZjorqLljZXmlbDph4/nlKjkuo7pqozor4EKICAgICAgICAgICAgdGhpcy5mb3JtLnBsYW5TdGFydFRpbWUgPSBwbGFuRGF0YS5wbGFuU3RhcnRUaW1lOwogICAgICAgICAgICB0aGlzLmZvcm0ucGxhbkVuZFRpbWUgPSBwbGFuRGF0YS5wbGFuRW5kVGltZTsKICAgICAgICAgICAgdGhpcy5mb3JtLnJlcXVpcmVkRGF0ZSA9IHBsYW5EYXRhLnJlcXVpcmVkRGF0ZTsKICAgICAgICAgICAgdGhpcy5mb3JtLnJlbWFyayA9IHBsYW5EYXRhLnJlbWFyazsKCiAgICAgICAgICAgIC8vIOWmguaenOacieS6p+WTgeS/oeaBr++8jOmcgOimgeiOt+WPluS6p+WTgeivpuaDhQogICAgICAgICAgICBpZiAocGxhbkRhdGEucHJvZHVjdElkKSB7CiAgICAgICAgICAgICAgdGhpcy5nZXRQcm9kdWN0RGV0YWlscyhwbGFuRGF0YS5wcm9kdWN0SWQpOwogICAgICAgICAgICB9CgogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+W3suWFs+iBlOeUn+S6p+iuouWNle+8jOivt+WujOWWhOWFtuS7luS/oeaBrycpOwogICAgICAgICAgfQogICAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WFs+iBlOeUn+S6p+iuouWNleWksei0pScpOwogICAgICAgIH0pOwogICAgICB9KTsKCiAgICAgIHRoaXMucHJvZHVjdGlvbk9yZGVyRGlhbG9nVmlzaWJsZSA9IGZhbHNlOwogICAgfSwKCiAgICAvLyDojrflj5bkuqflk4Hor6bmg4UKICAgIGdldFByb2R1Y3REZXRhaWxzKHByb2R1Y3RJZCkgewogICAgICBsaXN0UHJvZHVjdHMoeyBwcm9kdWN0SWQ6IHByb2R1Y3RJZCB9KS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICBpZiAocmVzcG9uc2Uucm93cyAmJiByZXNwb25zZS5yb3dzLmxlbmd0aCA+IDApIHsKICAgICAgICAgIGNvbnN0IHByb2R1Y3QgPSByZXNwb25zZS5yb3dzWzBdOwogICAgICAgICAgdGhpcy5mb3JtLnByb2R1Y3ROYW1lID0gcHJvZHVjdC5wcm9kdWN0X25hbWU7CiAgICAgICAgICB0aGlzLmZvcm0ucHJvZHVjdENvZGUgPSBwcm9kdWN0LnByb2R1Y3RfY29kZTsKICAgICAgICAgIHRoaXMuZm9ybS5zcGVjaWZpY2F0aW9uID0gcHJvZHVjdC5wcm9kdWN0X3NmbjsKICAgICAgICAgIHRoaXMuZm9ybS51bml0ID0gcHJvZHVjdC5wcm9kdWN0X3VuaXQ7CiAgICAgICAgfQogICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W5Lqn5ZOB6K+m5oOF5aSx6LSlJyk7CiAgICAgIH0pOwogICAgfSwKICAgIAogICAgLy8g6Kem5Y+R5LiK5LygCiAgICB0cmlnZ2VyVXBsb2FkKCkgewogICAgICB0aGlzLiRyZWZzLnVwbG9hZC4kZWwuY2xpY2soKTsKICAgIH0sCiAgICAKICAgIC8vIOaJk+W8gOS6p+WTgemAieaLqeW8ueeqlwogICAgb3BlblByb2R1Y3RTZWxlY3Rpb24oKSB7CiAgICAgIGlmICh0aGlzLmZvcm0uc291cmNlVHlwZSA9PT0gJ1BST0RVQ1RJT05fT1JERVInKSB7CiAgICAgICAgLy8g5aaC5p6c6YCJ5oup5LqG55Sf5Lqn6K6i5Y2V77yM5L2G5rKh5pyJ6YCJ5oup5YW35L2T6K6i5Y2VCiAgICAgICAgaWYgKCF0aGlzLmZvcm0ub3JkZXJDb2RlKSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+WFiOmAieaLqeeUn+S6p+iuouWNlScpOwogICAgICAgICAgcmV0dXJuOwogICAgICAgIH0KICAgICAgICAvLyDmmL7npLror6XorqLljZXnmoTkuqflk4HliJfooagKICAgICAgICB0aGlzLmdldFByb2R1Y3RzQnlPcmRlcigpOwogICAgICB9IGVsc2UgewogICAgICAgIC8vIOWFtuS7luaDheWGteaYvuekuuWFqOmDqOS6p+WTgQogICAgICAgIHRoaXMuZ2V0UHJvZHVjdExpc3QoKTsKICAgICAgfQogICAgICB0aGlzLnByb2R1Y3REaWFsb2dWaXNpYmxlID0gdHJ1ZTsKICAgIH0sCiAgICAKICAgIC8vIOiOt+WPluS6p+WTgeWIl+ihqAogICAgZ2V0UHJvZHVjdExpc3QoKSB7CiAgICAgIHRoaXMucHJvZHVjdExvYWRpbmcgPSB0cnVlOwogICAgICBsaXN0UHJvZHVjdHMoewogICAgICAgIHBhZ2VOdW06IHRoaXMucHJvZHVjdFF1ZXJ5LnBhZ2VOdW0sCiAgICAgICAgcGFnZVNpemU6IHRoaXMucHJvZHVjdFF1ZXJ5LnBhZ2VTaXplLAogICAgICAgIGtleXdvcmQ6IHRoaXMucHJvZHVjdFF1ZXJ5LmtleXdvcmQsCiAgICAgICAgcHJvZHVjdFVuaXQ6IHRoaXMucHJvZHVjdFF1ZXJ5LnVuaXQsCiAgICAgICAgcHJvZHVjdFR5cGU6IHRoaXMucHJvZHVjdFF1ZXJ5LnR5cGUsCiAgICAgICAgcHJvZHVjdFByb3BlcnR5OiB0aGlzLnByb2R1Y3RRdWVyeS5wcm9wZXJ0eQogICAgICB9KS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLnByb2R1Y3RMb2FkaW5nID0gZmFsc2U7CiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgewogICAgICAgICAgdGhpcy5wcm9kdWN0TGlzdCA9IHJlc3BvbnNlLnJvd3M7CiAgICAgICAgICB0aGlzLnByb2R1Y3RUb3RhbCA9IHJlc3BvbnNlLnRvdGFsOwogICAgICAgIH0KICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgIHRoaXMucHJvZHVjdExvYWRpbmcgPSBmYWxzZTsKICAgICAgICAvLyDmqKHmi5/mlbDmja4KICAgICAgICB0aGlzLnByb2R1Y3RMaXN0ID0gWwogICAgICAgICAgeyBwcm9kdWN0X2lkOiAxLCBwcm9kdWN0X2NvZGU6ICdDUDAyNScsIHByb2R1Y3RfbmFtZTogJ+S6p+WTgeS4gCcsIHByb2R1Y3Rfc2ZuOiAn6JOd6ImyJywgcHJvZHVjdF90eXBlOiAn5oiQ5ZOBJywgcHJvZHVjdF91bml0OiAn5LiqJywgcHJvZHVjdF9wcm9wZXJ0eTogJ+iHquWIticgfSwKICAgICAgICAgIHsgcHJvZHVjdF9pZDogMiwgcHJvZHVjdF9jb2RlOiAnQ1AwMjUnLCBwcm9kdWN0X25hbWU6ICfkuqflk4HkuIAnLCBwcm9kdWN0X3NmbjogJ+iTneiJsicsIHByb2R1Y3RfdHlwZTogJ+aIkOWTgScsIHByb2R1Y3RfdW5pdDogJ+S4qicsIHByb2R1Y3RfcHJvcGVydHk6ICfoh6rliLYnIH0sCiAgICAgICAgICB7IHByb2R1Y3RfaWQ6IDMsIHByb2R1Y3RfY29kZTogJ0NQMDI1JywgcHJvZHVjdF9uYW1lOiAn5Lqn5ZOB5LiAJywgcHJvZHVjdF9zZm46ICfok53oibInLCBwcm9kdWN0X3R5cGU6ICfmiJDlk4EnLCBwcm9kdWN0X3VuaXQ6ICfkuKonLCBwcm9kdWN0X3Byb3BlcnR5OiAn6Ieq5Yi2JyB9LAogICAgICAgICAgeyBwcm9kdWN0X2lkOiA0LCBwcm9kdWN0X2NvZGU6ICdDUDAyNScsIHByb2R1Y3RfbmFtZTogJ+S6p+WTgeS4gCcsIHByb2R1Y3Rfc2ZuOiAn6JOd6ImyJywgcHJvZHVjdF90eXBlOiAn5oiQ5ZOBJywgcHJvZHVjdF91bml0OiAn5LiqJywgcHJvZHVjdF9wcm9wZXJ0eTogJ+iHquWIticgfSwKICAgICAgICAgIHsgcHJvZHVjdF9pZDogNSwgcHJvZHVjdF9jb2RlOiAnQ1AwMjUnLCBwcm9kdWN0X25hbWU6ICfkuqflk4HkuIAnLCBwcm9kdWN0X3NmbjogJ+iTneiJsicsIHByb2R1Y3RfdHlwZTogJ+aIkOWTgScsIHByb2R1Y3RfdW5pdDogJ+S4qicsIHByb2R1Y3RfcHJvcGVydHk6ICfoh6rliLYnIH0KICAgICAgICBdOwogICAgICAgIHRoaXMucHJvZHVjdFRvdGFsID0gNTA7CiAgICAgIH0pOwogICAgfSwKCiAgICAvLyDmoLnmja7nlJ/kuqforqLljZXojrflj5bkuqflk4HliJfooagKICAgIGdldFByb2R1Y3RzQnlPcmRlcigpIHsKICAgICAgaWYgKCF0aGlzLnNlbGVjdGVkUHJvZHVjdGlvbk9yZGVyKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5pyq5om+5Yiw6YCJ5Lit55qE55Sf5Lqn6K6i5Y2V5L+h5oGvJyk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CgogICAgICB0aGlzLnByb2R1Y3RMb2FkaW5nID0gdHJ1ZTsKICAgICAgLy8g6LCD55SoQVBJ6I635Y+W6K6i5Y2V5Lqn5ZOB5piO57uGCiAgICAgIGltcG9ydCgiQC9hcGkvc2MvcHJvZHVjdGlvbk9yZGVyIikudGhlbihhcGkgPT4gewogICAgICAgIGFwaS5nZXRQcm9kdWN0aW9uT3JkZXJEZXRhaWxzKHRoaXMuc2VsZWN0ZWRQcm9kdWN0aW9uT3JkZXIucHJvZHVjdGlvbk9yZGVySWQpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCAmJiByZXNwb25zZS5kYXRhICYmIHJlc3BvbnNlLmRhdGEucHJvZHVjdHMpIHsKICAgICAgICAgICAgLy8g5bCG6K6i5Y2V5piO57uG6L2s5o2i5Li65Lqn5ZOB5YiX6KGo5qC85byPCiAgICAgICAgICAgIHRoaXMucHJvZHVjdExpc3QgPSByZXNwb25zZS5kYXRhLnByb2R1Y3RzLm1hcChkZXRhaWwgPT4gKHsKICAgICAgICAgICAgICBwcm9kdWN0X2lkOiBkZXRhaWwucHJvZHVjdElkLAogICAgICAgICAgICAgIHByb2R1Y3RfbmFtZTogZGV0YWlsLnByb2R1Y3ROYW1lLAogICAgICAgICAgICAgIHByb2R1Y3RfY29kZTogZGV0YWlsLnByb2R1Y3RDb2RlLAogICAgICAgICAgICAgIHByb2R1Y3Rfc2ZuOiBkZXRhaWwucHJvZHVjdFNmbiwKICAgICAgICAgICAgICBwcm9kdWN0X3VuaXQ6IGRldGFpbC5wcm9kdWN0VW5pdCwKICAgICAgICAgICAgICAvLyDmt7vliqDorqLljZXnm7jlhbPkv6Hmga8KICAgICAgICAgICAgICBvcmRlcl9xdHk6IGRldGFpbC5xdHlOdW0sCiAgICAgICAgICAgICAgZGVsaXZlcnlfZGF0ZTogZGV0YWlsLmRlbGl2ZXJ5RGF0ZQogICAgICAgICAgICB9KSk7CiAgICAgICAgICAgIHRoaXMucHJvZHVjdFRvdGFsID0gdGhpcy5wcm9kdWN0TGlzdC5sZW5ndGg7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivpeiuouWNleaaguaXoOS6p+WTgeaYjue7hicpOwogICAgICAgICAgICB0aGlzLnByb2R1Y3RMaXN0ID0gW107CiAgICAgICAgICAgIHRoaXMucHJvZHVjdFRvdGFsID0gMDsKICAgICAgICAgIH0KICAgICAgICAgIHRoaXMucHJvZHVjdExvYWRpbmcgPSBmYWxzZTsKICAgICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfojrflj5borqLljZXkuqflk4HliJfooajlpLHotKUnKTsKICAgICAgICAgIHRoaXMucHJvZHVjdExvYWRpbmcgPSBmYWxzZTsKICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9LAoKICAgIC8vIOaQnOe0ouS6p+WTgQogICAgc2VhcmNoUHJvZHVjdHMoKSB7CiAgICAgIHRoaXMucHJvZHVjdFF1ZXJ5LnBhZ2VOdW0gPSAxOwogICAgICB0aGlzLmdldFByb2R1Y3RMaXN0KCk7CiAgICB9LAogICAgCiAgICAvLyDph43nva7kuqflk4Hmn6Xor6LmnaHku7YKICAgIHJlc2V0UHJvZHVjdFF1ZXJ5KCkgewogICAgICB0aGlzLnByb2R1Y3RRdWVyeSA9IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBrZXl3b3JkOiAiIiwKICAgICAgICB1bml0OiAiIiwKICAgICAgICB0eXBlOiAiIiwKICAgICAgICBwcm9wZXJ0eTogIiIKICAgICAgfTsKICAgICAgdGhpcy5nZXRQcm9kdWN0TGlzdCgpOwogICAgfSwKICAgIAogICAgLy8g5aSE55CG5Lqn5ZOB6KGo5qC86YCJ5oup5Y+Y5YyWCiAgICBoYW5kbGVQcm9kdWN0U2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgewogICAgICBpZiAoc2VsZWN0aW9uLmxlbmd0aCA+IDApIHsKICAgICAgICB0aGlzLnNlbGVjdGVkUHJvZHVjdCA9IHNlbGVjdGlvblswXTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLnNlbGVjdGVkUHJvZHVjdCA9IG51bGw7CiAgICAgIH0KICAgIH0sCiAgICAKICAgIC8vIOWkhOeQhuS6p+WTgemhteeggeWPmOWMlgogICAgaGFuZGxlUHJvZHVjdEN1cnJlbnRDaGFuZ2UoY3VycmVudFBhZ2UpIHsKICAgICAgdGhpcy5wcm9kdWN0UXVlcnkucGFnZU51bSA9IGN1cnJlbnRQYWdlOwogICAgICB0aGlzLmdldFByb2R1Y3RMaXN0KCk7CiAgICB9LAogICAgCiAgICAvLyDlpITnkIbkuqflk4Hmr4/pobXmnaHmlbDlj5jljJYKICAgIGhhbmRsZVByb2R1Y3RTaXplQ2hhbmdlKHNpemUpIHsKICAgICAgdGhpcy5wcm9kdWN0UXVlcnkucGFnZVNpemUgPSBzaXplOwogICAgICB0aGlzLnByb2R1Y3RRdWVyeS5wYWdlTnVtID0gMTsKICAgICAgdGhpcy5nZXRQcm9kdWN0TGlzdCgpOwogICAgfSwKICAgIAogICAgLy8g56Gu6K6k5Lqn5ZOB6YCJ5oupCiAgICBjb25maXJtUHJvZHVjdFNlbGVjdCgpIHsKICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRQcm9kdWN0KSB7CiAgICAgICAgdGhpcy5mb3JtLnByb2R1Y3RJZCA9IHRoaXMuc2VsZWN0ZWRQcm9kdWN0LnByb2R1Y3RfaWQ7CiAgICAgICAgdGhpcy5mb3JtLnByb2R1Y3ROYW1lID0gdGhpcy5zZWxlY3RlZFByb2R1Y3QucHJvZHVjdF9uYW1lOwogICAgICAgIHRoaXMuZm9ybS5wcm9kdWN0Q29kZSA9IHRoaXMuc2VsZWN0ZWRQcm9kdWN0LnByb2R1Y3RfY29kZTsKICAgICAgICB0aGlzLmZvcm0uc3BlY2lmaWNhdGlvbiA9IHRoaXMuc2VsZWN0ZWRQcm9kdWN0LnByb2R1Y3Rfc2ZuOwogICAgICAgIHRoaXMuZm9ybS51bml0ID0gdGhpcy5zZWxlY3RlZFByb2R1Y3QucHJvZHVjdF91bml0OwoKICAgICAgICAvLyDlpoLmnpzmmK/ku47nlJ/kuqforqLljZXpgInmi6nnmoTkuqflk4HvvIzoh6rliqjloavlhYXorqLljZXmlbDph4/lkozkuqTku5jml6XmnJ8KICAgICAgICBpZiAodGhpcy5mb3JtLnNvdXJjZVR5cGUgPT09ICdQUk9EVUNUSU9OX09SREVSJyAmJiB0aGlzLnNlbGVjdGVkUHJvZHVjdC5vcmRlcl9xdHkpIHsKICAgICAgICAgIHRoaXMuZm9ybS5wbGFubmVkUXR5ID0gdGhpcy5zZWxlY3RlZFByb2R1Y3Qub3JkZXJfcXR5OwogICAgICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRQcm9kdWN0LmRlbGl2ZXJ5X2RhdGUpIHsKICAgICAgICAgICAgdGhpcy5mb3JtLnJlcXVpcmVkRGF0ZSA9IHRoaXMuc2VsZWN0ZWRQcm9kdWN0LmRlbGl2ZXJ5X2RhdGU7CiAgICAgICAgICAgIHRoaXMuZm9ybS5wbGFuRW5kVGltZSA9IHRoaXMuc2VsZWN0ZWRQcm9kdWN0LmRlbGl2ZXJ5X2RhdGU7CiAgICAgICAgICB9CiAgICAgICAgfQoKICAgICAgICB0aGlzLnByb2R1Y3REaWFsb2dWaXNpYmxlID0gZmFsc2U7CgogICAgICAgIC8vIOa4heepuuW3sumAiUJPTQogICAgICAgIHRoaXMuc2VsZWN0ZWRCb20gPSBudWxsOwogICAgICAgIHRoaXMuc2VsZWN0ZWRCb21JZCA9IG51bGw7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7fpgInmi6nkuIDkuKrkuqflk4HvvIEnKTsKICAgICAgfQogICAgfSwKICAgIAogICAgLy8g6YCJ5oupQk9NCiAgICBzZWxlY3RCb20oKSB7CiAgICAgIGlmICghdGhpcy5mb3JtLnByb2R1Y3RJZCkgewogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+35YWI6YCJ5oup5oiQ5ZOB77yBJyk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIHRoaXMuYm9tRGlhbG9nVmlzaWJsZSA9IHRydWU7CiAgICAgIHRoaXMuZ2V0Qm9tTGlzdCgpOwogICAgfSwKICAgIAogICAgLy8g6I635Y+WQk9N5YiX6KGoCiAgICBnZXRCb21MaXN0KCkgewogICAgICB0aGlzLmJvbUxvYWRpbmcgPSB0cnVlOwogICAgICBsaXN0Qm9tc0J5UHJvZHVjdElkKHRoaXMuZm9ybS5wcm9kdWN0SWQpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuYm9tTG9hZGluZyA9IGZhbHNlOwogICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsKICAgICAgICAgIHRoaXMuYm9tTGlzdCA9IHJlc3BvbnNlLnJvd3M7CiAgICAgICAgICB0aGlzLmJvbVRvdGFsID0gcmVzcG9uc2UudG90YWw7CiAgICAgICAgICBpZiAoIXRoaXMuYm9tTGlzdCB8fCB0aGlzLmJvbUxpc3QubGVuZ3RoID09PSAwKSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuaW5mbygi5pyq5om+5Yiw6K+l5Lqn5ZOB55qEQk9N5L+h5oGvIik7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAvLyDlpoLmnpzmnInpu5jorqRCT03vvIzliJnoh6rliqjpgInkuK0KICAgICAgICAgICAgY29uc3QgZGVmYXVsdEJvbSA9IHRoaXMuYm9tTGlzdC5maW5kKGIgPT4gYi5ib21fc3RhdHVzID09PSAnMScpOwogICAgICAgICAgICBpZiAoZGVmYXVsdEJvbSkgewogICAgICAgICAgICAgIHRoaXMuaGFuZGxlQm9tU2VsZWN0KGRlZmF1bHRCb20pOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuYm9tTGlzdCA9IFtdOwogICAgICAgICAgdGhpcy5ib21Ub3RhbCA9IDA7CiAgICAgICAgfQogICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgdGhpcy5ib21Mb2FkaW5nID0gZmFsc2U7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6I635Y+WQk9N5YiX6KGo5aSx6LSlJyk7CiAgICAgIH0pOwogICAgfSwKICAgIAogICAgLy8g5aSE55CGQk9N6KGM6YCJ5oupCiAgICBoYW5kbGVCb21TZWxlY3Qocm93KSB7CiAgICAgIHRoaXMuc2VsZWN0ZWRCb20gPSByb3c7CiAgICAgIHRoaXMuc2VsZWN0ZWRCb21JZCA9IHJvdy5ib21faWQ7CiAgICB9LAogICAgCiAgICAvLyDnoa7orqRCT03pgInmi6kKICAgIGNvbmZpcm1Cb21TZWxlY3QoKSB7CiAgICAgIGlmICh0aGlzLnNlbGVjdGVkQm9tKSB7CiAgICAgICAgdGhpcy5ib21EaWFsb2dWaXNpYmxlID0gZmFsc2U7CiAgICAgICAgLy8g6I635Y+WQk9N6K+m5oOFCiAgICAgICAgdGhpcy5nZXRCb21EZXRhaWwoKTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+mAieaLqeS4gOS4qkJPTe+8gScpOwogICAgICB9CiAgICB9LAogICAgCiAgICAvLyDojrflj5ZCT03or6bmg4UKICAgIGdldEJvbURldGFpbCgpIHsKICAgICAgZmluZEJvbURldGFpbHModGhpcy5zZWxlY3RlZEJvbS5ib21faWQpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIGNvbnNvbGUubG9nKCLmiJDlip/ojrflj5ZCT03or6bmg4Xlk43lupQ6IiwgcmVzcG9uc2UpOwogICAgICAgIGlmIChyZXNwb25zZSAmJiByZXNwb25zZS5jb2RlID09PSAyMDApIHsKICAgICAgICAgIHRoaXMuYm9tRGV0YWlsTGlzdCA9IHJlc3BvbnNlLnJvd3M7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuYm9tRGV0YWlsTGlzdCA9IFtdOwogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi6I635Y+WQk9N6K+m5oOF5aSx6LSlOiAiICsgKHJlc3BvbnNlID8gcmVzcG9uc2UubXNnIDogJ+aXoOWTjeW6lCcpKTsKICAgICAgICB9CiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsKICAgICAgICBjb25zb2xlLmVycm9yKCLojrflj5ZCT03or6bmg4XmjqXlj6PosIPnlKjlpLHotKU6IiwgZXJyb3IpOwogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuiOt+WPlkJPTeivpuaDheaOpeWPo+iwg+eUqOWksei0pSIpOwogICAgICAgIHRoaXMuYm9tRGV0YWlsTGlzdCA9IFtdOwogICAgICB9KTsKICAgIH0sCiAgICAKICAgIC8vIOa4hemZpOW3sumAiUJPTQogICAgY2xlYXJTZWxlY3RlZEJvbSgpIHsKICAgICAgdGhpcy5zZWxlY3RlZEJvbSA9IG51bGw7CiAgICAgIHRoaXMuc2VsZWN0ZWRCb21JZCA9IG51bGw7CiAgICAgIHRoaXMuYm9tRGV0YWlsTGlzdCA9IFtdOwogICAgfSwKICAgIAogICAgLy8g5LiK5Lyg5YmN5qOA5p+l5paH5Lu257G75Z6L5ZKM5aSn5bCPCiAgICBiZWZvcmVVcGxvYWQoZmlsZSkgewogICAgICBjb25zdCBpc1ZhbGlkVHlwZSA9IC9cLihkb2N8ZG9jeHx4bHN8eGxzeHxwZGZ8cmFyfHppcHxwbmd8anBnfGpwZWcpJC9pLnRlc3QoZmlsZS5uYW1lKTsKICAgICAgY29uc3QgaXNMdDEwTSA9IGZpbGUuc2l6ZSAvIDEwMjQgLyAxMDI0IDwgMTA7CgogICAgICBpZiAoIWlzVmFsaWRUeXBlKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5LiK5Lyg5paH5Lu25qC85byP5LiN5pSv5oyBIScpOwogICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgfQogICAgICBpZiAoIWlzTHQxME0pIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfkuIrkvKDmlofku7blpKflsI/kuI3og73otoXov4cgMTBNQiEnKTsKICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgIH0KICAgICAgcmV0dXJuIHRydWU7CiAgICB9LAogICAgCiAgICAvLyDkuIrkvKDmlofku7blpITnkIYKICAgIHVwbG9hZEZpbGUob3B0aW9ucykgewogICAgICAvLyDov5nph4zlupTor6XosIPnlKjlrp7pmYXnmoTmlofku7bkuIrkvKBBUEkKICAgICAgY29uc29sZS5sb2coJ+aWh+S7tuS4iuS8oDonLCBvcHRpb25zLmZpbGUpOwogICAgICAvLyDlgYforr7kuIrkvKDmiJDlip8KICAgICAgdGhpcy5maWxlTGlzdC5wdXNoKHsKICAgICAgICBuYW1lOiBvcHRpb25zLmZpbGUubmFtZSwKICAgICAgICB1cmw6IFVSTC5jcmVhdGVPYmplY3RVUkwob3B0aW9ucy5maWxlKQogICAgICB9KTsKICAgICAgb3B0aW9ucy5vblN1Y2Nlc3MoKTsKICAgIH0sCiAgICAKICAgIC8vIOenu+mZpOaWh+S7tgogICAgaGFuZGxlUmVtb3ZlKGZpbGUpIHsKICAgICAgY29uc3QgaW5kZXggPSB0aGlzLmZpbGVMaXN0LmluZGV4T2YoZmlsZSk7CiAgICAgIGlmIChpbmRleCAhPT0gLTEpIHsKICAgICAgICB0aGlzLmZpbGVMaXN0LnNwbGljZShpbmRleCwgMSk7CiAgICAgIH0KICAgIH0sCiAgICAKICAgIC8vIOihqOWNleaPkOS6pAogICAgc3VibWl0Rm9ybSgpIHsKICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIC8vIOmineWklueahOaXpeacn+mAu+i+kemqjOivgQogICAgICAgICAgaWYgKCF0aGlzLnZhbGlkYXRlRGF0ZUxvZ2ljKCkpIHsKICAgICAgICAgICAgcmV0dXJuOwogICAgICAgICAgfQoKICAgICAgICAgIC8vIOihqOWNlemqjOivgemAmui/h++8jOiwg+eUqEFQSeaPkOS6pOaVsOaNrgogICAgICAgICAgY29uc3QgZGF0YSA9IHsKICAgICAgICAgICAgcGxhbkNvZGU6IHRoaXMuZm9ybS5wbGFuQ29kZSwKICAgICAgICAgICAgcGxhbk5hbWU6IHRoaXMuZm9ybS5wbGFuTmFtZSwKICAgICAgICAgICAgc291cmNlVHlwZTogdGhpcy5mb3JtLnNvdXJjZVR5cGUsCiAgICAgICAgICAgIG9yZGVyQ29kZTogdGhpcy5mb3JtLm9yZGVyQ29kZSwKICAgICAgICAgICAgcGxhblN0YXJ0VGltZTogdGhpcy5mb3JtLnBsYW5TdGFydFRpbWUsCiAgICAgICAgICAgIHBsYW5FbmRUaW1lOiB0aGlzLmZvcm0ucGxhbkVuZFRpbWUsCiAgICAgICAgICAgIHJlcXVpcmVkRGF0ZTogdGhpcy5mb3JtLnJlcXVpcmVkRGF0ZSwKICAgICAgICAgICAgcmVtYXJrOiB0aGlzLmZvcm0ucmVtYXJrLAogICAgICAgICAgICBwcm9kdWN0SWQ6IHRoaXMuZm9ybS5wcm9kdWN0SWQsCiAgICAgICAgICAgIHBsYW5uZWRRdHk6IHRoaXMuZm9ybS5wbGFubmVkUXR5CiAgICAgICAgICB9OwogICAgICAgICAgCiAgICAgICAgICBhZGRQcm9kdWN0aW9uUGxhbihkYXRhKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgewogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOwogICAgICAgICAgICAgIHRoaXMuY2FuY2VsKCk7CiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IocmVzcG9uc2UubXNnIHx8ICLmlrDlop7lpLHotKUiKTsKICAgICAgICAgICAgfQogICAgICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgICAgICAvLyDmqKHmi5/miJDlip/lk43lupQKICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIik7CiAgICAgICAgICAgIHRoaXMuY2FuY2VsKCk7CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIAogICAgLy8g5Y+W5raI5oyJ6ZKuCiAgICBjYW5jZWwoKSB7CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsgcGF0aDogIi9zYy9wbGFuIiB9KTsKICAgIH0sCgogICAgLy8g6Kem5Y+R5oyH5a6a5a2X5q6155qE6aqM6K+BCiAgICB0cmlnZ2VyRmllbGRWYWxpZGF0aW9uKGZpZWxkTmFtZSkgewogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgaWYgKHRoaXMuJHJlZnMuZm9ybSkgewogICAgICAgICAgdGhpcy4kcmVmcy5mb3JtLnZhbGlkYXRlRmllbGQoZmllbGROYW1lKTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKCiAgICAvLyDpqozor4HorqHliJLmlbDph48KICAgIHZhbGlkYXRlUGxhbm5lZFF0eShydWxlLCB2YWx1ZSwgY2FsbGJhY2spIHsKICAgICAgaWYgKCF2YWx1ZSkgewogICAgICAgIGNhbGxiYWNrKCk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CgogICAgICBjb25zdCBwbGFubmVkUXR5ID0gTnVtYmVyKHZhbHVlKTsKICAgICAgY29uc3Qgb3JkZXJRdHkgPSBOdW1iZXIodGhpcy5mb3JtLm9yZGVyUXR5KTsKCiAgICAgIGlmIChwbGFubmVkUXR5IDw9IDApIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoJ+iuoeWIkuaVsOmHj+W/hemhu+Wkp+S6jjAnKSk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CgogICAgICBpZiAob3JkZXJRdHkgPiAwICYmIHBsYW5uZWRRdHkgPiBvcmRlclF0eSkgewogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcihg6K6h5YiS5pWw6YeP5LiN6IO95aSn5LqO6K6i5Y2V5pWw6YePKCR7b3JkZXJRdHl9KWApKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIGNhbGxiYWNrKCk7CiAgICB9LAoKICAgIC8vIOmqjOivgeW8gOW3peaXtumXtAogICAgdmFsaWRhdGVTdGFydFRpbWUocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSB7CiAgICAgIGlmICghdmFsdWUpIHsKICAgICAgICBjYWxsYmFjaygpOwogICAgICAgIHJldHVybjsKICAgICAgfQoKICAgICAgY29uc3QgdG9kYXkgPSBuZXcgRGF0ZSgpOwogICAgICB0b2RheS5zZXRIb3VycygwLCAwLCAwLCAwKTsKICAgICAgY29uc3Qgc3RhcnREYXRlID0gbmV3IERhdGUodmFsdWUpOwoKICAgICAgLy8g5byA5bel5pel5pyf5LiN6IO95pep5LqO5b2T5YmN5pel5pyfCiAgICAgIGlmIChzdGFydERhdGUgPCB0b2RheSkgewogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcign5byA5bel5pel5pyf5LiN6IO95pep5LqO5b2T5YmN5pel5pyfJykpOwogICAgICAgIHJldHVybjsKICAgICAgfQoKICAgICAgLy8g5aaC5p6c5a6M5bel5pe26Ze05bey6YCJ5oup77yM5byA5bel5pe26Ze05LiN6IO95pma5LqO5a6M5bel5pe26Ze0CiAgICAgIGlmICh0aGlzLmZvcm0ucGxhbkVuZFRpbWUpIHsKICAgICAgICBjb25zdCBlbmREYXRlID0gbmV3IERhdGUodGhpcy5mb3JtLnBsYW5FbmRUaW1lKTsKICAgICAgICBpZiAoc3RhcnREYXRlID4gZW5kRGF0ZSkgewogICAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCflvIDlt6Xml6XmnJ/kuI3og73mmZrkuo7lrozlt6Xml6XmnJ8nKSk7CiAgICAgICAgICByZXR1cm47CiAgICAgICAgfQogICAgICB9CgogICAgICBjYWxsYmFjaygpOwoKICAgICAgLy8g6Kem5Y+R55u45YWz5a2X5q6155qE6YeN5paw6aqM6K+BCiAgICAgIGlmICh0aGlzLmZvcm0ucGxhbkVuZFRpbWUpIHsKICAgICAgICB0aGlzLnRyaWdnZXJGaWVsZFZhbGlkYXRpb24oJ3BsYW5FbmRUaW1lJyk7CiAgICAgIH0KICAgICAgaWYgKHRoaXMuZm9ybS5yZXF1aXJlZERhdGUpIHsKICAgICAgICB0aGlzLnRyaWdnZXJGaWVsZFZhbGlkYXRpb24oJ3JlcXVpcmVkRGF0ZScpOwogICAgICB9CiAgICB9LAoKICAgIC8vIOmqjOivgeWujOW3peaXtumXtAogICAgdmFsaWRhdGVFbmRUaW1lKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgewogICAgICBpZiAoIXZhbHVlKSB7CiAgICAgICAgY2FsbGJhY2soKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIGNvbnN0IGVuZERhdGUgPSBuZXcgRGF0ZSh2YWx1ZSk7CgogICAgICAvLyDlpoLmnpzlvIDlt6Xml7bpl7Tlt7LpgInmi6nvvIzlrozlt6Xml7bpl7TkuI3og73ml6nkuo7lvIDlt6Xml7bpl7QKICAgICAgaWYgKHRoaXMuZm9ybS5wbGFuU3RhcnRUaW1lKSB7CiAgICAgICAgY29uc3Qgc3RhcnREYXRlID0gbmV3IERhdGUodGhpcy5mb3JtLnBsYW5TdGFydFRpbWUpOwogICAgICAgIGlmIChlbmREYXRlIDwgc3RhcnREYXRlKSB7CiAgICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoJ+WujOW3peaXpeacn+S4jeiDveaXqeS6juW8gOW3peaXpeacnycpKTsKICAgICAgICAgIHJldHVybjsKICAgICAgICB9CiAgICAgIH0KCiAgICAgIGNhbGxiYWNrKCk7CgogICAgICAvLyDop6blj5Hnm7jlhbPlrZfmrrXnmoTph43mlrDpqozor4EKICAgICAgaWYgKHRoaXMuZm9ybS5wbGFuU3RhcnRUaW1lKSB7CiAgICAgICAgdGhpcy50cmlnZ2VyRmllbGRWYWxpZGF0aW9uKCdwbGFuU3RhcnRUaW1lJyk7CiAgICAgIH0KICAgICAgaWYgKHRoaXMuZm9ybS5yZXF1aXJlZERhdGUpIHsKICAgICAgICB0aGlzLnRyaWdnZXJGaWVsZFZhbGlkYXRpb24oJ3JlcXVpcmVkRGF0ZScpOwogICAgICB9CiAgICB9LAoKICAgIC8vIOmqjOivgemcgOaxguaXpeacnwogICAgdmFsaWRhdGVSZXF1aXJlZERhdGUocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSB7CiAgICAgIGlmICghdmFsdWUpIHsKICAgICAgICBjYWxsYmFjaygpOwogICAgICAgIHJldHVybjsKICAgICAgfQoKICAgICAgY29uc3QgcmVxdWlyZWREYXRlID0gbmV3IERhdGUodmFsdWUpOwoKICAgICAgLy8g6ZyA5rGC5pel5pyf5LiN6IO95pep5LqO5b2T5YmN5pel5pyfCiAgICAgIGNvbnN0IHRvZGF5ID0gbmV3IERhdGUoKTsKICAgICAgdG9kYXkuc2V0SG91cnMoMCwgMCwgMCwgMCk7CiAgICAgIGlmIChyZXF1aXJlZERhdGUgPCB0b2RheSkgewogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcign6ZyA5rGC5pel5pyf5LiN6IO95pep5LqO5b2T5YmN5pel5pyfJykpOwogICAgICAgIHJldHVybjsKICAgICAgfQoKICAgICAgLy8g5aaC5p6c5a6M5bel5pe26Ze05bey6YCJ5oup77yM6ZyA5rGC5pel5pyf5LiN6IO95pep5LqO5a6M5bel5pe26Ze0CiAgICAgIGlmICh0aGlzLmZvcm0ucGxhbkVuZFRpbWUpIHsKICAgICAgICBjb25zdCBlbmREYXRlID0gbmV3IERhdGUodGhpcy5mb3JtLnBsYW5FbmRUaW1lKTsKICAgICAgICBpZiAocmVxdWlyZWREYXRlIDwgZW5kRGF0ZSkgewogICAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCfpnIDmsYLml6XmnJ/kuI3og73ml6nkuo7lrozlt6Xml6XmnJ8nKSk7CiAgICAgICAgICByZXR1cm47CiAgICAgICAgfQogICAgICB9CgogICAgICBjYWxsYmFjaygpOwoKICAgICAgLy8g6Kem5Y+R55u45YWz5a2X5q6155qE6YeN5paw6aqM6K+BCiAgICAgIGlmICh0aGlzLmZvcm0ucGxhbkVuZFRpbWUpIHsKICAgICAgICB0aGlzLnRyaWdnZXJGaWVsZFZhbGlkYXRpb24oJ3BsYW5FbmRUaW1lJyk7CiAgICAgIH0KICAgIH0sCgogICAgLy8g57u85ZCI5pel5pyf6YC76L6R6aqM6K+BCiAgICB2YWxpZGF0ZURhdGVMb2dpYygpIHsKICAgICAgY29uc3QgdG9kYXkgPSBuZXcgRGF0ZSgpOwogICAgICB0b2RheS5zZXRIb3VycygwLCAwLCAwLCAwKTsKCiAgICAgIC8vIOajgOafpeW8gOW3peaXpeacnwogICAgICBpZiAodGhpcy5mb3JtLnBsYW5TdGFydFRpbWUpIHsKICAgICAgICBjb25zdCBzdGFydERhdGUgPSBuZXcgRGF0ZSh0aGlzLmZvcm0ucGxhblN0YXJ0VGltZSk7CiAgICAgICAgaWYgKHN0YXJ0RGF0ZSA8IHRvZGF5KSB7CiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi5byA5bel5pel5pyf5LiN6IO95pep5LqO5b2T5YmN5pel5pyfIik7CiAgICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgICAgfQogICAgICB9CgogICAgICAvLyDmo4Dmn6XlvIDlt6Xml6XmnJ/lkozlrozlt6Xml6XmnJ/nmoTlhbPns7sKICAgICAgaWYgKHRoaXMuZm9ybS5wbGFuU3RhcnRUaW1lICYmIHRoaXMuZm9ybS5wbGFuRW5kVGltZSkgewogICAgICAgIGNvbnN0IHN0YXJ0RGF0ZSA9IG5ldyBEYXRlKHRoaXMuZm9ybS5wbGFuU3RhcnRUaW1lKTsKICAgICAgICBjb25zdCBlbmREYXRlID0gbmV3IERhdGUodGhpcy5mb3JtLnBsYW5FbmRUaW1lKTsKICAgICAgICBpZiAoc3RhcnREYXRlID4gZW5kRGF0ZSkgewogICAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuW8gOW3peaXpeacn+S4jeiDveaZmuS6juWujOW3peaXpeacnyIpOwogICAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICAgIH0KICAgICAgfQoKICAgICAgLy8g5qOA5p+l6ZyA5rGC5pel5pyfCiAgICAgIGlmICh0aGlzLmZvcm0ucmVxdWlyZWREYXRlKSB7CiAgICAgICAgY29uc3QgcmVxdWlyZWREYXRlID0gbmV3IERhdGUodGhpcy5mb3JtLnJlcXVpcmVkRGF0ZSk7CiAgICAgICAgaWYgKHJlcXVpcmVkRGF0ZSA8IHRvZGF5KSB7CiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi6ZyA5rGC5pel5pyf5LiN6IO95pep5LqO5b2T5YmN5pel5pyfIik7CiAgICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgICAgfQoKICAgICAgICAvLyDpnIDmsYLml6XmnJ/kuI3og73ml6nkuo7lrozlt6Xml6XmnJ8KICAgICAgICBpZiAodGhpcy5mb3JtLnBsYW5FbmRUaW1lKSB7CiAgICAgICAgICBjb25zdCBlbmREYXRlID0gbmV3IERhdGUodGhpcy5mb3JtLnBsYW5FbmRUaW1lKTsKICAgICAgICAgIGlmIChyZXF1aXJlZERhdGUgPCBlbmREYXRlKSB7CiAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLpnIDmsYLml6XmnJ/kuI3og73ml6nkuo7lrozlt6Xml6XmnJ8iKTsKICAgICAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQoKICAgICAgcmV0dXJuIHRydWU7CiAgICB9CiAgfQp9Owo="}, {"version": 3, "sources": ["add_plan.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0ZA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "add_plan.vue", "sourceRoot": "src/views/sc/plan", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <div class=\"form-container\">\n      <!-- 基础信息区 -->\n      <el-tabs type=\"border-card\">\n        <el-tab-pane>\n          <span slot=\"label\"><i class=\"el-icon-date\"></i> 基础信息</span>\n          <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\n            <el-row :gutter=\"20\">\n              <el-col :span=\"8\">\n                <el-form-item label=\"计划编号\" prop=\"planCode\" required>\n                  <el-input v-model=\"form.planCode\" placeholder=\"请输入\" :disabled=\"isSystemCode\"></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"4\">\n                <el-switch\n                  v-model=\"isSystemCode\"\n                  active-text=\"系统编号\"\n                  inactive-text=\"\"\n                  style=\"margin-top: 13px;\"\n                  @change=\"handleSystemCodeChange\"\n                ></el-switch>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"计划名称\" prop=\"planName\" required>\n                  <el-input v-model=\"form.planName\" placeholder=\"请输入\"></el-input>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-row :gutter=\"20\">\n              <el-col :span=\"12\">\n                <el-form-item label=\"来源类型\" prop=\"sourceType\" required>\n                  <el-select v-model=\"form.sourceType\" placeholder=\"生产订单\" style=\"width: 100%\" @change=\"handleSourceTypeChange\">\n                    <el-option\n                      v-for=\"item in sourceTypeOptions\"\n                      :key=\"item.dictValue\"\n                      :label=\"item.dictLabel\"\n                      :value=\"item.dictValue\"\n                    ></el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"订单编号\" prop=\"orderCode\">\n                  <el-input v-model=\"form.orderCode\" placeholder=\"请输入\">\n                    <el-button\n                      v-if=\"form.sourceType === 'PRODUCTION_ORDER'\"\n                      slot=\"append\"\n                      icon=\"el-icon-search\"\n                      @click=\"openProductionOrderDialog\">\n                      选择\n                    </el-button>\n                  </el-input>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-row :gutter=\"20\">\n              <el-col :span=\"12\">\n                <el-form-item label=\"成品名称\" prop=\"productName\">\n                  <el-input\n                    placeholder=\"请选择成品\"\n                    v-model=\"form.productName\"\n                    class=\"input-with-select\"\n                  >\n                    <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"openProductSelection\"></el-button>\n                  </el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"成品编号\" prop=\"productCode\">\n                  <el-input v-model=\"form.productCode\" placeholder=\"请输入\" disabled></el-input>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-row :gutter=\"20\">\n              <el-col :span=\"12\">\n                <el-form-item label=\"规格型号\" prop=\"specification\">\n                  <el-input v-model=\"form.specification\" placeholder=\"请输入\" disabled></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"单位\" prop=\"unit\">\n                  <el-input v-model=\"form.unit\" placeholder=\"请输入\" disabled></el-input>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-row :gutter=\"20\">\n              <el-col :span=\"12\">\n                <el-form-item label=\"计划数量\" prop=\"plannedQty\" required>\n                  <el-input-number v-model=\"form.plannedQty\" :min=\"1\" controls-position=\"right\" style=\"width: 100%\"></el-input-number>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-row :gutter=\"20\">\n              <el-col :span=\"12\">\n                <el-form-item label=\"开工时间\" prop=\"planStartTime\">\n                  <el-date-picker\n                    v-model=\"form.planStartTime\"\n                    type=\"date\"\n                    placeholder=\"请选择日期\"\n                    style=\"width: 100%\"\n                    value-format=\"yyyy-MM-dd\"\n                  ></el-date-picker>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"完工时间\" prop=\"planEndTime\">\n                  <el-date-picker\n                    v-model=\"form.planEndTime\"\n                    type=\"date\"\n                    placeholder=\"请选择日期\"\n                    style=\"width: 100%\"\n                    value-format=\"yyyy-MM-dd\"\n                  ></el-date-picker>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-row :gutter=\"20\">\n              <el-col :span=\"12\">\n                <el-form-item label=\"需求日期\" prop=\"requiredDate\">\n                  <el-date-picker\n                    v-model=\"form.requiredDate\"\n                    type=\"date\"\n                    placeholder=\"请选择日期\"\n                    style=\"width: 100%\"\n                    value-format=\"yyyy-MM-dd\"\n                  ></el-date-picker>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-row :gutter=\"20\">\n              <el-col :span=\"24\">\n                <el-form-item label=\"备注\" prop=\"remark\">\n                  <el-input\n                    type=\"textarea\"\n                    v-model=\"form.remark\"\n                    placeholder=\"请输入\"\n                    :rows=\"4\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-row :gutter=\"20\">\n              <el-col :span=\"24\">\n                <el-form-item label=\"附件\" prop=\"attachment\">\n                  <div class=\"upload-container\" @click=\"triggerUpload\">\n                    <el-upload\n                      ref=\"upload\"\n                      class=\"upload-hidden\"\n                      action=\"#\"\n                      :http-request=\"uploadFile\"\n                      :file-list=\"fileList\"\n                      :before-upload=\"beforeUpload\"\n                      :on-remove=\"handleRemove\"\n                      multiple\n                      drag\n                    >\n                      <div class=\"upload-area\">\n                        <i class=\"el-icon-upload\"></i>\n                        <div class=\"upload-text\">点击或者拖动文件到虚线框内上传</div>\n                        <div class=\"upload-hint\">支持 docx, xls, PDF, rar, zip, PNG, JPG 等类型的文件</div>\n                      </div>\n                    </el-upload>\n                  </div>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-divider>\n              <span class=\"bom-title\">BOM组成</span>\n            </el-divider>\n            \n            <el-row>\n              <el-col :span=\"24\">\n                <div class=\"bom-container\">\n                  <div class=\"folder-icon\" v-if=\"!selectedBom\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"60\" height=\"60\" viewBox=\"0 0 24 24\" fill=\"#DCDFE6\">\n                      <path d=\"M10 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2h-8l-2-2z\"/>\n                    </svg>\n                  </div>\n                  <div class=\"bom-text\" v-if=\"!selectedBom\">暂无数据</div>\n                  <div class=\"bom-info\" v-else>\n                    <div class=\"bom-header\">\n                      <div class=\"bom-title-info\">\n                        <span>BOM编号：{{ selectedBom.bom_code }}</span>\n                        <span>版本号：{{ selectedBom.bom_version }}</span>\n                      </div>\n                      <el-button type=\"text\" icon=\"el-icon-delete\" @click=\"clearSelectedBom\">清除</el-button>\n                    </div>\n                    <el-table\n                      :data=\"bomDetailList\"\n                      border\n                      size=\"small\"\n                      style=\"width: 100%\"\n                      class=\"bom-detail-table\"\n                    >\n                      <el-table-column label=\"序号\" type=\"index\" width=\"50\" align=\"center\"></el-table-column>\n                      <el-table-column prop=\"material_code\" label=\"物料编码\" min-width=\"120\" align=\"center\"></el-table-column>\n                      <el-table-column prop=\"material_name\" label=\"物料名称\" min-width=\"150\" align=\"center\"></el-table-column>\n                      <el-table-column prop=\"specification\" label=\"规格型号\" min-width=\"100\" align=\"center\"></el-table-column>\n                      <el-table-column prop=\"unit\" label=\"单位\" width=\"60\" align=\"center\"></el-table-column>\n                      <el-table-column prop=\"quantity\" label=\"用量\" width=\"80\" align=\"center\"></el-table-column>\n                      <el-table-column prop=\"remark\" label=\"备注\" min-width=\"120\" align=\"center\"></el-table-column>\n                    </el-table>\n                  </div>\n                  <div class=\"bom-action\">\n                    <el-tooltip :disabled=\"form.productId\" content=\"请先选择成品!\" placement=\"right\" effect=\"light\">\n                      <el-button type=\"primary\" class=\"select-bom-button\" @click=\"selectBom\">选择BOM</el-button>\n                    </el-tooltip>\n                  </div>\n                </div>\n              </el-col>\n            </el-row>\n            \n            <el-row>\n              <el-col :span=\"24\" style=\"text-align: center; margin-top: 20px;\">\n                <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n                <el-button @click=\"cancel\">取 消</el-button>\n              </el-col>\n            </el-row>\n          </el-form>\n        </el-tab-pane>\n      </el-tabs>\n    </div>\n    \n    <!-- 产品选择对话框 -->\n    <el-dialog title=\"选择成品\" :visible.sync=\"productDialogVisible\" width=\"40%\" append-to-body>\n      <el-form :model=\"productQuery\" ref=\"productQueryForm\" :inline=\"true\" class=\"demo-form-inline\" size=\"small\">\n        <el-form-item>\n          <el-input v-model=\"productQuery.keyword\" placeholder=\"请输入产品编号/名称\" clearable style=\"width: 180px;\"></el-input>\n        </el-form-item>\n        <el-form-item>\n          <el-select v-model=\"productQuery.unit\" placeholder=\"请选择单位\" clearable style=\"width: 120px;\">\n            <el-option label=\"个\" value=\"个\"></el-option>\n            <el-option label=\"件\" value=\"件\"></el-option>\n            <el-option label=\"台\" value=\"台\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item>\n          <el-select v-model=\"productQuery.type\" placeholder=\"请选择类型\" clearable style=\"width: 120px;\">\n            <el-option label=\"成品\" value=\"成品\"></el-option>\n            <el-option label=\"半成品\" value=\"半成品\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item>\n          <el-select v-model=\"productQuery.property\" placeholder=\"请选择产品属性\" clearable style=\"width: 120px;\">\n            <el-option label=\"自制\" value=\"自制\"></el-option>\n            <el-option label=\"外购\" value=\"外购\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"small\" @click=\"searchProducts\">查询</el-button>\n          <el-button icon=\"el-icon-refresh\" size=\"small\" @click=\"resetProductQuery\">重置</el-button>\n        </el-form-item>\n      </el-form>\n      \n      <el-table\n        v-loading=\"productLoading\"\n        :data=\"productList\"\n        border\n        size=\"small\"\n        style=\"width: 100%\"\n        @selection-change=\"handleProductSelectionChange\"\n        height=\"300\"\n      >\n        <el-table-column type=\"selection\" width=\"40\" align=\"center\"></el-table-column>\n        <el-table-column label=\"序号\" type=\"index\" width=\"40\" align=\"center\"></el-table-column>\n        <el-table-column prop=\"product_code\" label=\"产品编号\" width=\"90\" align=\"center\"></el-table-column>\n        <el-table-column prop=\"product_name\" label=\"产品名称\" min-width=\"120\" align=\"center\"></el-table-column>\n        <el-table-column prop=\"product_sfn\" label=\"规格型号\" min-width=\"90\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <span :style=\"{color: '#1890ff'}\">{{ scope.row.product_sfn }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"product_unit\" label=\"单位\" width=\"60\" align=\"center\"></el-table-column>\n        <!-- 生产订单产品特有的列 -->\n        <el-table-column v-if=\"form.sourceType === 'PRODUCTION_ORDER'\" prop=\"order_qty\" label=\"订单数量\" width=\"80\" align=\"center\"></el-table-column>\n        <el-table-column v-if=\"form.sourceType === 'PRODUCTION_ORDER'\" prop=\"delivery_date\" label=\"交付日期\" width=\"100\" align=\"center\">\n          <template slot-scope=\"scope\">\n            {{ scope.row.delivery_date ? scope.row.delivery_date.substring(0, 10) : '' }}\n          </template>\n        </el-table-column>\n      </el-table>\n      \n      <div class=\"pagination-container\">\n        <span class=\"total-text\">共 {{ productTotal }} 条</span>\n        <div class=\"pagination-wrapper\">\n          <span class=\"page-size\">\n            <el-select v-model=\"productQuery.pageSize\" size=\"mini\" @change=\"handleProductSizeChange\" style=\"width: 80px;\">\n              <el-option\n                v-for=\"item in [10, 20, 30, 50]\"\n                :key=\"item\"\n                :label=\"`${item}条/页`\"\n                :value=\"item\">\n              </el-option>\n            </el-select>\n          </span>\n          <el-pagination\n            small\n            background\n            @current-change=\"handleProductCurrentChange\"\n            :current-page=\"productQuery.pageNum\"\n            :page-size=\"productQuery.pageSize\"\n            layout=\"prev, pager, next, jumper\"\n            :pager-count=\"5\"\n            :total=\"productTotal\">\n          </el-pagination>\n        </div>\n      </div>\n      \n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button size=\"small\" @click=\"productDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" size=\"small\" @click=\"confirmProductSelect\">确定</el-button>\n      </div>\n    </el-dialog>\n    \n    <!-- BOM选择对话框 -->\n    <el-dialog title=\"选择BOM\" :visible.sync=\"bomDialogVisible\" width=\"40%\" append-to-body>\n      <div class=\"bom-dialog-header\">\n        <div class=\"product-info\">\n          <span>产品名称：{{ form.productName }}</span>\n          <span>产品编号：{{ form.productCode }}</span>\n          <span>规格型号：{{ form.specification }}</span>\n          <span>单位：{{ form.unit }}</span>\n        </div>\n      </div>\n      \n      <el-table\n        v-loading=\"bomLoading\"\n        :data=\"bomList\"\n        border\n        style=\"width: 100%\"\n        @row-click=\"handleBomSelect\"\n        highlight-current-row\n        size=\"small\"\n        height=\"300\"\n      >\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <el-radio v-model=\"selectedBomId\" :label=\"scope.row.bom_id\" @change=\"handleBomSelect(scope.row)\">&nbsp;</el-radio>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"序号\" type=\"index\" width=\"55\" align=\"center\"></el-table-column>\n        <el-table-column prop=\"bom_code\" label=\"BOM编号\" min-width=\"150\" align=\"center\"></el-table-column>\n        <el-table-column prop=\"bom_version\" label=\"版本号\" width=\"100\" align=\"center\"></el-table-column>\n        <el-table-column prop=\"bom_status\" label=\"默认BOM\" width=\"100\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <span>{{ scope.row.bom_status === '1' ? '是' : '否' }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"bom_output\" label=\"日产量\" width=\"100\" align=\"center\"></el-table-column>\n      </el-table>\n      \n      <pagination\n        v-show=\"bomTotal > 0\"\n        :total=\"bomTotal\"\n        :page.sync=\"bomQuery.pageNum\"\n        :limit.sync=\"bomQuery.pageSize\"\n        @pagination=\"getBomList\"\n      />\n      \n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"bomDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"confirmBomSelect\">确定</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 生产订单选择对话框 -->\n    <el-dialog title=\"选择生产订单\" :visible.sync=\"productionOrderDialogVisible\" width=\"800px\" append-to-body>\n      <el-table\n        :data=\"productionOrderList\"\n        @selection-change=\"handleProductionOrderSelectionChange\"\n        style=\"width: 100%\">\n        <el-table-column type=\"selection\" width=\"55\"></el-table-column>\n        <el-table-column prop=\"prodOrderCode\" label=\"订单编号\" width=\"150\"></el-table-column>\n        <el-table-column prop=\"customerName\" label=\"客户名称\" width=\"150\"></el-table-column>\n        <el-table-column prop=\"deliveryDate\" label=\"交付日期\" width=\"120\">\n          <template slot-scope=\"scope\">\n            {{ scope.row.deliveryDate ? scope.row.deliveryDate.substring(0, 10) : '' }}\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"status\" label=\"状态\" width=\"80\">\n          <template slot-scope=\"scope\">\n            <el-tag v-if=\"scope.row.status === '1'\" type=\"warning\">待计划</el-tag>\n            <el-tag v-else-if=\"scope.row.status === '2'\" type=\"primary\">已计划</el-tag>\n            <el-tag v-else-if=\"scope.row.status === '3'\" type=\"success\">生产中</el-tag>\n            <el-tag v-else-if=\"scope.row.status === '4'\" type=\"info\">已完成</el-tag>\n            <el-tag v-else type=\"info\">{{ scope.row.status }}</el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"remark\" label=\"备注\" show-overflow-tooltip></el-table-column>\n      </el-table>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"productionOrderDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"confirmProductionOrderSelect\">确定</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { addProductionPlan } from \"@/api/sc/productionPlan\";\nimport { listProducts, listBomsByProductId, findBomDetails } from \"@/api/basic/product\";\nimport { getAutoNumbers } from \"@/api/basic/numbers\";\nimport Pagination from \"@/components/Pagination\";\n\nexport default {\n  name: \"AddPlan\",\n  components: {\n    Pagination\n  },\n  data() {\n    return {\n      // 表单数据\n      form: {\n        planCode: \"\",\n        planName: \"\",\n        sourceType: \"PRODUCTION_ORDER\",\n        orderCode: \"\",\n        productId: undefined,\n        productName: \"\",\n        productCode: \"\",\n        specification: \"\",\n        unit: \"\",\n        plannedQty: 1,\n        planStartTime: \"\",\n        planEndTime: \"\",\n        requiredDate: \"\",\n        remark: \"\",\n        orderQty: 0  // 添加订单数量字段\n      },\n      // 是否使用系统编号\n      isSystemCode: true,\n      // 表单验证规则\n      rules: {\n        planName: [\n          { required: true, message: \"计划名称不能为空\", trigger: \"blur\" },\n          { max: 50, message: \"长度不能超过50个字符\", trigger: \"blur\" }\n        ],\n        sourceType: [\n          { required: true, message: \"来源类型不能为空\", trigger: \"change\" }\n        ],\n        productName: [\n          { required: true, message: \"请选择产品\", trigger: \"change\" }\n        ],\n        plannedQty: [\n          { required: true, message: \"计划数量不能为空\", trigger: \"blur\" },\n          { validator: this.validatePlannedQty, trigger: \"blur\" }\n        ],\n        planStartTime: [\n          { validator: this.validateStartTime, trigger: \"change\" }\n        ],\n        planEndTime: [\n          { validator: this.validateEndTime, trigger: \"change\" }\n        ],\n        requiredDate: [\n          { validator: this.validateRequiredDate, trigger: \"change\" }\n        ]\n      },\n      // 产品下拉选项\n      productOptions: [],\n      // 产品加载状态\n      productLoading: false,\n      // 来源类型选项\n      sourceTypeOptions: [\n        { dictLabel: \"生产订单\", dictValue: \"PRODUCTION_ORDER\" }\n      ],\n      // 上传文件列表\n      fileList: [],\n      \n      // 产品选择对话框\n      productDialogVisible: false,\n      productQuery: {\n        pageNum: 1,\n        pageSize: 10,\n        keyword: \"\",\n        unit: \"\",\n        type: \"\",\n        property: \"\"\n      },\n      productList: [],\n      productTotal: 0,\n\n      // 生产订单选择对话框\n      productionOrderDialogVisible: false,\n      productionOrderList: [],\n      selectedProductionOrder: null,\n      selectedProduct: null,\n      \n      // BOM选择对话框\n      bomDialogVisible: false,\n      bomQuery: {\n        pageNum: 1,\n        pageSize: 10,\n        productId: undefined\n      },\n      bomList: [],\n      bomTotal: 0,\n      bomLoading: false,\n      selectedBom: null,\n      selectedBomId: null,\n      bomDetailList: [],\n    };\n  },\n  created() {\n    // 初始化时如果是系统编号，则生成计划编号\n    if (this.isSystemCode) {\n      this.generatePlanCode();\n    }\n  },\n  methods: {\n    // 生成计划编号\n    generatePlanCode() {\n      getAutoNumbers(6).then(response => {\n        if (response.code === 200) {\n          this.form.planCode = response.msg;\n        } else {\n          this.$message.error('获取计划编号失败');\n        }\n      }).catch(() => {\n        this.$message.error('获取计划编号失败');\n      });\n    },\n    \n    // 处理系统编号开关变化\n    handleSystemCodeChange(val) {\n      if (val) {\n        // 如果开启系统编号，则生成编号\n        this.generatePlanCode();\n      } else {\n        // 如果关闭系统编号，则清空编号\n        this.form.planCode = '';\n      }\n    },\n\n    // 处理来源类型变化\n    handleSourceTypeChange(val) {\n      // 清空订单编号\n      this.form.orderCode = \"\";\n      // 如果选择生产订单，清空产品相关信息\n      if (val === 'PRODUCTION_ORDER') {\n        this.form.productId = undefined;\n        this.form.productName = \"\";\n        this.form.productCode = \"\";\n        this.form.specification = \"\";\n        this.form.unit = \"\";\n      }\n    },\n\n    // 打开生产订单选择对话框\n    openProductionOrderDialog() {\n      this.productionOrderDialogVisible = true;\n      this.getProductionOrderList();\n    },\n\n    // 获取生产订单列表\n    getProductionOrderList() {\n      // 这里调用生产订单列表API\n      import(\"@/api/sc/productionOrder\").then(api => {\n        api.listProductionOrder({}).then(response => {\n          this.productionOrderList = response.rows || [];\n        }).catch(() => {\n          this.$message.error('获取生产订单列表失败');\n        });\n      });\n    },\n\n    // 处理生产订单选择变化\n    handleProductionOrderSelectionChange(selection) {\n      this.selectedProductionOrder = selection.length > 0 ? selection[0] : null;\n    },\n\n    // 确认选择生产订单\n    confirmProductionOrderSelect() {\n      if (!this.selectedProductionOrder) {\n        this.$message.warning('请选择一个生产订单');\n        return;\n      }\n\n      // 设置订单编号\n      this.form.orderCode = this.selectedProductionOrder.prodOrderCode;\n\n      // 调用API根据生产订单创建计划模板\n      import(\"@/api/sc/productionPlan\").then(api => {\n        api.createPlanFromOrder(this.selectedProductionOrder.productionOrderId).then(response => {\n          if (response.code === 200) {\n            const planData = response.data;\n            // 填充表单数据\n            this.form.planName = planData.planName;\n            this.form.productId = planData.productId;\n            this.form.plannedQty = planData.plannedQty;\n            this.form.orderQty = planData.plannedQty; // 保存订单数量用于验证\n            this.form.planStartTime = planData.planStartTime;\n            this.form.planEndTime = planData.planEndTime;\n            this.form.requiredDate = planData.requiredDate;\n            this.form.remark = planData.remark;\n\n            // 如果有产品信息，需要获取产品详情\n            if (planData.productId) {\n              this.getProductDetails(planData.productId);\n            }\n\n            this.$message.success('已关联生产订单，请完善其他信息');\n          }\n        }).catch(() => {\n          this.$message.error('关联生产订单失败');\n        });\n      });\n\n      this.productionOrderDialogVisible = false;\n    },\n\n    // 获取产品详情\n    getProductDetails(productId) {\n      listProducts({ productId: productId }).then(response => {\n        if (response.rows && response.rows.length > 0) {\n          const product = response.rows[0];\n          this.form.productName = product.product_name;\n          this.form.productCode = product.product_code;\n          this.form.specification = product.product_sfn;\n          this.form.unit = product.product_unit;\n        }\n      }).catch(() => {\n        console.error('获取产品详情失败');\n      });\n    },\n    \n    // 触发上传\n    triggerUpload() {\n      this.$refs.upload.$el.click();\n    },\n    \n    // 打开产品选择弹窗\n    openProductSelection() {\n      if (this.form.sourceType === 'PRODUCTION_ORDER') {\n        // 如果选择了生产订单，但没有选择具体订单\n        if (!this.form.orderCode) {\n          this.$message.warning('请先选择生产订单');\n          return;\n        }\n        // 显示该订单的产品列表\n        this.getProductsByOrder();\n      } else {\n        // 其他情况显示全部产品\n        this.getProductList();\n      }\n      this.productDialogVisible = true;\n    },\n    \n    // 获取产品列表\n    getProductList() {\n      this.productLoading = true;\n      listProducts({\n        pageNum: this.productQuery.pageNum,\n        pageSize: this.productQuery.pageSize,\n        keyword: this.productQuery.keyword,\n        productUnit: this.productQuery.unit,\n        productType: this.productQuery.type,\n        productProperty: this.productQuery.property\n      }).then(response => {\n        this.productLoading = false;\n        if (response.code === 200) {\n          this.productList = response.rows;\n          this.productTotal = response.total;\n        }\n      }).catch(() => {\n        this.productLoading = false;\n        // 模拟数据\n        this.productList = [\n          { product_id: 1, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' },\n          { product_id: 2, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' },\n          { product_id: 3, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' },\n          { product_id: 4, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' },\n          { product_id: 5, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' }\n        ];\n        this.productTotal = 50;\n      });\n    },\n\n    // 根据生产订单获取产品列表\n    getProductsByOrder() {\n      if (!this.selectedProductionOrder) {\n        this.$message.error('未找到选中的生产订单信息');\n        return;\n      }\n\n      this.productLoading = true;\n      // 调用API获取订单产品明细\n      import(\"@/api/sc/productionOrder\").then(api => {\n        api.getProductionOrderDetails(this.selectedProductionOrder.productionOrderId).then(response => {\n          if (response.code === 200 && response.data && response.data.products) {\n            // 将订单明细转换为产品列表格式\n            this.productList = response.data.products.map(detail => ({\n              product_id: detail.productId,\n              product_name: detail.productName,\n              product_code: detail.productCode,\n              product_sfn: detail.productSfn,\n              product_unit: detail.productUnit,\n              // 添加订单相关信息\n              order_qty: detail.qtyNum,\n              delivery_date: detail.deliveryDate\n            }));\n            this.productTotal = this.productList.length;\n          } else {\n            this.$message.warning('该订单暂无产品明细');\n            this.productList = [];\n            this.productTotal = 0;\n          }\n          this.productLoading = false;\n        }).catch(() => {\n          this.$message.error('获取订单产品列表失败');\n          this.productLoading = false;\n        });\n      });\n    },\n\n    // 搜索产品\n    searchProducts() {\n      this.productQuery.pageNum = 1;\n      this.getProductList();\n    },\n    \n    // 重置产品查询条件\n    resetProductQuery() {\n      this.productQuery = {\n        pageNum: 1,\n        pageSize: 10,\n        keyword: \"\",\n        unit: \"\",\n        type: \"\",\n        property: \"\"\n      };\n      this.getProductList();\n    },\n    \n    // 处理产品表格选择变化\n    handleProductSelectionChange(selection) {\n      if (selection.length > 0) {\n        this.selectedProduct = selection[0];\n      } else {\n        this.selectedProduct = null;\n      }\n    },\n    \n    // 处理产品页码变化\n    handleProductCurrentChange(currentPage) {\n      this.productQuery.pageNum = currentPage;\n      this.getProductList();\n    },\n    \n    // 处理产品每页条数变化\n    handleProductSizeChange(size) {\n      this.productQuery.pageSize = size;\n      this.productQuery.pageNum = 1;\n      this.getProductList();\n    },\n    \n    // 确认产品选择\n    confirmProductSelect() {\n      if (this.selectedProduct) {\n        this.form.productId = this.selectedProduct.product_id;\n        this.form.productName = this.selectedProduct.product_name;\n        this.form.productCode = this.selectedProduct.product_code;\n        this.form.specification = this.selectedProduct.product_sfn;\n        this.form.unit = this.selectedProduct.product_unit;\n\n        // 如果是从生产订单选择的产品，自动填充订单数量和交付日期\n        if (this.form.sourceType === 'PRODUCTION_ORDER' && this.selectedProduct.order_qty) {\n          this.form.plannedQty = this.selectedProduct.order_qty;\n          if (this.selectedProduct.delivery_date) {\n            this.form.requiredDate = this.selectedProduct.delivery_date;\n            this.form.planEndTime = this.selectedProduct.delivery_date;\n          }\n        }\n\n        this.productDialogVisible = false;\n\n        // 清空已选BOM\n        this.selectedBom = null;\n        this.selectedBomId = null;\n      } else {\n        this.$message.warning('请选择一个产品！');\n      }\n    },\n    \n    // 选择BOM\n    selectBom() {\n      if (!this.form.productId) {\n        this.$message.warning('请先选择成品！');\n        return;\n      }\n      this.bomDialogVisible = true;\n      this.getBomList();\n    },\n    \n    // 获取BOM列表\n    getBomList() {\n      this.bomLoading = true;\n      listBomsByProductId(this.form.productId).then(response => {\n        this.bomLoading = false;\n        if (response.code === 200) {\n          this.bomList = response.rows;\n          this.bomTotal = response.total;\n          if (!this.bomList || this.bomList.length === 0) {\n            this.$message.info(\"未找到该产品的BOM信息\");\n          } else {\n            // 如果有默认BOM，则自动选中\n            const defaultBom = this.bomList.find(b => b.bom_status === '1');\n            if (defaultBom) {\n              this.handleBomSelect(defaultBom);\n            }\n          }\n        } else {\n          this.bomList = [];\n          this.bomTotal = 0;\n        }\n      }).catch(() => {\n        this.bomLoading = false;\n        this.$message.error('获取BOM列表失败');\n      });\n    },\n    \n    // 处理BOM行选择\n    handleBomSelect(row) {\n      this.selectedBom = row;\n      this.selectedBomId = row.bom_id;\n    },\n    \n    // 确认BOM选择\n    confirmBomSelect() {\n      if (this.selectedBom) {\n        this.bomDialogVisible = false;\n        // 获取BOM详情\n        this.getBomDetail();\n      } else {\n        this.$message.warning('请选择一个BOM！');\n      }\n    },\n    \n    // 获取BOM详情\n    getBomDetail() {\n      findBomDetails(this.selectedBom.bom_id).then(response => {\n        console.log(\"成功获取BOM详情响应:\", response);\n        if (response && response.code === 200) {\n          this.bomDetailList = response.rows;\n        } else {\n          this.bomDetailList = [];\n          this.$message.error(\"获取BOM详情失败: \" + (response ? response.msg : '无响应'));\n        }\n      }).catch(error => {\n        console.error(\"获取BOM详情接口调用失败:\", error);\n        this.$message.error(\"获取BOM详情接口调用失败\");\n        this.bomDetailList = [];\n      });\n    },\n    \n    // 清除已选BOM\n    clearSelectedBom() {\n      this.selectedBom = null;\n      this.selectedBomId = null;\n      this.bomDetailList = [];\n    },\n    \n    // 上传前检查文件类型和大小\n    beforeUpload(file) {\n      const isValidType = /\\.(doc|docx|xls|xlsx|pdf|rar|zip|png|jpg|jpeg)$/i.test(file.name);\n      const isLt10M = file.size / 1024 / 1024 < 10;\n\n      if (!isValidType) {\n        this.$message.error('上传文件格式不支持!');\n        return false;\n      }\n      if (!isLt10M) {\n        this.$message.error('上传文件大小不能超过 10MB!');\n        return false;\n      }\n      return true;\n    },\n    \n    // 上传文件处理\n    uploadFile(options) {\n      // 这里应该调用实际的文件上传API\n      console.log('文件上传:', options.file);\n      // 假设上传成功\n      this.fileList.push({\n        name: options.file.name,\n        url: URL.createObjectURL(options.file)\n      });\n      options.onSuccess();\n    },\n    \n    // 移除文件\n    handleRemove(file) {\n      const index = this.fileList.indexOf(file);\n      if (index !== -1) {\n        this.fileList.splice(index, 1);\n      }\n    },\n    \n    // 表单提交\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          // 额外的日期逻辑验证\n          if (!this.validateDateLogic()) {\n            return;\n          }\n\n          // 表单验证通过，调用API提交数据\n          const data = {\n            planCode: this.form.planCode,\n            planName: this.form.planName,\n            sourceType: this.form.sourceType,\n            orderCode: this.form.orderCode,\n            planStartTime: this.form.planStartTime,\n            planEndTime: this.form.planEndTime,\n            requiredDate: this.form.requiredDate,\n            remark: this.form.remark,\n            productId: this.form.productId,\n            plannedQty: this.form.plannedQty\n          };\n          \n          addProductionPlan(data).then(response => {\n            if (response.code === 200) {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.cancel();\n            } else {\n              this.$modal.msgError(response.msg || \"新增失败\");\n            }\n          }).catch(() => {\n            // 模拟成功响应\n            this.$modal.msgSuccess(\"新增成功\");\n            this.cancel();\n          });\n        }\n      });\n    },\n    \n    // 取消按钮\n    cancel() {\n      this.$router.push({ path: \"/sc/plan\" });\n    },\n\n    // 触发指定字段的验证\n    triggerFieldValidation(fieldName) {\n      this.$nextTick(() => {\n        if (this.$refs.form) {\n          this.$refs.form.validateField(fieldName);\n        }\n      });\n    },\n\n    // 验证计划数量\n    validatePlannedQty(rule, value, callback) {\n      if (!value) {\n        callback();\n        return;\n      }\n\n      const plannedQty = Number(value);\n      const orderQty = Number(this.form.orderQty);\n\n      if (plannedQty <= 0) {\n        callback(new Error('计划数量必须大于0'));\n        return;\n      }\n\n      if (orderQty > 0 && plannedQty > orderQty) {\n        callback(new Error(`计划数量不能大于订单数量(${orderQty})`));\n        return;\n      }\n\n      callback();\n    },\n\n    // 验证开工时间\n    validateStartTime(rule, value, callback) {\n      if (!value) {\n        callback();\n        return;\n      }\n\n      const today = new Date();\n      today.setHours(0, 0, 0, 0);\n      const startDate = new Date(value);\n\n      // 开工日期不能早于当前日期\n      if (startDate < today) {\n        callback(new Error('开工日期不能早于当前日期'));\n        return;\n      }\n\n      // 如果完工时间已选择，开工时间不能晚于完工时间\n      if (this.form.planEndTime) {\n        const endDate = new Date(this.form.planEndTime);\n        if (startDate > endDate) {\n          callback(new Error('开工日期不能晚于完工日期'));\n          return;\n        }\n      }\n\n      callback();\n\n      // 触发相关字段的重新验证\n      if (this.form.planEndTime) {\n        this.triggerFieldValidation('planEndTime');\n      }\n      if (this.form.requiredDate) {\n        this.triggerFieldValidation('requiredDate');\n      }\n    },\n\n    // 验证完工时间\n    validateEndTime(rule, value, callback) {\n      if (!value) {\n        callback();\n        return;\n      }\n\n      const endDate = new Date(value);\n\n      // 如果开工时间已选择，完工时间不能早于开工时间\n      if (this.form.planStartTime) {\n        const startDate = new Date(this.form.planStartTime);\n        if (endDate < startDate) {\n          callback(new Error('完工日期不能早于开工日期'));\n          return;\n        }\n      }\n\n      callback();\n\n      // 触发相关字段的重新验证\n      if (this.form.planStartTime) {\n        this.triggerFieldValidation('planStartTime');\n      }\n      if (this.form.requiredDate) {\n        this.triggerFieldValidation('requiredDate');\n      }\n    },\n\n    // 验证需求日期\n    validateRequiredDate(rule, value, callback) {\n      if (!value) {\n        callback();\n        return;\n      }\n\n      const requiredDate = new Date(value);\n\n      // 需求日期不能早于当前日期\n      const today = new Date();\n      today.setHours(0, 0, 0, 0);\n      if (requiredDate < today) {\n        callback(new Error('需求日期不能早于当前日期'));\n        return;\n      }\n\n      // 如果完工时间已选择，需求日期不能早于完工时间\n      if (this.form.planEndTime) {\n        const endDate = new Date(this.form.planEndTime);\n        if (requiredDate < endDate) {\n          callback(new Error('需求日期不能早于完工日期'));\n          return;\n        }\n      }\n\n      callback();\n\n      // 触发相关字段的重新验证\n      if (this.form.planEndTime) {\n        this.triggerFieldValidation('planEndTime');\n      }\n    },\n\n    // 综合日期逻辑验证\n    validateDateLogic() {\n      const today = new Date();\n      today.setHours(0, 0, 0, 0);\n\n      // 检查开工日期\n      if (this.form.planStartTime) {\n        const startDate = new Date(this.form.planStartTime);\n        if (startDate < today) {\n          this.$modal.msgError(\"开工日期不能早于当前日期\");\n          return false;\n        }\n      }\n\n      // 检查开工日期和完工日期的关系\n      if (this.form.planStartTime && this.form.planEndTime) {\n        const startDate = new Date(this.form.planStartTime);\n        const endDate = new Date(this.form.planEndTime);\n        if (startDate > endDate) {\n          this.$modal.msgError(\"开工日期不能晚于完工日期\");\n          return false;\n        }\n      }\n\n      // 检查需求日期\n      if (this.form.requiredDate) {\n        const requiredDate = new Date(this.form.requiredDate);\n        if (requiredDate < today) {\n          this.$modal.msgError(\"需求日期不能早于当前日期\");\n          return false;\n        }\n\n        // 需求日期不能早于完工日期\n        if (this.form.planEndTime) {\n          const endDate = new Date(this.form.planEndTime);\n          if (requiredDate < endDate) {\n            this.$modal.msgError(\"需求日期不能早于完工日期\");\n            return false;\n          }\n        }\n      }\n\n      return true;\n    }\n  }\n};\n</script>\n\n<style scoped>\n.form-container {\n  background-color: #fff;\n  padding: 10px;\n}\n\n.el-tabs--border-card {\n  box-shadow: none;\n}\n\n.upload-container {\n  width: 100%;\n  border: 1px dashed #d9d9d9;\n  border-radius: 6px;\n  text-align: center;\n  padding: 20px 0;\n  cursor: pointer;\n}\n\n.upload-container:hover {\n  border-color: #409EFF;\n}\n\n.upload-area {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\n.upload-area .el-icon-upload {\n  font-size: 48px;\n  color: #c0c4cc;\n  margin-bottom: 10px;\n}\n\n.upload-text {\n  font-size: 14px;\n  color: #606266;\n  margin-bottom: 10px;\n}\n\n.upload-hint {\n  font-size: 12px;\n  color: #909399;\n}\n\n.input-with-select .el-input-group__append {\n  background-color: #fff;\n}\n\n.bom-title {\n  font-size: 16px;\n  font-weight: bold;\n  color: #303133;\n}\n\n.bom-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  margin: 20px 0;\n  padding: 30px 0;\n}\n\n.folder-icon {\n  margin-bottom: 20px;\n}\n\n.bom-text {\n  font-size: 14px;\n  color: #909399;\n  margin-bottom: 20px;\n}\n\n.warning-text {\n  color: #E6A23C;\n  font-size: 12px;\n  margin-left: 10px;\n}\n\n.warning-text i {\n  margin-right: 5px;\n}\n\n.upload-hidden {\n  width: 100%;\n  height: 100%;\n}\n\n.upload-hidden >>> .el-upload {\n  width: 100%;\n}\n\n.upload-hidden >>> .el-upload-dragger {\n  width: 100%;\n  height: 100%;\n  border: none;\n  padding: 0;\n  margin: 0;\n}\n\n.bom-dialog-header {\n  margin-bottom: 15px;\n  padding: 10px;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n}\n\n.product-info {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.product-info span {\n  margin-right: 20px;\n  line-height: 30px;\n}\n\n.el-radio {\n  margin-right: 0;\n}\n\n.pagination-container {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 0;\n  font-size: 12px;\n}\n\n.pagination-wrapper {\n  display: flex;\n  align-items: center;\n}\n\n.page-size {\n  margin-right: 10px;\n}\n\n.total-text {\n  color: #606266;\n  font-size: 12px;\n}\n\n.bom-info {\n  width: 100%;\n  margin-bottom: 20px;\n}\n\n.bom-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n  padding: 8px;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n}\n\n.bom-title-info {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.bom-title-info span {\n  margin-right: 20px;\n  font-weight: bold;\n}\n\n.bom-detail-table {\n  margin-bottom: 15px;\n}\n\n.bom-action {\n  display: flex;\n  align-items: center;\n}\n\n.select-bom-button {\n  margin-right: 10px;\n}\n</style>\n"]}]}