package com.ruoyi.system.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

@Data
@TableName("basic_suppliers")
public class BasicSuppliers extends BaseEntity {
    @TableId(value ="supplier_id",type = IdType.AUTO)
    private Long supplier_id; // 供应商id
    private String supplier_code; // 供应商编号
    private String supplier_name; // 供应商名称
    private String supplier_level; // 供应商等级
    private String supplier_person; // 联系人
    private String supplier_phone; // 联系电话
    private String phone_status; // 联系电话是否启用
    private String supplier_status; // 供应商状态
    private String remarks; // 备注
    private String is_delete; // 逻辑删除
    private String create_by;
    private Date create_time;
    private String update_by;
    private Date update_time;
}
