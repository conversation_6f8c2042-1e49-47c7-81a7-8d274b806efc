package com.cssl.contrller;

import com.cssl.pojo.BasicBom;
import com.cssl.pojo.BasicBomdetails;
import com.cssl.pojo.vo.BasicBomdetailsVo;
import com.cssl.service.BasicBomService;
import com.cssl.service.BasicBomdetailsService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/bmd")
public class BasicBomdetailsContrller extends BaseController {
    @Resource
    private BasicBomdetailsService basicBomdetailsService;

    @Resource
    private BasicBomService basicBomService;

    //查询Bom物料信息
    @PostMapping("/list1")
    public TableDataInfo list1(BasicBomdetails basicBomdetails)
    {
        List<BasicBomdetails> list =basicBomdetailsService.listBasicBomdetails(basicBomdetails);
        return getDataTable(list);
    }


    //删除Bom明细表信息
    @Log(title = "删除Bom明细表信息", businessType = BusinessType.UPDATE)
    @PutMapping("/dp1")
    public AjaxResult edit1(@RequestParam("bomdetails_id") Long bomdetails_id)
    {
        return toAjax(basicBomdetailsService.deleteBasicBomdetails(bomdetails_id));
    }


    //根据物料id或产品id查询BOM物料信息
    @PostMapping("/find")
    public TableDataInfo find(@RequestBody BasicBomdetails basicBomdetails,Map<String, Object> params)
    {
        System.out.println("sss:"+basicBomdetails.getBom_id());

        // 检查 bom_id 是否存在
        if (basicBomdetails == null || basicBomdetails.getBom_id() == null) {
            return getDataTable(new ArrayList<>());
        }

        // 步骤 1: 根据 bom_id 获取初始的BOM详情列表
        List<BasicBomdetails> initialDetails = basicBomdetailsService.listBasicBomdetails(basicBomdetails);
        if (initialDetails == null || initialDetails.isEmpty()) {
            return getDataTable(new ArrayList<>());
        }

        // 步骤 2: 从初始列表中收集所有物料ID
        List<Long> materialIds = new ArrayList<>();
        for (BasicBomdetails detail : initialDetails) {
            if (detail.getMaterial_id() != null) {
                materialIds.add(detail.getMaterial_id());
            }
        }

        if (materialIds.isEmpty()) {
            return getDataTable(new ArrayList<>());
        }

        // 步骤 3: 使用收集到的物料ID列表来查询完整的、关联了物料信息的BOM详情
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("material_ids", materialIds);
        queryParams.put("bom_id",basicBomdetails.getBom_id());
        List<BasicBomdetails> finalDetails = basicBomdetailsService.selectBasicBomdetailsByMaterialId(queryParams);

        return getDataTable(finalDetails);
    }


    //查询Bom头部信息
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody BasicBom basicBom)
    {
        startPage();
        List<BasicBom> list =basicBomService.listBasicBom(basicBom);
        return getDataTable(list);
    }

    //添加Bom头部信息
    @Log(title = "添加Bom头部信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasicBom basicBom)
    {

        return toAjax(basicBomService.addBasicBom(basicBom));
    }

    //修改Bom头部信息
    @Log(title = "修改Bom头部信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasicBom basicBom)
    {
        return toAjax(basicBomService.updateBasicBom(basicBom));
    }

    //删除Bom头部信息
    @Log(title = "删除Bom头部信息", businessType = BusinessType.UPDATE)
    @PutMapping("/dp/{bom_id}")
    public AjaxResult edit(@PathVariable Long bom_id)
    {
        return toAjax(basicBomService.deleteBasicBom(bom_id));
    }

    @PostMapping("/detail/{bom_id}")
    public TableDataInfo detail(@PathVariable Long bom_id)
    {
        List<BasicBomdetailsVo> list =basicBomdetailsService.selectBasicBomdetailsVoByBomId(bom_id);
        return getDataTable(list);
    }


    //批量删除
    @Log(title = "批量删除", businessType = BusinessType.UPDATE)
    @PutMapping("/batch/{bom_ids}")
    public AjaxResult edit(@PathVariable List<Long> bom_ids)
    {
        return toAjax(basicBomService.deleteBasicBomByIds(bom_ids));
    }

    //查询Bom头部详细信息
    @PostMapping("/find/{bom_id}")
    public TableDataInfo find(@PathVariable Long bom_id)
    {

        List<BasicBom> list =basicBomService.getBasicBom(bom_id);
        return getDataTable(list);
    }

    @PostMapping("/find1")
    public TableDataInfo find1(@RequestBody BasicBomdetails basicBomdetails,Map<String, Object> params)
    {
        System.out.println("sss:"+basicBomdetails.getBom_id());

        // 检查 bom_id 是否存在
        if (basicBomdetails == null || basicBomdetails.getBom_id() == null) {
            return getDataTable(new ArrayList<>());
        }

        // 步骤 1: 根据 bom_id 获取初始的BOM详情列表
        List<BasicBomdetails> initialDetails = basicBomdetailsService.listBasicBomdetails(basicBomdetails);
        if (initialDetails == null || initialDetails.isEmpty()) {
            return getDataTable(new ArrayList<>());
        }

        // 步骤 2: 从初始列表中收集所有物料ID
        List<Long> operationalIds = new ArrayList<>();
        for (BasicBomdetails detail : initialDetails) {
            if (detail.getOperational_id() != null) {
                operationalIds.add(detail.getOperational_id());
            }
        }

        if (operationalIds.isEmpty()) {
            return getDataTable(new ArrayList<>());
        }

        // 步骤 3: 使用收集到的物料ID列表来查询完整的、关联了物料信息的BOM详情
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("operational_ids", operationalIds);
        queryParams.put("bom_id",basicBomdetails.getBom_id());
        System.out.println("kk:"+queryParams);
        List<BasicBomdetails> finalDetails = basicBomdetailsService.selectBasicBomdetailsVoByOperationalId(queryParams);

        return getDataTable(finalDetails);
    }

    //查询工艺路线为空的产品
    @PostMapping("/pro")
    public TableDataInfo pro(@RequestBody BasicBomdetailsVo basicBomdetailsVo)
    {
        startPage();
        List<BasicBomdetailsVo> list =basicBomdetailsService.selectBasicBomdetailsVoByOperationalIdIsNull(basicBomdetailsVo);

        return getDataTable(list);
    }



}
