/*
 Navicat Premium Data Transfer

 Source Server         : yunSQL
 Source Server Type    : MySQL
 Source Server Version : 50744
 Source Host           : ***************:3322
 Source Schema         : ldzlmes

 Target Server Type    : MySQL
 Target Server Version : 50744
 File Encoding         : 65001

 Date: 02/07/2025 15:47:31
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for basic_wlfl
-- ----------------------------
DROP TABLE IF EXISTS `basic_wlfl`;
CREATE TABLE `basic_wlfl`  (
  `material_classification_id` bigint(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `material_classification_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分类编码',
  `material_classification_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分类名称',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新者',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `is_delete` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '1' COMMENT '逻辑删除',
  PRIMARY KEY (`material_classification_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of basic_wlfl
-- ----------------------------
INSERT INTO `basic_wlfl` VALUES (1, 'WLFL001', '钢板类', 'admin', '2025-06-25 15:14:04', 'admin', '2025-06-25 15:14:04', '0');
INSERT INTO `basic_wlfl` VALUES (2, 'WLFL002', '型钢类', 'admin', '2025-06-25 15:14:04', 'admin', '2025-06-25 15:14:04', '0');
INSERT INTO `basic_wlfl` VALUES (3, 'WLFL003', '管材类', 'admin', '2025-06-25 15:14:04', 'admin', '2025-06-25 15:14:04', '0');
INSERT INTO `basic_wlfl` VALUES (4, 'WLFL004', '建筑钢材类', 'admin', '2025-06-25 15:14:04', 'admin', '2025-06-25 15:14:04', '0');
INSERT INTO `basic_wlfl` VALUES (5, 'WLFL005', '钢结构类', 'admin', '2025-06-25 15:14:04', 'admin', '2025-06-25 15:14:04', '0');
INSERT INTO `basic_wlfl` VALUES (6, 'WLFL006', '钢丝制品类', 'admin', '2025-06-25 15:14:04', 'admin', '2025-06-25 15:14:04', '0');
INSERT INTO `basic_wlfl` VALUES (7, 'WLFL007', '有色金属类', 'admin', '2025-06-25 15:14:04', 'admin', '2025-06-25 15:14:04', '0');
INSERT INTO `basic_wlfl` VALUES (8, 'WLFL008', '焊接材料类', 'admin', '2025-06-25 15:14:04', 'admin', '2025-06-25 15:14:04', '0');
INSERT INTO `basic_wlfl` VALUES (9, 'WLFL009', '紧固件类', 'admin', '2025-06-25 15:14:04', 'admin', '2025-06-25 15:14:04', '0');
INSERT INTO `basic_wlfl` VALUES (10, 'WLFL010', '工具材料类', 'admin', '2025-06-25 15:14:04', 'admin', '2025-06-25 15:14:04', '0');

SET FOREIGN_KEY_CHECKS = 1;
