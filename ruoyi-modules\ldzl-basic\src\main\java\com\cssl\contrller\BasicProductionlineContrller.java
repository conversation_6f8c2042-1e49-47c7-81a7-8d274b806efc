package com.cssl.contrller;

import com.cssl.pojo.BasicCustomers;
import com.cssl.pojo.BasicProductionline;
import com.cssl.pojo.BasicWorkshop;
import com.cssl.service.BasicProductionlineService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/prt")
public class BasicProductionlineContrller extends BaseController {
    @Resource
    private BasicProductionlineService basicProductionlineService;

    //查询所有生产线
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody BasicProductionline basicProductionline)
    {
        startPage();
        List<BasicProductionline> list =basicProductionlineService.listBasicProductionline(basicProductionline);
        return getDataTable(list);
    }

    //添加生产线信息
    @Log(title = "添加生产线信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasicProductionline basicProductionline)
    {
        return toAjax(basicProductionlineService.addBasicProductionline(basicProductionline));
    }

    //修改生产线信息
    @Log(title = "修改生产线信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasicProductionline basicProductionline)
    {
        return toAjax(basicProductionlineService.updateBasicProductionline(basicProductionline));
    }

    //删除生产线信息
    @Log(title = "删除生产线", businessType = BusinessType.UPDATE)
    @PutMapping("/dp/{production_line_id}")
    public AjaxResult edit1(@PathVariable Long production_line_id)
    {
        return toAjax(basicProductionlineService.delBasicProductionline(production_line_id));
    }

    @PostMapping("/find")
    public TableDataInfo find(@RequestBody BasicProductionline basicProductionline)
    {

        List<BasicProductionline> list =basicProductionlineService.listBasicProductionline(basicProductionline);
        return getDataTable(list);
    }

}
