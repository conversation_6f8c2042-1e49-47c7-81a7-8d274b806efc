package com.ruoyi.system.api.factory;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.system.api.RemoteScTaskService;
import com.ruoyi.system.api.domain.ScTaskDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * 生产任务服务降级处理
 * 
 * <AUTHOR>
 */
@Component
public class RemoteScTaskFallbackFactory implements FallbackFactory<RemoteScTaskService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteScTaskFallbackFactory.class);

    @Override
    public RemoteScTaskService create(Throwable cause) {
        log.error("生产任务服务调用失败:{}", cause.getMessage());
        return new RemoteScTaskService() {
            @Override
            public R<List<ScTaskDto>> getTaskListForQc(String workOrderCode, String workOrderName, 
                    String processCode, String taskName, String taskCode, String status, String source) {
                return R.fail("获取生产任务列表失败:" + cause.getMessage());
            }
        };
    }
}
