/*
 Navicat Premium Data Transfer

 Source Server         : yunSQL
 Source Server Type    : MySQL
 Source Server Version : 50744
 Source Host           : ***************:3322
 Source Schema         : ldzlmes

 Target Server Type    : MySQL
 Target Server Version : 50744
 File Encoding         : 65001

 Date: 02/07/2025 15:47:38
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for basic_wlflz
-- ----------------------------
DROP TABLE IF EXISTS `basic_wlflz`;
CREATE TABLE `basic_wlflz`  (
  `material_subcategory_id` bigint(11) NOT NULL AUTO_INCREMENT COMMENT '子id',
  `material_subcategory_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '子分类编码',
  `material_subcategory_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '子分类名称',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `is_delete` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '1' COMMENT '逻辑删除',
  `material_classification_id` bigint(11) NULL DEFAULT NULL COMMENT '父id',
  PRIMARY KEY (`material_subcategory_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 21 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of basic_wlflz
-- ----------------------------
INSERT INTO `basic_wlflz` VALUES (1, 'WLFLZ001', '中厚板', 'admin', '2025-06-25 15:14:04', 'admin', '2025-06-25 15:14:04', '0', 1);
INSERT INTO `basic_wlflz` VALUES (2, 'WLFLZ002', '薄板', 'admin', '2025-06-25 15:14:04', 'admin', '2025-06-25 15:14:04', '0', 1);
INSERT INTO `basic_wlflz` VALUES (3, 'WLFLZ003', '镀锌板', 'admin', '2025-06-25 15:14:04', 'admin', '2025-06-25 15:14:04', '0', 1);
INSERT INTO `basic_wlflz` VALUES (4, 'WLFLZ004', '不锈钢', 'admin', '2025-06-25 15:14:04', 'admin', '2025-06-25 15:14:04', '0', 1);
INSERT INTO `basic_wlflz` VALUES (5, 'WLFLZ005', '花纹板', 'admin', '2025-06-25 15:14:04', 'admin', '2025-06-25 15:14:04', '0', 1);
INSERT INTO `basic_wlflz` VALUES (6, 'WLFLZ006', '工字钢', 'admin', '2025-06-25 15:14:04', 'admin', '2025-06-25 15:14:04', '0', 2);
INSERT INTO `basic_wlflz` VALUES (7, 'WLFLZ007', 'H型钢', 'admin', '2025-06-25 15:14:04', 'admin', '2025-06-25 15:14:04', '0', 2);
INSERT INTO `basic_wlflz` VALUES (8, 'WLFLZ008', '角钢', 'admin', '2025-06-25 15:14:04', 'admin', '2025-06-25 15:14:04', '0', 2);
INSERT INTO `basic_wlflz` VALUES (9, 'WLFLZ009', '槽钢', 'admin', '2025-06-25 15:14:04', 'admin', '2025-06-25 15:14:04', '0', 2);
INSERT INTO `basic_wlflz` VALUES (10, 'WLFLZ010', '方管', 'admin', '2025-06-25 15:14:04', 'admin', '2025-06-25 15:14:04', '0', 3);
INSERT INTO `basic_wlflz` VALUES (11, 'WLFLZ011', '圆管', 'admin', '2025-06-25 15:14:04', 'admin', '2025-06-25 15:14:04', '0', 3);
INSERT INTO `basic_wlflz` VALUES (12, 'WLFLZ012', '矩形管', 'admin', '2025-06-25 15:14:04', 'admin', '2025-06-25 15:14:04', '0', 3);
INSERT INTO `basic_wlflz` VALUES (13, 'WLFLZ013', '镀锌管', 'admin', '2025-06-25 15:14:04', 'admin', '2025-06-25 15:14:04', '0', 3);
INSERT INTO `basic_wlflz` VALUES (14, 'WLFLZ014', '螺纹钢', 'admin', '2025-06-25 15:14:04', 'admin', '2025-06-25 15:14:04', '0', 4);
INSERT INTO `basic_wlflz` VALUES (15, 'WLFLZ015', '盘螺', 'admin', '2025-06-25 15:14:04', 'admin', '2025-06-25 15:14:04', '0', 4);
INSERT INTO `basic_wlflz` VALUES (16, 'WLFLZ016', '钢筋网', 'admin', '2025-06-25 15:14:04', 'admin', '2025-06-25 15:14:04', '0', 4);
INSERT INTO `basic_wlflz` VALUES (17, 'WLFLZ017', '铝板', 'admin', '2025-06-25 15:14:04', 'admin', '2025-06-25 15:14:04', '0', 7);
INSERT INTO `basic_wlflz` VALUES (18, 'WLFLZ018', '铜板', 'admin', '2025-06-25 15:14:04', 'admin', '2025-06-25 15:14:04', '0', 7);
INSERT INTO `basic_wlflz` VALUES (19, 'WLFLZ019', '钛板', 'admin', '2025-06-25 15:14:04', 'admin', '2025-06-25 15:14:04', '0', 7);
INSERT INTO `basic_wlflz` VALUES (20, 'WLFLZ020', '镍板', 'admin', '2025-06-25 15:14:04', 'admin', '2025-06-25 15:14:04', '0', 7);

SET FOREIGN_KEY_CHECKS = 1;
