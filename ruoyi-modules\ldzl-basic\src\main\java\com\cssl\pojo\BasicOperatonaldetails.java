package com.cssl.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@TableName("basic_operatonaldetails")
public class BasicOperatonaldetails  {
    @TableId(value ="operationaldetails_id", type = IdType.AUTO)
    private Long operationaldetails_id;
    private Long operational_id;
    private Long process_id;
    private Long product_id;
    private String is_delete;
    private String create_by;
    private Date create_time;
    private String update_by;
    private Date update_time;
    @TableField(exist = false)
    private BasicOperational basicOperational;
    @TableField(exist = false)
    private BasicProcess basicProcess;
    @TableField(exist = false)
    private BasicProduct basicProduct;

    //修改时被删除的产品id
    @TableField(exist = false)
    private Long deleteProductIds;
}
