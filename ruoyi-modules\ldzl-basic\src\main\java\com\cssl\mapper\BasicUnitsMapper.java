package com.cssl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cssl.pojo.BasicUnits;

import java.util.List;

public interface BasicUnitsMapper extends BaseMapper<BasicUnits> {
    //查询所有单位
    public List<BasicUnits> listBasicUnits(BasicUnits basicUnits);

    //添加主单位
    public int addBasicUnits(BasicUnits basicUnits);

    //查询正在启用的单位
    public List<BasicUnits>findByBasicUnits();


    //修改单位
    public int updateBasicUnits(BasicUnits basicUnits);

    //删除单位
    public int delBasicUnits(Long unit_id);

    //批量删除
    public int delBatchBasicUnits(List<Long> unit_ids);

}
