package com.cssl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cssl.pojo.BasicOperational;
import com.cssl.pojo.BasicOperatonaldetails;
import com.cssl.pojo.vo.BasicOperationdetailsVo;

import java.util.List;

public interface BasicOperatonaldetailsService  extends IService<BasicOperatonaldetails> {
    //删除工艺路线详情
    public int deleteBasicOperationdetailsById(Long operational_id);

    //批量删除
    public int deleteBatchBasicOperationdetails(List<Long> operationalIds);
    //根据工艺路线id查询产品id
    public List<BasicOperatonaldetails> getBasicOperationdetailsByOperationalId(Long operational_id);

    //根据工艺路线id查询详情
    public List<BasicOperationdetailsVo> getBasicOperationdetailsByOperationalId2(Long operational_id);

    //根据工艺路线id查询详情
    public List<BasicOperatonaldetails> getBasicOperationdetailsByOperationalId3(Long operational_id);

    //根据工艺路线id批量删除
    public int delBatchBasicOperationdetails(List<Long> operationalIds);
}
