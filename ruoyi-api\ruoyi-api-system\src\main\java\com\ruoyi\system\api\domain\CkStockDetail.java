package com.ruoyi.system.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 库存明细表
 * @TableName ck_stock_detail
 */
@TableName(value ="ck_stock_detail")
@Data
public class CkStockDetail implements Serializable {
    /**
     * 库存明细id
     */
    @TableId(type = IdType.AUTO)
    private Long item_id;

    /**
     * 产品物料编码
     */
    private String item_code;

    /**
     * 产品物料名称
     */
    private String item_name;

    /**
     * 单位id
     */
    private String unit_id;

    /**
     * 批次ID
     */
    private Long batch_id;

    /**
     * 批次编号
     */
    private String batch_code;

    /**
     * 生产日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date produce_time;

    /**
     * 客户id
     */
    private Long client_id;

    /**
     * 客户编码
     */
    private String client_code;

    /**
     * 客户名称
     */
    private String client_name;

    /**
     * 仓库id
     */
    private Long warehouse_id;

    /**
     * 仓库编码
     */
    private String warehouse_code;

    /**
     * 仓库名称
     */
    private String warehouse_name;

    /**
     * 库区id
     */
    private Long location_id;

    /**
     * 库区编码
     */
    private String location_code;

    /**
     * 库区名称
     */
    private String location_name;

    /**
     * 库位id
     */
    private Long area_id;

    /**
     * 库位编码
     */
    private String area_code;

    /**
     * 库位名称
     */
    private String area_name;

    /**
     * 库存数量
     */
    private BigDecimal quantity_num;

    /**
     * 库存保留数量
     */
    private BigDecimal reserved_num;

    /**
     * 一级分类id
     */
    private Long material_classification_id;

    /**
     * 二级分类id
     */
    private Long material_subcategory_id;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否冻结(Y：已冻结，N：未冻结)
     */
    private String frozen_flag;

    /**
     * 规格型号
     */
    private String stock_sfn;

    /**
     * 预留字段3
     */
    private Integer attr3;

    /**
     * 预留字段4
     */
    private Integer attr4;

    /**
     * 创建人
     */
    private String create_by;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date create_time;

    /**
     * 更新人
     */
    private String update_by;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date update_time;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    private String is_delete;

    /**
     * 单位表 引用
     */
    @TableField(exist = false)
    private BasicUnits units;

    public CkStockDetail() {
    }

    public CkStockDetail(Long item_id, BigDecimal quantity_num) {
        this.item_id = item_id;
        this.quantity_num = quantity_num;
    }

    public CkStockDetail(String item_code, BigDecimal quantity_num) {
        this.item_code = item_code;
        this.quantity_num = quantity_num;
    }

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        CkStockDetail other = (CkStockDetail) that;
        return (this.getItem_id() == null ? other.getItem_id() == null : this.getItem_id().equals(other.getItem_id()))
            && (this.getItem_code() == null ? other.getItem_code() == null : this.getItem_code().equals(other.getItem_code()))
            && (this.getItem_name() == null ? other.getItem_name() == null : this.getItem_name().equals(other.getItem_name()))
            && (this.getUnit_id() == null ? other.getUnit_id() == null : this.getUnit_id().equals(other.getUnit_id()))
            && (this.getBatch_id() == null ? other.getBatch_id() == null : this.getBatch_id().equals(other.getBatch_id()))
            && (this.getBatch_code() == null ? other.getBatch_code() == null : this.getBatch_code().equals(other.getBatch_code()))
            && (this.getProduce_time() == null ? other.getProduce_time() == null : this.getProduce_time().equals(other.getProduce_time()))
            && (this.getClient_id() == null ? other.getClient_id() == null : this.getClient_id().equals(other.getClient_id()))
            && (this.getClient_code() == null ? other.getClient_code() == null : this.getClient_code().equals(other.getClient_code()))
            && (this.getClient_name() == null ? other.getClient_name() == null : this.getClient_name().equals(other.getClient_name()))
            && (this.getWarehouse_id() == null ? other.getWarehouse_id() == null : this.getWarehouse_id().equals(other.getWarehouse_id()))
            && (this.getWarehouse_code() == null ? other.getWarehouse_code() == null : this.getWarehouse_code().equals(other.getWarehouse_code()))
            && (this.getWarehouse_name() == null ? other.getWarehouse_name() == null : this.getWarehouse_name().equals(other.getWarehouse_name()))
            && (this.getLocation_id() == null ? other.getLocation_id() == null : this.getLocation_id().equals(other.getLocation_id()))
            && (this.getLocation_code() == null ? other.getLocation_code() == null : this.getLocation_code().equals(other.getLocation_code()))
            && (this.getLocation_name() == null ? other.getLocation_name() == null : this.getLocation_name().equals(other.getLocation_name()))
            && (this.getArea_id() == null ? other.getArea_id() == null : this.getArea_id().equals(other.getArea_id()))
            && (this.getArea_code() == null ? other.getArea_code() == null : this.getArea_code().equals(other.getArea_code()))
            && (this.getArea_name() == null ? other.getArea_name() == null : this.getArea_name().equals(other.getArea_name()))
            && (this.getQuantity_num() == null ? other.getQuantity_num() == null : this.getQuantity_num().equals(other.getQuantity_num()))
            && (this.getReserved_num() == null ? other.getReserved_num() == null : this.getReserved_num().equals(other.getReserved_num()))
            && (this.getMaterial_classification_id() == null ? other.getMaterial_classification_id() == null : this.getMaterial_classification_id().equals(other.getMaterial_classification_id()))
            && (this.getMaterial_subcategory_id() == null ? other.getMaterial_subcategory_id() == null : this.getMaterial_subcategory_id().equals(other.getMaterial_subcategory_id()))
            && (this.getRemark() == null ? other.getRemark() == null : this.getRemark().equals(other.getRemark()))
            && (this.getFrozen_flag() == null ? other.getFrozen_flag() == null : this.getFrozen_flag().equals(other.getFrozen_flag()))
            && (this.getStock_sfn() == null ? other.getStock_sfn() == null : this.getStock_sfn().equals(other.getStock_sfn()))
            && (this.getAttr3() == null ? other.getAttr3() == null : this.getAttr3().equals(other.getAttr3()))
            && (this.getAttr4() == null ? other.getAttr4() == null : this.getAttr4().equals(other.getAttr4()))
            && (this.getCreate_by() == null ? other.getCreate_by() == null : this.getCreate_by().equals(other.getCreate_by()))
            && (this.getCreate_time() == null ? other.getCreate_time() == null : this.getCreate_time().equals(other.getCreate_time()))
            && (this.getUpdate_by() == null ? other.getUpdate_by() == null : this.getUpdate_by().equals(other.getUpdate_by()))
            && (this.getUpdate_time() == null ? other.getUpdate_time() == null : this.getUpdate_time().equals(other.getUpdate_time()))
            && (this.getIs_delete() == null ? other.getIs_delete() == null : this.getIs_delete().equals(other.getIs_delete()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getItem_id() == null) ? 0 : getItem_id().hashCode());
        result = prime * result + ((getItem_code() == null) ? 0 : getItem_code().hashCode());
        result = prime * result + ((getItem_name() == null) ? 0 : getItem_name().hashCode());
        result = prime * result + ((getUnit_id() == null) ? 0 : getUnit_id().hashCode());
        result = prime * result + ((getBatch_id() == null) ? 0 : getBatch_id().hashCode());
        result = prime * result + ((getBatch_code() == null) ? 0 : getBatch_code().hashCode());
        result = prime * result + ((getProduce_time() == null) ? 0 : getProduce_time().hashCode());
        result = prime * result + ((getClient_id() == null) ? 0 : getClient_id().hashCode());
        result = prime * result + ((getClient_code() == null) ? 0 : getClient_code().hashCode());
        result = prime * result + ((getClient_name() == null) ? 0 : getClient_name().hashCode());
        result = prime * result + ((getWarehouse_id() == null) ? 0 : getWarehouse_id().hashCode());
        result = prime * result + ((getWarehouse_code() == null) ? 0 : getWarehouse_code().hashCode());
        result = prime * result + ((getWarehouse_name() == null) ? 0 : getWarehouse_name().hashCode());
        result = prime * result + ((getLocation_id() == null) ? 0 : getLocation_id().hashCode());
        result = prime * result + ((getLocation_code() == null) ? 0 : getLocation_code().hashCode());
        result = prime * result + ((getLocation_name() == null) ? 0 : getLocation_name().hashCode());
        result = prime * result + ((getArea_id() == null) ? 0 : getArea_id().hashCode());
        result = prime * result + ((getArea_code() == null) ? 0 : getArea_code().hashCode());
        result = prime * result + ((getArea_name() == null) ? 0 : getArea_name().hashCode());
        result = prime * result + ((getQuantity_num() == null) ? 0 : getQuantity_num().hashCode());
        result = prime * result + ((getReserved_num() == null) ? 0 : getReserved_num().hashCode());
        result = prime * result + ((getMaterial_classification_id() == null) ? 0 : getMaterial_classification_id().hashCode());
        result = prime * result + ((getMaterial_subcategory_id() == null) ? 0 : getMaterial_subcategory_id().hashCode());
        result = prime * result + ((getRemark() == null) ? 0 : getRemark().hashCode());
        result = prime * result + ((getFrozen_flag() == null) ? 0 : getFrozen_flag().hashCode());
        result = prime * result + ((getStock_sfn() == null) ? 0 : getStock_sfn().hashCode());
        result = prime * result + ((getAttr3() == null) ? 0 : getAttr3().hashCode());
        result = prime * result + ((getAttr4() == null) ? 0 : getAttr4().hashCode());
        result = prime * result + ((getCreate_by() == null) ? 0 : getCreate_by().hashCode());
        result = prime * result + ((getCreate_time() == null) ? 0 : getCreate_time().hashCode());
        result = prime * result + ((getUpdate_by() == null) ? 0 : getUpdate_by().hashCode());
        result = prime * result + ((getUpdate_time() == null) ? 0 : getUpdate_time().hashCode());
        result = prime * result + ((getIs_delete() == null) ? 0 : getIs_delete().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", item_id=").append(item_id);
        sb.append(", item_code=").append(item_code);
        sb.append(", item_name=").append(item_name);
        sb.append(", unit_id=").append(unit_id);
        sb.append(", batch_id=").append(batch_id);
        sb.append(", batch_code=").append(batch_code);
        sb.append(", produce_time=").append(produce_time);
        sb.append(", client_id=").append(client_id);
        sb.append(", client_code=").append(client_code);
        sb.append(", client_name=").append(client_name);
        sb.append(", warehouse_id=").append(warehouse_id);
        sb.append(", warehouse_code=").append(warehouse_code);
        sb.append(", warehouse_name=").append(warehouse_name);
        sb.append(", location_id=").append(location_id);
        sb.append(", location_code=").append(location_code);
        sb.append(", location_name=").append(location_name);
        sb.append(", area_id=").append(area_id);
        sb.append(", area_code=").append(area_code);
        sb.append(", area_name=").append(area_name);
        sb.append(", quantity_num=").append(quantity_num);
        sb.append(", reserved_num=").append(reserved_num);
        sb.append(", material_classification_id=").append(material_classification_id);
        sb.append(", material_subcategory_id=").append(material_subcategory_id);
        sb.append(", remark=").append(remark);
        sb.append(", frozen_flag=").append(frozen_flag);
        sb.append(", stock_sfn=").append(stock_sfn);
        sb.append(", attr3=").append(attr3);
        sb.append(", attr4=").append(attr4);
        sb.append(", create_by=").append(create_by);
        sb.append(", create_time=").append(create_time);
        sb.append(", update_by=").append(update_by);
        sb.append(", update_time=").append(update_time);
        sb.append(", is_delete=").append(is_delete);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}