<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cssl.mapper.BasicStationMapper">
    <resultMap id="selectBasicStation" type="com.cssl.pojo.BasicStation">
        <id column="station_id" property="station_id"/>
        <association property="basicProductionline" javaType="com.cssl.pojo.BasicProductionline">
            <id column="production_line_id" property="production_line_id"/>
        </association>
    </resultMap>

    <insert id="addBasicStation">
        insert into basic_station
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="factory_name !=null and factory_name !=''">factory_name,</if>
            <if test="factory_id !=null ">factory_id,</if>
            <if test="workshop_id !=null ">workshop_id,</if>
            <if test="workshop_name !=null and workshop_name !=''">workshop_name,</if>
            <if test="production_line_name !=null and production_line_name !=''">production_line_name,</if>
            <if test="production_line_id !=null ">production_line_id,</if>
            <if test="remarks !=null and remarks !=''">remarks,</if>
            <if test="station_code !=null and station_code !=''">station_code,</if>
            <if test="station_name !=null and station_name !=''">station_name,</if>
            <if test="is_delete!=null ">is_delete,</if>
            <if test="create_by!=null and create_by !='' ">create_by,</if>
            <if test="create_time!=null ">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="factory_name !=null and factory_name !=''">#{factory_name},</if>
            <if test="factory_id !=null ">#{factory_id},</if>
            <if test="workshop_id !=null ">#{workshop_id},</if>
            <if test="workshop_name !=null and workshop_name !=''">#{workshop_name},</if>
            <if test="production_line_name !=null and production_line_name !=''">#{production_line_name},</if>
            <if test="production_line_id !=null ">#{production_line_id},</if>
            <if test="remarks !=null and remarks !=''">#{remarks},</if>
            <if test="station_code !=null and station_code !=''">#{station_code},</if>
            <if test="station_name !=null and station_name !=''">#{station_name},</if>
            <if test="is_delete!=null ">#{is_delete},</if>
            <if test="create_by!=null and create_by !='' ">#{create_by},</if>
            <if test="create_time!=null ">#{create_time},</if>
        </trim>
    </insert>

    <update id="updateBasicStation">
        update basic_station
        <trim prefix="SET" suffixOverrides=",">
            <if test="factory_name !=null and factory_name !=''">factory_name=#{factory_name},</if>
            <if test="factory_id !=null ">factory_id=#{factory_id},</if>
            <if test="workshop_id !=null ">workshop_id=#{workshop_id},</if>
            <if test="production_line_id !=null ">production_line_id=#{production_line_id},</if>
            <if test="workshop_name !=null and workshop_name !=''">workshop_name=#{workshop_name},</if>
            <if test="production_line_name !=null and production_line_name !=''">production_line_name=#{production_line_name},</if>
            <if test="remarks !=null and remarks !=''">remarks=#{remarks},</if>
            <if test="station_code !=null and station_code !=''">station_code=#{station_code},</if>
            <if test="station_name !=null and station_name !=''">station_name=#{station_name},</if>
            <if test="update_by!=null and update_by !='' ">update_by=#{update_by},</if>
            <if test="update_time!=null ">update_time=#{update_time},</if>
        </trim>
        where station_id=#{station_id}
    </update>

    <update id="delBasicStation">
        update basic_station
        <trim prefix="SET" suffixOverrides=",">
            <if test="is_delete !=null">is_delete=1</if>
        </trim>
        where station_id = #{station_id}
    </update>

    <select id="listBasicStation" resultType="com.cssl.pojo.BasicStation">
        SELECT * from basic_station bs inner join basic_productionline bp on bs.production_line_id=bp.production_line_id  inner join basic_workshop bw on bp.workshop_id=bw.workshop_id
        inner join basic_factory bf on bf.factory_id=bw.workshop_id where bp.is_delete=0 and bw.is_delete=0 and bf.is_delete=0 and bs.is_delete=0
        <if test="basicProductionline !=null and basicProductionline.production_line_id !=null">and bp.production_line_id=#{basicProductionline.production_line_id}</if>
        <if test="station_id !=null and station_id !=''">and bs.station_id=#{station_id}</if>
        <if test="station_name !=null and station_name !=''">and bs.station_name=#{station_name} </if>
    </select>

    <select id="selectBasicStationByProcessIds" resultType="com.cssl.pojo.BasicStation">
        SELECT DISTINCT bs.*, bd.process_id
        FROM basic_station bs
        INNER JOIN basic_processdetails bd ON bs.station_id = bd.station_id
        WHERE bs.is_delete = '0' AND bd.is_delete = '0' AND bd.process_id IN
        <foreach item="item" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>