package com.cssl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cssl.pojo.BasicCustomers;

import java.util.List;

public interface BasicCustomersMapper extends BaseMapper<BasicCustomers> {
    //查询所有客户信息
    public List<BasicCustomers> listBasicCustomers(BasicCustomers basicCustomers);

    //添加客户信息
    public int addBasicCustomers(BasicCustomers basicCustomers);

    //修改客户信息
    public int updateCustomers(BasicCustomers basicCustomers);

    //删除客户信息
    public int delCustomers(Long customer_id);

    //批量删除
    public int delBatchCustomers(List<Long> customer_ids);
}
