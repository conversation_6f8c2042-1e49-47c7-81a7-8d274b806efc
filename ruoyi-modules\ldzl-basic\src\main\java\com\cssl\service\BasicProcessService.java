package com.cssl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cssl.pojo.BasicProcess;
import com.cssl.pojo.BasicProductionline;

import java.util.List;

public interface BasicProcessService extends IService<BasicProcess> {
    //查询所有工序信息
    public List<BasicProcess> listBasicProcess(BasicProcess basicProcess);

    //添加工序信息
    public int addBasicProcess(BasicProcess basicProcess);

    //修改工序信息
    public int updateBasicProcess(BasicProcess basicProcess);

    //删除工序信息
    public int delBasicProcess(Long process_id);

    List<BasicProcess> selectBasicProcessByIds(List<Long> processIds);

    //批量删除工序信息
    public int delBatchBasicProcess(List<Long> processIds);
}
