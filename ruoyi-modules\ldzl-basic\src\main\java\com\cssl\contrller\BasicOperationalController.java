package com.cssl.contrller;

import java.util.List;
import java.io.IOException;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.cssl.service.BasicOperatonaldetailsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.cssl.pojo.BasicOperational;
import com.cssl.service.BasicOperationalService;
import com.ruoyi.common.core.utils.bean.BeanUtils;
import com.ruoyi.system.api.domain.BasicOperationalDto;
import java.util.stream.Collectors;

/**
 * 工艺路线Controller
 * 
 * <AUTHOR>
 * @date 
 */
@RestController
@RequestMapping("/bop")
public class BasicOperationalController extends BaseController
{
    @Autowired
    private BasicOperationalService basicOperationalService;

    @Resource
    private BasicOperatonaldetailsService basicOperatonaldetailsService;

    /**
     * 查询工艺路线列表
     */
    @GetMapping ("/list")
    public TableDataInfo list(BasicOperational basicOperational)
    {
        startPage();
        List<BasicOperational> list = basicOperationalService.selectBasicOperationalList(basicOperational);
        return getDataTable(list);
    }

    @PostMapping ("/list1")
    public TableDataInfo list1(@RequestBody BasicOperational basicOperational)
    {
        startPage();
        List<BasicOperational> list = basicOperationalService.listBasicOperational(basicOperational);
        return getDataTable(list);
    }

    /**
     * 导出工艺路线列表
     */
    @Log(title = "工艺路线", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasicOperational basicOperational)
    {
        List<BasicOperational> list = basicOperationalService.selectBasicOperationalList(basicOperational);
        ExcelUtil<BasicOperational> util = new ExcelUtil<BasicOperational>(BasicOperational.class);
        util.exportExcel(response, list, "工艺路线数据");
    }

    /**
     * 获取工艺路线详细信息
     */
    @GetMapping(value = "/op")
    public AjaxResult getInfo(@RequestBody BasicOperational basicOperational)
    {
        return success(basicOperationalService.listBasicOperational(basicOperational));
    }

    /**
     * 根据产品ID获取工艺路线详细信息
     */
    @GetMapping(value = "/material/{productId}")
    public AjaxResult getInfoByProductId(@PathVariable("productId") Long productId)
    {
        List<BasicOperational> list = basicOperationalService.selectBasicOperationalByProductId(productId);
        List<BasicOperationalDto> dtoList = list.stream().map(item -> {
            BasicOperationalDto dto = new BasicOperationalDto();
            BeanUtils.copyProperties(item, dto);
            return dto;
        }).collect(Collectors.toList());
        return success(dtoList);
    }

    /**
     * 新增工艺路线
     */
    @Log(title = "工艺路线", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasicOperational basicOperational)
    {
        return toAjax(basicOperationalService.insertBasicOperational(basicOperational));
    }

    /**
     * 修改工艺路线
     */
    @Log(title = "工艺路线", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasicOperational basicOperational)
    {
        return toAjax(basicOperationalService.updateBasicOperational(basicOperational));
    }

    //删除工艺路线
    @Log(title = "删除工艺路线", businessType = BusinessType.UPDATE)
    @PutMapping("/dp/{operational_id}")
    public AjaxResult edit1(@PathVariable Long operational_id)
    {
        return toAjax(basicOperationalService.deleteBasicOperationalById(operational_id));
    }

    //批量删除工艺路线
    @Log(title = "批量删除工艺路线", businessType = BusinessType.UPDATE)
    @PutMapping("/batch/{operational_ids}")
    public AjaxResult edit(@PathVariable List<Long> operational_ids)
    {
        return toAjax(basicOperationalService.deleteBatchBasicOperational(operational_ids));
    }

    //根据工艺路线id查询详情
    @GetMapping("/details/{operational_id}")
    public AjaxResult getDetails(@PathVariable("operational_id") Long operational_id)
    {
        return success(basicOperatonaldetailsService.getBasicOperationdetailsByOperationalId2(operational_id));
    }
} 