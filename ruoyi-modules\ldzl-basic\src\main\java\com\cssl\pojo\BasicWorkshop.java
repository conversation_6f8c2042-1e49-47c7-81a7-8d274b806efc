package com.cssl.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

@Data
@TableName("basic_workshop")
public class BasicWorkshop extends BaseEntity {
    @TableId(value ="workshop_id",type = IdType.AUTO)
    private Integer workshop_id;
    private String factory_name;
    private String workshop_code;
    private String workshop_name;
    private String remarks;
    private String is_delete;
    private String create_by;
    private Date create_time;
    private String update_by;
    private Date update_time;
    @TableField(exist = false)
    private BasicFactory basicFactory;
    private Integer factory_id;

}
