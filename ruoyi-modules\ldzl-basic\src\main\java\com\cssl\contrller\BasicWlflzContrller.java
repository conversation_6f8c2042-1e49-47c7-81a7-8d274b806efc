package com.cssl.contrller;

import com.cssl.pojo.BasicWlflz;
import com.cssl.service.BasicWlflzService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/wlflz")
public class BasicWlflzContrller extends BaseController {
    @Resource
    private BasicWlflzService basicWlflzService;

    //添加子分类
    @Log(title = "添加子分类", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasicWlflz basicWlflz)
    {
        return toAjax(basicWlflzService.insertBasicWlflz(basicWlflz));
    }
    //查询所有分类信息
    @PostMapping("/list")
    public TableDataInfo list(BasicWlflz basicWlflz)
    {
        startPage();
        List<BasicWlflz> list =basicWlflzService.selectBasicWlflzList(basicWlflz);
        return getDataTable(list);
    }
    //修改子类
    @Log(title = "修改子类", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasicWlflz basicWlflz)
    {
        return toAjax(basicWlflzService.updateBasicWlflz(basicWlflz));
    }

    //删除子类
    @Log(title = "删除子类", businessType = BusinessType.UPDATE)
    @PutMapping("/dp/{material_subcategory_id}")
    public AjaxResult edit1(@PathVariable Long material_subcategory_id)
    {
        return toAjax(basicWlflzService.delBasicWlflz(material_subcategory_id));
    }


    @PostMapping("/find")
    public TableDataInfo find(@RequestBody BasicWlflz basicWlflz)
    {
        List<BasicWlflz> list =basicWlflzService.selectBasicWlflzList(basicWlflz);
        return getDataTable(list);
    }
}
