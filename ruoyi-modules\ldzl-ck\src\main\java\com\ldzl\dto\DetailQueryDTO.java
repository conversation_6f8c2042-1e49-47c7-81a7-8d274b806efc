package com.ldzl.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;

/**
 * 采购详情查询参数
 */
@Data
public class DetailQueryDTO implements Serializable {
    /**
     * 当前页码
     */
    private int pageNum;

    /**
     * 每页数量
     */
    private int pageSize;

    /**
     * 采购单ID
     */
    private Long po_id;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}
