package com.cssl.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cssl.mapper.BasicNumbersMapper;
import com.cssl.mapper.BasicProductMapper;
import com.cssl.pojo.BasicNumbers;
import com.cssl.pojo.BasicProduct;
import com.cssl.service.BasicNumbersService;
import com.cssl.service.BasicProductService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.RemoteFileService;
import com.ruoyi.system.api.domain.SysFile;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class BasicProductServiceImpl extends ServiceImpl<BasicProductMapper, BasicProduct> implements BasicProductService {


    @Resource
    private BasicProductMapper basicProductMapper;


    @Override
    public List<BasicProduct> listBasicProductMapper(BasicProduct basicProduct) {
        return basicProductMapper.listBasicProductMapper(basicProduct);
    }

    @Override
    public int addBasicProduct(BasicProduct basicProduct) {
        basicProduct.setCreate_by(SecurityUtils.getUsername());
        basicProduct.setCreate_time(new Date());
        basicProduct.setIs_delete("0");

        return basicProductMapper.addBasicProduct(basicProduct);
    }

    @Override
    public int updateBasicProduct(BasicProduct basicProduct) {
        basicProduct.setUpdate_by(SecurityUtils.getUsername());
        basicProduct.setUpdate_time(new Date());
        return basicProductMapper.updateBasicProduct(basicProduct);
    }

    @Override
    public int delBasicProduct(Long product_id) {
        return basicProductMapper.delBasicProduct(product_id);
    }

    @Override
    public int delBasicProductByIds(List<Long> product_ids) {
        return basicProductMapper.delBasicProductByIds(product_ids);
    }

}
