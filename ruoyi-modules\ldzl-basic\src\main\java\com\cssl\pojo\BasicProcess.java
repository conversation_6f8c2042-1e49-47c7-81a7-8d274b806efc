package com.cssl.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.lang.reflect.ParameterizedType;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@TableName("basic_process")
public class BasicProcess extends BaseEntity {
    @TableId(value = "process_id",type = IdType.AUTO)
    private Long process_id;
    private String process_code;
    private String process_name;
    private String process_status;
    private String process_description;
    private String remarks;
    private String is_delete;
    private String create_by;
    private Date create_time;
    private String update_by;
    private Date update_time;
    private Long station_id;
    @TableField(exist = false)
    private List<BasicProcessdetails> basicProcessdetailsList;

    /**
     * 以下字段是从BaseEntity继承而来，但在basic_process表中不存在，
     * 或者由当前类中的字段（如create_by）处理，
     * 因此需要使用 @TableField(exist = false) 告诉MyBatis-Plus在进行数据库操作时忽略它们。
     */
    @TableField(exist = false)
    private String searchValue;
    @TableField(exist = false)
    private String createBy;
    @TableField(exist = false)
    private Date createTime;
    @TableField(exist = false)
    private String updateBy;
    @TableField(exist = false)
    private Date updateTime;
    @TableField(exist = false)
    private String remark;
    @TableField(exist = false)
    private Map<String, Object> params;
}
