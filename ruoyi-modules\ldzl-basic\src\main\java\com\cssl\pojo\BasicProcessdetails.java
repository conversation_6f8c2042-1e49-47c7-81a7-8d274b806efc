package com.cssl.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.util.Date;
import java.util.Map;

@Data
@TableName("basic_processdetails")
public class BasicProcessdetails extends BaseEntity {
    @TableId(value ="processdetails_id",type = IdType.AUTO)
    private Long processdetails_id;
    private Long material_id;
    private Long station_id;
    private Long process_id;
    private String is_delete;
    private String create_by;
    private Date create_time;
    private String update_by;
    private Date update_time;
    @TableField(exist = false)
    private BasicWlgl basicWlgl;
    @TableField(exist = false)
    private BasicStation basicStation;
    @TableField(exist = false)
    private BasicProcess basicProcess;
    @TableField(exist = false)
    private Map<String, Object> params;

}
