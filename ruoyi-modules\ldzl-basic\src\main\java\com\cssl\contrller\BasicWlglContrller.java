package com.cssl.contrller;

import com.cssl.pojo.BasicProduct;
import com.cssl.pojo.BasicWlfl;
import com.cssl.pojo.BasicWlflz;
import com.cssl.pojo.BasicWlgl;
import com.cssl.service.BasicWlglService;
import com.github.pagehelper.PageHelper;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/wlgl")
public class BasicWlglContrller extends BaseController {
    @Resource
    private BasicWlglService basicWlglService;

    //查询所有物料信息
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody BasicWlgl basicWlgl) {
        Map<String, Object> params=basicWlgl.getParams();
        if(params!=null && params.get("pageNum") !=null && params.get("pageSize")!=null){
            Integer pageNum = Integer.parseInt(params.get("pageNum").toString());
            Integer pageSize = Integer.parseInt(params.get("pageSize").toString());
            PageHelper.startPage(pageNum, pageSize);
        }else {
            startPage();
        }
        List<BasicWlgl> list =basicWlglService.listBasicWlgl(basicWlgl);

        /*startPage();
        List<BasicWlgl> list=basicWlglService.listBasicWlgl(basicWlgl);
        return getDataTable(list);*/

        return getDataTable(list);
    }
    //添加物料信息
    @Log(title = "添加物料信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasicWlgl basicWlgl)
    {
        System.out.println("lx:"+basicWlgl);
        return toAjax(basicWlglService.addBasicWlgl(basicWlgl));
    }

    //修改物料
    @Log(title = "修改物料", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasicWlgl basicWlgl)
    {
        return toAjax(basicWlglService.updateBasicwlgl(basicWlgl));
    }

    //删除物料
    @Log(title = "删除物料", businessType = BusinessType.UPDATE)
    @PutMapping("/dp/{material_id}")
    public AjaxResult edit1(@PathVariable Long material_id)
    {
        return toAjax(basicWlglService.delBasicWlgl(material_id));
    }
}
