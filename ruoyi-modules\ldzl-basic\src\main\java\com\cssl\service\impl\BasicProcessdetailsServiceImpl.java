package com.cssl.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cssl.mapper.BasicNumbersMapper;
import com.cssl.mapper.BasicProcessdetailsMapper;
import com.cssl.pojo.BasicNumbers;
import com.cssl.pojo.BasicProcessdetails;
import com.cssl.pojo.vo.BasicProcessdetailsVo;
import com.cssl.service.BasicNumbersService;
import com.cssl.service.BasicProcessdetailsService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
@Service
public class BasicProcessdetailsServiceImpl extends ServiceImpl<BasicProcessdetailsMapper, BasicProcessdetails> implements BasicProcessdetailsService {
    @Resource
    private BasicProcessdetailsMapper basicProcessdetailsMapper;
    @Override
    public List<BasicProcessdetails> listBasicProcessdetails(BasicProcessdetails basicProcessdetails) {
        return basicProcessdetailsMapper.listBasicProcessdetails(basicProcessdetails);
    }

    @Override
    public int delBasicProcessdetails(Long processdetails_id) {
        return basicProcessdetailsMapper.delBasicProcessdetails(processdetails_id);
    }

    @Override
    public int delBatchBasicProcessdetails(List<Long> processdetailsIds) {
        return basicProcessdetailsMapper.delBatchBasicProcessdetails(processdetailsIds);
    }

    @Override
    public List<BasicProcessdetails> listBasicProcessdetailsByProcessId(Long process_id) {
        return basicProcessdetailsMapper.listBasicProcessdetailsByProcessId(process_id);
    }

    @Override
    public List<BasicProcessdetailsVo> listBasicProcessdetailsvoBy(Long process_id) {
        return basicProcessdetailsMapper.listBasicProcessdetailsvoBy(process_id);
    }

    @Override
    public int delBatchBasicProcessdetailsByProcessId(List<Long> processIds) {
        return basicProcessdetailsMapper.delBatchBasicProcessdetailsByProcessId(processIds);
    }
}
