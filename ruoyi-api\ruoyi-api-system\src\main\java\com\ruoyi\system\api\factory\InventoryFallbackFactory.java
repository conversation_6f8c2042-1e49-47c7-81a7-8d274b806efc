package com.ruoyi.system.api.factory;

import com.ruoyi.common.core.domain.UR;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.system.api.InventoryService;
import com.ruoyi.system.api.domain.CkBatch;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 仓库库存服务降级处理
 */
@Component
public class InventoryFallbackFactory implements FallbackFactory<InventoryService> {

    private static final Logger log = LoggerFactory.getLogger(RemoteFileFallbackFactory.class);

    @Override
    public InventoryService create(Throwable throwable)
    {
        log.error("仓库库存服务调用失败:{}", throwable.getMessage());
        return new InventoryService()
        {
            @Override
            public UR viewInventory(String code) {
                return UR.fail("查询库存失败:" + throwable.getMessage());
            }

            @Override
            public UR viewInventoryList(List<String> listCode) {
                return UR.fail("批量查询库存失败:" + throwable.getMessage());
            }

            @Override
            public UR modifyInventory(String item_code, BigDecimal quantity_num) {
                return UR.fail("修改库存失败:" + throwable.getMessage());
            }

            @Override
            public UR modifyInventoryBatch(Map<String, BigDecimal> map) {
                return UR.fail("批量修改库存失败:" + throwable.getMessage());
            }

            @Override
            public UR addBatch(CkBatch batch) {
                return UR.fail("添加批次失败：" + throwable.getMessage());
            }

            @Override
            public TableDataInfo selectCkProductRecptDetectSingle(String recpt_coed, String recpt_name) {
                return new TableDataInfo();
            }

            @Override
            public UR updateCkProductRecptStatusStatus(Long recpt_id, Long status) {
                return UR.fail("修改入库单状态失败：" + throwable.getMessage());
            }

            @Override
            public TableDataInfo findReceipt_id(Long recpt_id) {
                return new TableDataInfo();
            }

            @Override
            public TableDataInfo selectCkProductSales(String sales_code, String sales_name) {
                return new TableDataInfo();
            }

            @Override
            public TableDataInfo selectCkProductSalesLine(Long sales_id) {
                return new TableDataInfo();
            }

            @Override
            public UR updateCkProductSalesStatusStatus(Long sales_id, String status) {
                return UR.fail("修改出库单状态失败：" + throwable.getMessage());
            }

        };
    }
}
