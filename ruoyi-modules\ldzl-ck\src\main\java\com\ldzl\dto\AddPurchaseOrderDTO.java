package com.ldzl.dto;

import com.ldzl.pojo.CkPurchaseOrder;
import com.ldzl.pojo.CkPurchaseOrderLine;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 新增采购订单
 */
@Data
public class AddPurchaseOrderDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 接收采购订单信息
     */
    private CkPurchaseOrder order;

    /**
     * 接收采购订单行信息
     */
    private List<CkPurchaseOrderLine> listOrderLine;
}
