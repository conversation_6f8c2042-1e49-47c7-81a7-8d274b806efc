package com.ruoyi.system.api;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.system.api.domain.ScTaskDto;
import com.ruoyi.system.api.factory.RemoteScTaskFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import java.util.List;

/**
 * 生产任务远程服务
 * 为质检模块提供工单、工序、任务信息查询
 * 
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteScTaskService", value = ServiceNameConstants.LDZL_SC_SERVICE, fallbackFactory = RemoteScTaskFallbackFactory.class)
public interface RemoteScTaskService {
    
    /**
     * 查询任务列表（供质检模块使用）
     * 支持按工单编号、工序编号、任务编号等条件查询
     * 
     * @param workOrderCode 工单编号
     * @param workOrderName 工单名称  
     * @param processCode 工序编码
     * @param taskName 工序名称
     * @param taskCode 任务编号
     * @param status 任务状态
     * @param source 请求来源
     * @return 任务列表
     */
    @GetMapping("/qc-query/all-tasks")
     R<List<ScTaskDto>> getTaskListForQc(
            @RequestParam(value =("workOrderCode"), required = false) String workOrderCode,
            @RequestParam(value =("workOrderName"), required = false) String workOrderName,
            @RequestParam(value =("processCode"), required = false) String processCode,
            @RequestParam(value =("taskName"), required = false) String taskName,
            @RequestParam(value =("taskCode"), required = false) String taskCode,
            @RequestParam(value =("status"), required = false) String status,
            @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
