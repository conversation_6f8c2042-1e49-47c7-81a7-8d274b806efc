package com.cssl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cssl.pojo.BasicUnits;
import com.cssl.pojo.BasicWlgl;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface BasicWlglService extends IService<BasicWlgl> {
    //查询所有物料
    public List<BasicWlgl> listBasicWlgl(BasicWlgl basicWlgl);

    //添加物料
    public int addBasicWlgl(BasicWlgl basicWlgl);

    //修改物料信息
    public int updateBasicwlgl(BasicWlgl basicWlgl);

    //删除物料
    public int delBasicWlgl(Long material_id);
}
