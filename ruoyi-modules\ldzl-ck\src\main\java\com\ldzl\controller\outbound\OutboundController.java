package com.ldzl.controller.outbound;

import com.ldzl.pojo.CkProductSales;
import com.ldzl.service.CkProductSalesLineService;
import com.ldzl.service.CkProductSalesService;
import com.ruoyi.common.core.domain.UR;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.TableDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 出库控制请
 */
@RestController
@RequestMapping("/outbound")
public class OutboundController extends BaseController {

    @Autowired
    private CkProductSalesService pss; // 出库服务
    @Autowired
    private CkProductSalesLineService psls; // 出库行服务

    /**
     * 查询待质检的出库单
     * @param sales_code
     * @param sales_name
     * @return
     */
    @GetMapping("/selectCkProductSales/{sales_code}/{sales_name}")
    public TableDataInfo selectCkProductSales(@PathVariable("sales_code") String sales_code,
                                              @PathVariable("sales_name") String sales_name){
        startPage();
        return getDataTable(pss.selectCkProductSales(sales_code, sales_name));
    }

    /**
     * 查询指定出库单下的商品详情
     * @param sales_id
     * @return
     */
    @GetMapping("/ckProductSalesLine/{sales_id}")
    public TableDataInfo selectCkProductSalesLine(@PathVariable("sales_id") Long sales_id){
        startPage();
        return getDataTable(psls.selectCkProductSalesLine(sales_id));
    }

    /**
     * 修改出库单状态
     * @param sales_id
     * @param status
     * @return
     */
    @GetMapping("/ckProductSalesStatus/{sales_id}/{status}")
    public UR updateCkProductSalesStatusStatus(@PathVariable("sales_id") Long sales_id,
                                               @PathVariable("status") String status){
        if(pss.updateCkProductSalesStatusStatus(status,sales_id))
            return UR.ok("修改成功");
        else
            return UR.fail("修改失败");
    }

}
