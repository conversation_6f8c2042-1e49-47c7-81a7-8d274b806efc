package com.cssl.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("basic_operational")
public class BasicOperational extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @TableId(value ="operational_id", type = IdType.AUTO)
    private Long operational_id;

    private String operational_code;

    private String operational_name;

    private String operational_status;

    private String operational_description;

    private Long processId;

    private Long productId;

    private String is_delete;
    private String create_by;
    private Date create_time;
    private String update_by;
    private Date update_time;
    private String remarks;
    @TableField(exist = false)
    private List<BasicOperatonaldetails> operatonaldetails;
}
