package com.cssl.pojo.vo;

import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class BasicBomdetailsVo extends BaseEntity {
    private BigDecimal material_usage;
    private String material_Ingredient_ratio;
    private String material_code;
    private String material_name;
    private String material_sfn;
    private String material_unit;
    private String product_code;
    private String product_name;
    private String product_sfn;
    private String product_unit;
    private Long bomdetails_id;
    private Integer position;
    private String is_detele;
    private Long bom_id;
    private Long material_id;
    private Long operational_id;
    private Long product_id;
    private String operational_code;
    private String operational_name;
    private String bom_status;
    private String create_by; // 创建人
    private Date create_time; // 创建时间
    private String update_by; // 更新人
    private Date update_time; // 更新时间
    private String operational_description;
    private String bom_code;
    private String bom_version;

}
