package com.cssl.pojo.vo;

import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class BasicProcessdetailsVo extends BaseEntity {
    private Long processdetails_id;
    private Long material_id;
    private Long station_id;
    private Long process_id;
    private BigDecimal usage_num;
    private String station_code;
    private String station_name;
    private String remarks;
    private String material_code;
    private String material_name;
    private String material_sfn;
    private String material_unit;
    private String create_by; // 创建人
    private Date create_time; // 创建时间
    private String update_by; // 更新人
    private Date update_time; // 更新时间
}
