package com.ruoyi.system.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 销售出库单表
 * @TableName ck_product_sales
 */
@TableName(value ="ck_product_sales")
@Data
public class CkProductSales implements Serializable {
    /**
     * 出库单ID
     */
    @TableId(type = IdType.AUTO)
    private Long sales_id;

    /**
     * 出库单编号
     */
    private String sales_code;

    /**
     * 出库单名称
     */
    private String sales_name;

    /**
     * 出货检验单ID
     */
    private Long oqc_id;

    /**
     * 出货检验单名称
     */
    private String oqc_name;

    /**
     * 出货检验单编码
     */
    private String oqc_code;

    /**
     * 通知单ID
     */
    private Long notice_id;

    /**
     * 客户ID
     */
    private Long customer_id;

    /**
     * 客户编码
     */
    private String customer_code;

    /**
     * 客户名称
     */
    private String customer_name;

    /**
     * 收货人
     */
    private String recipient;

    /**
     * 联系电话
     */
    private String tel;

    /**
     * 收货地址
     */
    private String address;

    /**
     * 承运商
     */
    private String carrier;

    /**
     * 运输单号
     */
    private String shipping_number;

    /**
     * 批次ID
     */
    private String batch_id;

    /**
     * 批次号
     */
    private String batch_code;

    /**
     * 出库日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sales_date;

    /**
     * 单据状态(1/未提交，2/已提交，3/未检测，4/已检测)
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 预留字段1
     */
    private String attr1;

    /**
     * 预留字段2
     */
    private String attr2;

    /**
     * 预留字段3
     */
    private Integer attr3;

    /**
     * 预留字段4
     */
    private Integer attr4;

    /**
     * 创建者
     */
    private String create_by;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date create_time;

    /**
     * 更新者
     */
    private String update_by;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date update_time;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    private String is_delete;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        CkProductSales other = (CkProductSales) that;
        return (this.getSales_id() == null ? other.getSales_id() == null : this.getSales_id().equals(other.getSales_id()))
            && (this.getSales_code() == null ? other.getSales_code() == null : this.getSales_code().equals(other.getSales_code()))
            && (this.getSales_name() == null ? other.getSales_name() == null : this.getSales_name().equals(other.getSales_name()))
            && (this.getOqc_id() == null ? other.getOqc_id() == null : this.getOqc_id().equals(other.getOqc_id()))
            && (this.getOqc_name() == null ? other.getOqc_name() == null : this.getOqc_name().equals(other.getOqc_name()))
            && (this.getOqc_code() == null ? other.getOqc_code() == null : this.getOqc_code().equals(other.getOqc_code()))
            && (this.getNotice_id() == null ? other.getNotice_id() == null : this.getNotice_id().equals(other.getNotice_id()))
            && (this.getCustomer_id() == null ? other.getCustomer_id() == null : this.getCustomer_id().equals(other.getCustomer_id()))
            && (this.getCustomer_code() == null ? other.getCustomer_code() == null : this.getCustomer_code().equals(other.getCustomer_code()))
            && (this.getCustomer_name() == null ? other.getCustomer_name() == null : this.getCustomer_name().equals(other.getCustomer_name()))
            && (this.getRecipient() == null ? other.getRecipient() == null : this.getRecipient().equals(other.getRecipient()))
            && (this.getTel() == null ? other.getTel() == null : this.getTel().equals(other.getTel()))
            && (this.getAddress() == null ? other.getAddress() == null : this.getAddress().equals(other.getAddress()))
            && (this.getCarrier() == null ? other.getCarrier() == null : this.getCarrier().equals(other.getCarrier()))
            && (this.getShipping_number() == null ? other.getShipping_number() == null : this.getShipping_number().equals(other.getShipping_number()))
            && (this.getBatch_id() == null ? other.getBatch_id() == null : this.getBatch_id().equals(other.getBatch_id()))
            && (this.getBatch_code() == null ? other.getBatch_code() == null : this.getBatch_code().equals(other.getBatch_code()))
            && (this.getSales_date() == null ? other.getSales_date() == null : this.getSales_date().equals(other.getSales_date()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getRemark() == null ? other.getRemark() == null : this.getRemark().equals(other.getRemark()))
            && (this.getAttr1() == null ? other.getAttr1() == null : this.getAttr1().equals(other.getAttr1()))
            && (this.getAttr2() == null ? other.getAttr2() == null : this.getAttr2().equals(other.getAttr2()))
            && (this.getAttr3() == null ? other.getAttr3() == null : this.getAttr3().equals(other.getAttr3()))
            && (this.getAttr4() == null ? other.getAttr4() == null : this.getAttr4().equals(other.getAttr4()))
            && (this.getCreate_by() == null ? other.getCreate_by() == null : this.getCreate_by().equals(other.getCreate_by()))
            && (this.getCreate_time() == null ? other.getCreate_time() == null : this.getCreate_time().equals(other.getCreate_time()))
            && (this.getUpdate_by() == null ? other.getUpdate_by() == null : this.getUpdate_by().equals(other.getUpdate_by()))
            && (this.getUpdate_time() == null ? other.getUpdate_time() == null : this.getUpdate_time().equals(other.getUpdate_time()))
            && (this.getIs_delete() == null ? other.getIs_delete() == null : this.getIs_delete().equals(other.getIs_delete()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getSales_id() == null) ? 0 : getSales_id().hashCode());
        result = prime * result + ((getSales_code() == null) ? 0 : getSales_code().hashCode());
        result = prime * result + ((getSales_name() == null) ? 0 : getSales_name().hashCode());
        result = prime * result + ((getOqc_id() == null) ? 0 : getOqc_id().hashCode());
        result = prime * result + ((getOqc_name() == null) ? 0 : getOqc_name().hashCode());
        result = prime * result + ((getOqc_code() == null) ? 0 : getOqc_code().hashCode());
        result = prime * result + ((getNotice_id() == null) ? 0 : getNotice_id().hashCode());
        result = prime * result + ((getCustomer_id() == null) ? 0 : getCustomer_id().hashCode());
        result = prime * result + ((getCustomer_code() == null) ? 0 : getCustomer_code().hashCode());
        result = prime * result + ((getCustomer_name() == null) ? 0 : getCustomer_name().hashCode());
        result = prime * result + ((getRecipient() == null) ? 0 : getRecipient().hashCode());
        result = prime * result + ((getTel() == null) ? 0 : getTel().hashCode());
        result = prime * result + ((getAddress() == null) ? 0 : getAddress().hashCode());
        result = prime * result + ((getCarrier() == null) ? 0 : getCarrier().hashCode());
        result = prime * result + ((getShipping_number() == null) ? 0 : getShipping_number().hashCode());
        result = prime * result + ((getBatch_id() == null) ? 0 : getBatch_id().hashCode());
        result = prime * result + ((getBatch_code() == null) ? 0 : getBatch_code().hashCode());
        result = prime * result + ((getSales_date() == null) ? 0 : getSales_date().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getRemark() == null) ? 0 : getRemark().hashCode());
        result = prime * result + ((getAttr1() == null) ? 0 : getAttr1().hashCode());
        result = prime * result + ((getAttr2() == null) ? 0 : getAttr2().hashCode());
        result = prime * result + ((getAttr3() == null) ? 0 : getAttr3().hashCode());
        result = prime * result + ((getAttr4() == null) ? 0 : getAttr4().hashCode());
        result = prime * result + ((getCreate_by() == null) ? 0 : getCreate_by().hashCode());
        result = prime * result + ((getCreate_time() == null) ? 0 : getCreate_time().hashCode());
        result = prime * result + ((getUpdate_by() == null) ? 0 : getUpdate_by().hashCode());
        result = prime * result + ((getUpdate_time() == null) ? 0 : getUpdate_time().hashCode());
        result = prime * result + ((getIs_delete() == null) ? 0 : getIs_delete().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", sales_id=").append(sales_id);
        sb.append(", sales_code=").append(sales_code);
        sb.append(", sales_name=").append(sales_name);
        sb.append(", oqc_id=").append(oqc_id);
        sb.append(", oqc_name=").append(oqc_name);
        sb.append(", oqc_code=").append(oqc_code);
        sb.append(", notice_id=").append(notice_id);
        sb.append(", customer_id=").append(customer_id);
        sb.append(", customer_code=").append(customer_code);
        sb.append(", customer_name=").append(customer_name);
        sb.append(", recipient=").append(recipient);
        sb.append(", tel=").append(tel);
        sb.append(", address=").append(address);
        sb.append(", carrier=").append(carrier);
        sb.append(", shipping_number=").append(shipping_number);
        sb.append(", batch_id=").append(batch_id);
        sb.append(", batch_code=").append(batch_code);
        sb.append(", sales_date=").append(sales_date);
        sb.append(", status=").append(status);
        sb.append(", remark=").append(remark);
        sb.append(", attr1=").append(attr1);
        sb.append(", attr2=").append(attr2);
        sb.append(", attr3=").append(attr3);
        sb.append(", attr4=").append(attr4);
        sb.append(", create_by=").append(create_by);
        sb.append(", create_time=").append(create_time);
        sb.append(", update_by=").append(update_by);
        sb.append(", update_time=").append(update_time);
        sb.append(", is_delete=").append(is_delete);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}