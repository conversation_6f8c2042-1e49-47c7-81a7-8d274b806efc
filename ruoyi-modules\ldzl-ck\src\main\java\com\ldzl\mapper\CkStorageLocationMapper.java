package com.ldzl.mapper;

import com.ldzl.dto.CapacityDTO;
import com.ldzl.pojo.CkStorageLocation;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ck_storage_location(库区表)】的数据库操作Mapper
* @createDate 2025-07-10 17:18:31
* @Entity com.ldzl.pojo.CkStorageLocation
*/
public interface CkStorageLocationMapper extends BaseMapper<CkStorageLocation> {

    /**
     * 查询指定仓库下的库区
     * @param location
     * @return
     */
    List<CkStorageLocation> selectLocation(CkStorageLocation location);

    /**
     * 查询指定仓库下的库区容量
     * @return
     */
    List<CapacityDTO> findCapacity_l(CkStorageLocation location);
}




