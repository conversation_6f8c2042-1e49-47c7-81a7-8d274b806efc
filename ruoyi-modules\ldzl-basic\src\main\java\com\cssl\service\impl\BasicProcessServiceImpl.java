package com.cssl.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cssl.mapper.BasicNumbersMapper;
import com.cssl.mapper.BasicProcessMapper;
import com.cssl.mapper.BasicProcessdetailsMapper;
import com.cssl.pojo.BasicBomdetails;
import com.cssl.pojo.BasicNumbers;
import com.cssl.pojo.BasicProcess;
import com.cssl.pojo.BasicProcessdetails;
import com.cssl.pojo.vo.BasicBomdetailsVo;
import com.cssl.service.BasicNumbersService;
import com.cssl.service.BasicProcessService;
import com.cssl.service.BasicProcessdetailsService;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class BasicProcessServiceImpl extends ServiceImpl<BasicProcessMapper, BasicProcess> implements BasicProcessService {
    @Resource
    private BasicProcessMapper basicProcessMapper;

    @Resource
    private BasicProcessdetailsService basicProcessdetailsService;
    @Override
    public List<BasicProcess> listBasicProcess(BasicProcess basicProcess) {
        return basicProcessMapper.listBasicProcess(basicProcess);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int addBasicProcess(BasicProcess basicProcess) {
        basicProcess.setCreate_by(SecurityUtils.getUsername());
        basicProcess.setCreate_time(new Date());
        basicProcess.setIs_delete("0");
        int num=basicProcessMapper.addBasicProcess(basicProcess);
        if(num>0 && basicProcess.getProcess_id() != null){
                List<BasicProcessdetails> list=basicProcess.getBasicProcessdetailsList();
                System.out.println("efef:"+list);
                if(list!=null && !list.isEmpty()){
                    for(BasicProcessdetails b:list){
                        b.setCreate_by(SecurityUtils.getUsername());
                        b.setCreate_time(new Date());
                        b.setIs_delete("0");
                        b.setProcess_id(basicProcess.getProcess_id());
                        System.out.println("sdndn:"+basicProcess.getProcess_id());
                        basicProcessdetailsService.save(b);
                }


                System.out.println("sss:"+list);

            }
        }
        return num;
    }

    @Override
    @Transactional
    public int updateBasicProcess(BasicProcess basicProcess) {
        basicProcess.setUpdate_by(SecurityUtils.getUsername());
        basicProcess.setUpdate_time(new Date());
        int num=basicProcessMapper.updateBasicProcess(basicProcess);
        List<BasicProcessdetails> list=basicProcess.getBasicProcessdetailsList();
        if (list != null && !list.isEmpty()) {
            // 查询已有ID
            List<Long> existingIds = basicProcessdetailsService.listBasicProcessdetailsByProcessId(basicProcess.getProcess_id())
                    .stream()
                        .map(BasicProcessdetails::getProcessdetails_id)
                    .collect(Collectors.toList());

            List<BasicProcessdetails> insertList = new ArrayList<>();
            List<BasicProcessdetails> updateList = new ArrayList<>();

            for (BasicProcessdetails detail : list) {
                detail.setProcess_id(basicProcess.getProcess_id());
                if (detail.getProcessdetails_id() != null) {
                    // 更新
                    detail.setUpdate_by(SecurityUtils.getUsername());
                    detail.setUpdate_time(new Date());
                    updateList.add(detail);
                } else {
                    // 新增
                    detail.setCreate_by(SecurityUtils.getUsername());
                    detail.setCreate_time(new Date());
                    detail.setIs_delete("0");
                    insertList.add(detail);
                }
            }

            // 批量操作
            if (!insertList.isEmpty()) {
                basicProcessdetailsService.saveBatch(insertList);
            }
            if (!updateList.isEmpty()) {
                basicProcessdetailsService.updateBatchById(updateList);
            }

            // 删除未提交的ID
            Set<Long> newIds = list.stream()
                    .map(BasicProcessdetails::getProcessdetails_id)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            List<Long> toDeleteIds = existingIds.stream()
                    .filter(id -> !newIds.contains(id))
                    .collect(Collectors.toList());

            if (!toDeleteIds.isEmpty()) {
                basicProcessdetailsService.delBatchBasicProcessdetails(toDeleteIds);
            }
        } else {
            basicProcessdetailsService.delBasicProcessdetails(basicProcess.getProcess_id());
        }

        return num;
    }

    @Override
    @Transactional
    public int delBasicProcess(Long process_id) {
        basicProcessdetailsService.delBasicProcessdetails(process_id);
        return basicProcessMapper.delBasicProcess(process_id);
    }

    @Override
    public List<BasicProcess> selectBasicProcessByIds(List<Long> processIds) {
        if (processIds == null || processIds.isEmpty()){
            return Collections.emptyList();
        }
        return basicProcessMapper.selectBatchIds(processIds);
    }

    @Override
    public int delBatchBasicProcess(List<Long> processIds) {
        basicProcessdetailsService.delBatchBasicProcessdetailsByProcessId(processIds);
        return basicProcessMapper.delBatchBasicProcess(processIds);
    }
}
