package com.ruoyi.common.security.config;

import java.text.SimpleDateFormat;
import java.util.TimeZone;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.annotation.Bean;

/**
 * 系统配置
 *
 * <AUTHOR>
 */
public class ApplicationConfig
{
    /**
     * 时区配置和日期格式配置
     */
    @Bean
    public Jackson2ObjectMapperBuilderCustomizer jacksonObjectMapperCustomization()
    {
        return jacksonObjectMapperBuilder -> {
            // 设置时区
            jacksonObjectMapperBuilder.timeZone(TimeZone.getDefault());
        };
    }
}
