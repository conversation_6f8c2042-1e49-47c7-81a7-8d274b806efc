<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cssl.mapper.BasicWlflzMapper">
    <resultMap id="selectBasicWlfl" type="com.cssl.pojo.BasicWlflz">
        <id column="material_subcategory_id" property="material_subcategory_id"/>
        <association property="basicWlfl" javaType="com.cssl.pojo.BasicWlfl">
            <id column="material_classification_id" property="material_classification_id"/>
        </association>


    </resultMap>

    <insert id="insertBasicWlflz" parameterType="com.cssl.pojo.BasicWlflz"  useGeneratedKeys="true" keyProperty="material_subcategory_id">
        insert into basic_wlflz
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="material_subcategory_code != null and material_subcategory_code !=''">material_subcategory_code,</if>
            <if test="material_subcategory_name != null and material_subcategory_name != ''">material_subcategory_name,</if>
            <if test="create_by != null and create_by != ''">create_by,</if>
            <if test="create_time != null">create_time,</if>
            <if test="is_delete != null">is_delete,</if>
            <if test="material_classification_id != null and material_classification_id !=''">material_classification_id</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="material_subcategory_code != null and material_subcategory_code !=''">#{material_subcategory_code},</if>
            <if test="material_subcategory_name != null and material_subcategory_name != ''">#{material_subcategory_name},</if>
            <if test="create_by != null and create_by != ''">#{create_by},</if>
            <if test="create_time != null">#{create_time},</if>
            <if test="is_delete != null">#{is_delete},</if>
            <if test="material_classification_id != null and material_classification_id !=''">#{material_classification_id}</if>
        </trim>
    </insert>

    <update id="updateBasicWlflz">
        update basic_wlflz
        <trim prefix="SET" suffixOverrides=",">
            <if test="material_subcategory_name != null and material_subcategory_name != ''">material_subcategory_name = #{material_subcategory_name},</if>
        </trim>
        where material_subcategory_id = #{material_subcategory_id}
    </update>

    <update id="delBasicWlflz">
        update basic_wlflz
        <trim prefix="SET" suffixOverrides=",">
            <if test="wlflz_del != null ">is_delete =1,</if>
        </trim>
        where material_subcategory_id = #{material_subcategory_id}
    </update>

    <select id="selectBasicWlflzList" resultMap="selectBasicWlfl">
        SELECT * from basic_wlflz wz inner join basic_wlfl wl on wl.material_classification_id=wz.material_classification_id
        WHERE wl.is_delete=0 and wz.is_delete=0
        <if test="material_subcategory_name!=null and material_subcategory_name!=''">
            and wz.material_subcategory_name like CONCAT('%',#{material_subcategory_name},'%')
        </if>

<!--        <if test="basicWlfl != null and basicWlfl.material_classification_name!=null and basicWlfl.material_classification_name!=''">-->
<!--            and wl.material_classification_name=like CONCAT('%',#{basicWlfl.material_classification_name},'%')-->
<!--        </if>-->
<!--        <if test="basicWlfl !=null and basicWlfl.material_classification_id !=null">-->
<!--            and wl.material_classification_id=#{basicWlfl.material_classification_id}-->
<!--        </if>-->
    </select>


    <!-- 从表查询 -->
    <select id="selectBasicWlflzByMaterialClassificationId" parameterType="int" resultType="com.cssl.pojo.BasicWlflz">
        SELECT * FROM basic_wlflz WHERE material_classification_id = #{material_classification_id} AND is_delete = '0'
    </select>
</mapper>