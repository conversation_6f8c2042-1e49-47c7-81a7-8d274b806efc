# Tomcat
server:
  port: 8091

# Spring
spring: 
  application:
    # 应用名称
    name: spring-ai-alibaba
  profiles:
    # 环境配置
    active: dev
  cloud:
    compatibility-verifier:
      enabled: false
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 127.0.0.1:8848
      config:
        # 配置中心地址
        server-addr: 127.0.0.1:8848
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
  ai:
    dashscope:
      api-key: sk-2f9aebec16914deb9a1572819c871c36 