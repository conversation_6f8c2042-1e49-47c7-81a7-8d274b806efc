/*
 Navicat Premium Data Transfer

 Source Server         : yunSQL
 Source Server Type    : MySQL
 Source Server Version : 50744
 Source Host           : ***************:3322
 Source Schema         : ldzlmes

 Target Server Type    : MySQL
 Target Server Version : 50744
 File Encoding         : 65001

 Date: 02/07/2025 15:46:16
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for basic_numbers
-- ----------------------------
DROP TABLE IF EXISTS `basic_numbers`;
CREATE TABLE `basic_numbers`  (
  `en_code` int(11) NOT NULL AUTO_INCREMENT COMMENT '编号归职Id',
  `en_form` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '目标表单',
  `en_prefix` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '编号前缀',
  `en_time` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '时间规则',
  `en_num` int(10) UNSIGNED NOT NULL COMMENT '流水号/位',
  `en_step` int(11) NOT NULL COMMENT '步长',
  `en_flushed` int(11) NOT NULL COMMENT '按时间刷新（1.日,2.月3.年）',
  `en_rules` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '编号生成规则',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '操作人',
  `create_time` datetime(0) NOT NULL COMMENT '操作时间',
  `is_del` int(10) UNSIGNED ZEROFILL NOT NULL DEFAULT 0000000001 COMMENT '逻辑删除',
  `max_num` int(11) NOT NULL COMMENT '最大值',
  `last_date` datetime(0) NOT NULL COMMENT '最后一次更新时间',
  PRIMARY KEY (`en_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of basic_numbers
-- ----------------------------
INSERT INTO `basic_numbers` VALUES (1, '生产订单', 'SCDT', 'yyyyMMdd', 8, 1, 1, 'SCDT2025062701', 'admin', '2025-06-30 16:29:40', 0000000000, 10000, '2025-07-01 15:38:01');
INSERT INTO `basic_numbers` VALUES (2, '销售订单', 'XSDT', 'yyyyMMddHHmmss', 10, 2, 1, 'XSDT2025063001', 'admin', '2025-06-30 16:29:40', 0000000000, 10000, '2025-07-01 15:10:26');

SET FOREIGN_KEY_CHECKS = 1;
