package com.cssl.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("basic_wl")
public class BasicWlgl extends BaseEntity{
    @TableId(value ="material_id",type = IdType.AUTO)
    private Long material_id; // 物料id
    private String material_code; // 物料编码
    private String material_name; // 物料名称
    private String material_sfn; // 规格型号
    private String material_unit; // 单位
    private String material_type; // 物料类型
    private String material_mp; // 物料属性
    private Date material_expirydate; // 有效时间
    private Date material_alertdays; // 报警时间
    private Integer material_stockupperLimit; // 库存上限
    private Integer material_stocklowerLimit; // 库存下限
    private BigDecimal material_purchasePrice; // 采购价格
    private Long material_subcategory_id; // 物料分类id
    private String remarks; // 备注
    private String img; // 物料图片
    private String create_by;
    private Date create_time;
    private String update_by;
    private Date update_time;
    private String is_detele;
    private BigDecimal Inventory;
    @TableField(exist = false)
    private BasicWlflz basicWlflz;
    private MultipartFile file;
    private String material_status;
    @TableField(exist = false)
    private BasicWlfl basicWlfl;
}
