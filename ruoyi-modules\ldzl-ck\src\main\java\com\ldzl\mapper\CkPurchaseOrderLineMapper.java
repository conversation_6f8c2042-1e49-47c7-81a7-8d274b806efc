package com.ldzl.mapper;

import com.ldzl.dto.ArrivalNoticeDTO;
import com.ldzl.pojo.CkPurchaseOrder;
import com.ldzl.pojo.CkPurchaseOrderLine;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ck_purchase_order_line(采购订单详情表)】的数据库操作Mapper
* @createDate 2025-07-11 11:17:44
* @Entity com.ldzl.pojo.CkPurchaseOrderLine
*/
public interface CkPurchaseOrderLineMapper extends BaseMapper<CkPurchaseOrderLine> {

    /**
     * 查询到货通知
     * @param po
     * @return
     */
    List<ArrivalNoticeDTO> selectArrivalNotice(CkPurchaseOrder po);

    /**
     * 查询可入库的采购订单
     * @param arr
     * @return
     */
    List<ArrivalNoticeDTO> selectOrderStore(ArrivalNoticeDTO arr);

    /**
     * 移除指定的采购商品
     * @param line_id
     * @return
     */
    boolean updateIs_delete(@Param("line_id") Long line_id);

    /**
     * 移除指定采购单的采购商品
     * @param po_id
     * @return
     */
    int updateIs_delete_line(@Param("po_id") Long po_id);

    /**
     * 更新采购商品待上架数量
     * @return
     */
    int updateArrived_num(@Param("line_id") Long line_id,
                          @Param("receipt_num") BigDecimal receipt_num);

    int updateList_num(@Param("line_id") Long line_id,
                       @Param("receipt_num") BigDecimal receipt_num);

    /**
     * 批量更新采购商品已上架数量
     * @param lineIds
     * @return
     */
    int updateTreat_list_num(@Param("lineIds") List<Long> lineIds);

    /**
     * 回退 更新采购商品待上架数量
     * @return
     */
    int updateArrived_num_back(@Param("line_id") Long line_id,
                               @Param("receipt_num") BigDecimal receipt_num);
}




