package com.cssl.contrller;
import com.cssl.pojo.BasicProcess;
import com.cssl.pojo.BasicProcessdetails;
import com.cssl.pojo.vo.BasicProcessdetailsVo;
import com.cssl.service.BasicProcessService;
import com.cssl.service.BasicProcessdetailsService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;
import com.ruoyi.common.core.utils.bean.BeanUtils;
import com.ruoyi.system.api.domain.BasicProcessDto;

@RestController
@RequestMapping("/process")
public class BasicProcessContrller extends BaseController {
    @Resource
    private BasicProcessService basicProcessService;
    @Resource
    private BasicProcessdetailsService basicProcessdetailsService;

    /**
     * 根据ID列表查询工序列表
     */
    @PostMapping("/listByIds")
    public AjaxResult listByIds(@RequestBody List<Long> processIds)
    {
        List<BasicProcess> list = basicProcessService.selectBasicProcessByIds(processIds);
        List<BasicProcessDto> dtoList = list.stream().map(item -> {
            BasicProcessDto dto = new BasicProcessDto();
            BeanUtils.copyProperties(item, dto);
            return dto;
        }).collect(Collectors.toList());
        return success(dtoList);
    }

    //查询工序信息
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody BasicProcess basicProcess)
    {
        startPage();
        List<BasicProcess> list =basicProcessService.listBasicProcess(basicProcess);
        return getDataTable(list);
    }

    //添加工序信息
    @Log(title = "添加工序信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasicProcess basicProcess)
    {

        return toAjax(basicProcessService.addBasicProcess(basicProcess));
    }

    //修改工序信息
    @Log(title = "修改工序信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasicProcess basicProcess)
    {
        return toAjax(basicProcessService.updateBasicProcess(basicProcess));
    }

    //删除工序信息
    @Log(title = "删除工序信息", businessType = BusinessType.UPDATE)
    @PutMapping("/dp/{process_id}")
    public AjaxResult edit1(@PathVariable Long process_id)
    {
        return toAjax(basicProcessService.delBasicProcess(process_id));
    }

    //查看工序中物料配料和工位信息
    @PostMapping("/find")
    public TableDataInfo find(@RequestBody BasicProcessdetails basicProcessdetails)
    {

        List<BasicProcessdetails> list =basicProcessdetailsService.listBasicProcessdetails(basicProcessdetails);
        return getDataTable(list);
    }

    //批量删除
    @Log(title = "批量删除", businessType = BusinessType.UPDATE)
    @PutMapping("/batch/{processIds}")
    public AjaxResult edit(@PathVariable List<Long> processIds)
    {
        return toAjax(basicProcessService.delBatchBasicProcess(processIds));
    }

    //根据id查询工序详细信息
    @PostMapping("/find/{process_id}")
    public TableDataInfo find(@PathVariable Long process_id)
    {

        List<BasicProcessdetails> list =basicProcessdetailsService.listBasicProcessdetailsByProcessId(process_id);
        return getDataTable(list);
    }

    //根据id查询工序详细信息
    @PostMapping("/find1/{process_id}")
    public TableDataInfo find1(@PathVariable Long process_id)
    {
        List<BasicProcessdetailsVo> list =basicProcessdetailsService.listBasicProcessdetailsvoBy(process_id);
        return getDataTable(list);
    }

    //查询不分页工序信息
    @PostMapping("/listpor")
    public TableDataInfo listpor(@RequestBody BasicProcess basicProcess)
    {

        List<BasicProcess> list =basicProcessService.listBasicProcess(basicProcess);
        return getDataTable(list);
    }
}
