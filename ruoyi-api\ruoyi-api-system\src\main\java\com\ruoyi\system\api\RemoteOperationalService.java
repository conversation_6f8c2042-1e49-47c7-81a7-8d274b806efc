package com.ruoyi.system.api;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.system.api.domain.BasicOperationalDto;
import com.ruoyi.system.api.factory.RemoteOperationalFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

/**
 * 工艺路线服务
 * 
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteOperationalService", value = ServiceNameConstants.LDZL_BASIC_SERVICE, fallbackFactory = RemoteOperationalFallbackFactory.class)
public interface RemoteOperationalService
{
    /**
     * 根据产品ID查询工艺路线列表
     *
     * @param productId 产品ID
     * @param source 请求来源
     * @return 结果
     */
    @GetMapping("/bop/material/{productId}")
    public R<List<BasicOperationalDto>> getInfoByProductId(@PathVariable("productId") Long productId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

} 