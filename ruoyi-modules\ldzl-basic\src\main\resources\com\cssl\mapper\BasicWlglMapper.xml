<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cssl.mapper.BasicWlglMapper">
    <resultMap id="selectBasicWlgl" type="com.cssl.pojo.BasicWlgl">
        <id column="material_id" property="material_id"/>
        <association property="basicWlflz" javaType="com.cssl.pojo.BasicWlflz">
            <id column="material_subcategory_id" property="material_subcategory_id"/>
        </association>
        <association property="basicWlfl" javaType="com.cssl.pojo.BasicWlfl">
            <id column="material_classification_id" property="material_classification_id"/>
        </association>
    </resultMap>

    <insert id="addBasicWlgl" parameterType="com.cssl.pojo.BasicWlgl"  useGeneratedKeys="true" keyProperty="material_id">
        INSERT into basic_wl
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="material_code!= null and material_code!=''">material_code,</if>
            <if test="material_name != null and material_name != ''">material_name,</if>
            <if test="material_sfn != null and material_sfn !='' ">material_sfn,</if>
            <if test="material_unit != null and material_unit != ''" >material_unit,</if>
            <if test="material_type != null and material_type !=''">material_type,</if>
            <if test="material_mp != null and material_mp !=''">material_mp,</if>
            <if test="material_expirydate != null">material_expirydate,</if>
            <if test="material_alertdays != null">material_alertdays,</if>
            <if test="material_stockupperLimit != null ">material_stockupperLimit,</if>
            <if test="material_stocklowerLimit != null ">material_stocklowerLimit,</if>
            <if test="material_purchasePrice != null ">material_purchasePrice,</if>
            <if test="remarks != null ">remarks,</if>
            <if test="img != null and img !=''">img,</if>
            <if test="create_by != null ">create_by,</if>
            <if test="create_time != null ">create_time,</if>
            <if test="is_detele != null ">is_detele,</if>
            <if test="material_status != null ">material_status,</if>
            <if test="Inventory != null ">Inventory,</if>
            <if test="basicWlflz !=null and basicWlflz.material_subcategory_id !=null">material_subcategory_id</if>
            <if test="basicWlfl !=null and basicWlfl.material_classification_id !=null">material_classification_id</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="material_code!= null and material_code!=''">#{material_code},</if>
            <if test="material_name != null and material_name != ''">#{material_name},</if>
            <if test="material_sfn != null and material_sfn !='' ">#{material_sfn},</if>
            <if test="material_unit != null and material_unit != ''" >#{material_unit},</if>
            <if test="material_type != null and material_type !=''">#{material_type},</if>
            <if test="material_mp != null and material_mp !=''">#{material_mp},</if>
            <if test="material_expirydate != null">#{material_expirydate},</if>
            <if test="material_alertdays != null">#{material_alertdays},</if>
            <if test="material_stockupperLimit != null ">#{material_stockupperLimit},</if>
            <if test="material_stocklowerLimit != null ">#{material_stocklowerLimit},</if>
            <if test="material_purchasePrice != null ">#{material_purchasePrice},</if>
            <if test="remarks != null ">#{remarks},</if>
            <if test="img != null and img !=''">#{img},</if>
            <if test="create_by != null ">#{create_by},</if>
            <if test="create_time != null ">#{create_time},</if>
            <if test="is_detele != null ">#{is_detele},</if>
            <if test="material_status != null ">#{material_status},</if>
            <if test="Inventory != null ">#{Inventory},</if>
            <if test="basicWlflz !=null and basicWlflz.material_subcategory_id !=null">#{basicWlflz.material_subcategory_id}</if>
            <if test="basicWlfl !=null and basicWlfl.material_classification_id !=null">#{basicWlflz.material_classification_id}</if>
        </trim>
    </insert>

    <update id="updateBasicwlgl">
        update basic_wl
        <trim prefix="SET" suffixOverrides=",">
            <if test="material_name != null and material_name !='' ">material_name = #{material_name},</if>
            <if test="material_sfn != null and material_sfn !='' ">material_sfn=#{material_sfn},</if>
            <if test="material_unit != null and material_unit != ''" >material_unit=#{material_unit},</if>
            <if test="material_type != null and material_type !=''">material_type=#{material_type},</if>
            <if test="material_mp != null and material_mp !=''">material_mp=#{material_mp},</if>
            <if test="material_category != null and material_category !=''">material_category=#{material_category},</if>
            <if test="material_expirydate != null">material_expirydate=#{material_expirydate},</if>
            <if test="material_alertdays != null">material_alertdays=#{material_alertdays},</if>
            <if test="material_stockupperLimit != null ">material_stockupperLimit=#{material_stockupperLimit},</if>
            <if test="material_stocklowerLimit != null ">material_stocklowerLimit=#{material_stocklowerLimit},</if>
            <if test="material_purchasePrice != null ">material_purchasePrice=#{material_purchasePrice},</if>
            <if test="remarks != null ">remarks=#{remarks},</if>
            <if test="img != null and img !=''">img=#{img},</if>
            <if test="create_by != null ">create_by=#{create_by},</if>
            <if test="create_time != null ">create_time=#{create_time},</if>
            <if test="material_status != null ">material_status=#{material_status},</if>
            <if test="Inventory != null ">Inventory=#{Inventory},</if>
            <if test="basicWlflz !=null and basicWlflz.material_subcategory_id !=null">material_subcategory_id=#{basicWlflz.material_subcategory_id},</if>
            <if test="basicWlfl !=null and basicWlfl.material_classification_id !=null">material_classification_id=#{basicWlfl.material_classification_id}</if>
        </trim>
        where material_id = #{material_id}
    </update>

    <update id="delBasicWlgl">
        update basic_wl
        <trim prefix="SET" suffixOverrides=",">
            <if test="is_detele !=null">is_detele=1</if>
        </trim>
        where material_id = #{material_id}
    </update>

    <select id="listBasicWlgl" resultType="com.cssl.pojo.BasicWlgl">
        SELECT * from basic_wl wl inner join basic_wlflz lz on wl.material_subcategory_id=lz.material_subcategory_id inner JOIN
        basic_wlfl fl on lz.material_classification_id=fl.material_classification_id WHERE wl.is_detele=0 and lz.is_delete=0 and fl.is_delete=0
        <if test="material_name!=null and material_name!=''">
            and wl.material_name like CONCAT('%',#{material_name},'%')
        </if>
        <if test="material_type !=null and material_type !=''">
            and wl.material_type=#{material_type}
        </if>
        <if test="basicWlfl !=null and basicWlfl.material_classification_id !=null">
            and fl.material_classification_id=#{basicWlfl.material_classification_id}
        </if>
        <if test="basicWlflz!=null and basicWlflz.material_subcategory_id!=null">
            and lz.material_subcategory_id=#{basicWlflz.material_subcategory_id}
        </if>



    </select>
</mapper>