package com.cssl.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cssl.mapper.BasicNumbersMapper;
import com.cssl.mapper.BasicProductionlineMapper;
import com.cssl.pojo.BasicNumbers;
import com.cssl.pojo.BasicProductionline;
import com.cssl.service.BasicNumbersService;
import com.cssl.service.BasicProductionlineService;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class BasicProductionlineServiceImpl extends ServiceImpl<BasicProductionlineMapper, BasicProductionline> implements BasicProductionlineService {
    @Resource
    private BasicProductionlineMapper basicProductionlineMapper;

    @Override
    public List<BasicProductionline> listBasicProductionline(BasicProductionline basicProductionline) {
        return basicProductionlineMapper.listBasicProductionline(basicProductionline);
    }

    @Override
    public int addBasicProductionline(BasicProductionline basicProductionline) {
        basicProductionline.setCreate_by(SecurityUtils.getUsername());
        basicProductionline.setCreate_time(new Date());
        basicProductionline.setIs_delete("0");
        return basicProductionlineMapper.addBasicProductionline(basicProductionline);
    }

    @Override
    public int updateBasicProductionline(BasicProductionline basicProductionline) {
        basicProductionline.setUpdate_by(SecurityUtils.getUsername());
        basicProductionline.setUpdate_time(new Date());
        return basicProductionlineMapper.updateBasicProductionline(basicProductionline);
    }

    @Override
    public int delBasicProductionline(Long production_line_id) {
        return basicProductionlineMapper.delBasicProductionline(production_line_id);
    }
}
