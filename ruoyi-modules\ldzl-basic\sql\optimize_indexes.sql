-- ============================================
-- 钢材MES系统 - 产品查询性能优化索引脚本
-- 优化 basic_products + basic_wlflz + basic_wlfl 三表JOIN查询
-- ============================================

-- 1. 为 basic_products 表添加关键索引
-- 索引1：material_subcategory_id (用于JOIN basic_wlflz)
CREATE INDEX idx_products_subcategory ON basic_products(material_subcategory_id);

-- 索引2：is_delete (用于WHERE条件过滤)
CREATE INDEX idx_products_delete ON basic_products(is_delete);

-- 索引3：复合索引 - is_delete + material_subcategory_id (最优化JOIN+过滤)
CREATE INDEX idx_products_delete_subcategory ON basic_products(is_delete, material_subcategory_id);

-- 索引4：产品名称模糊查询优化
CREATE INDEX idx_products_name ON basic_products(product_name);

-- 索引5：产品类型查询优化
CREATE INDEX idx_products_type ON basic_products(product_type);

-- 2. 为 basic_wlflz 表添加关键索引
-- 索引1：material_subcategory_id (用于JOIN basic_products)
CREATE INDEX idx_wlflz_subcategory ON basic_wlflz(material_subcategory_id);

-- 索引2：material_classification_id (用于JOIN basic_wlfl)
CREATE INDEX idx_wlflz_classification ON basic_wlflz(material_classification_id);

-- 索引3：is_delete (用于WHERE条件过滤)
CREATE INDEX idx_wlflz_delete ON basic_wlflz(is_delete);

-- 索引4：复合索引 - is_delete + material_subcategory_id + material_classification_id
CREATE INDEX idx_wlflz_delete_sub_class ON basic_wlflz(is_delete, material_subcategory_id, material_classification_id);

-- 3. 为 basic_wlfl 表添加关键索引
-- 索引1：material_classification_id (用于JOIN basic_wlflz)
CREATE INDEX idx_wlfl_classification ON basic_wlfl(material_classification_id);

-- 索引2：is_delete (用于WHERE条件过滤)
CREATE INDEX idx_wlfl_delete ON basic_wlfl(is_delete);

-- 索引3：复合索引 - is_delete + material_classification_id (最优化JOIN+过滤)
CREATE INDEX idx_wlfl_delete_classification ON basic_wlfl(is_delete, material_classification_id);

-- ============================================
-- 验证索引创建情况
-- ============================================

-- 查看 basic_products 表的索引
SHOW INDEX FROM basic_products;

-- 查看 basic_wlflz 表的索引
SHOW INDEX FROM basic_wlflz;

-- 查看 basic_wlfl 表的索引
SHOW INDEX FROM basic_wlfl;

-- ============================================
-- 性能测试查询（用于验证优化效果）
-- ============================================

-- 测试原始查询性能
EXPLAIN SELECT * 
FROM basic_products pr 
INNER JOIN basic_wlflz lz ON pr.material_subcategory_id = lz.material_subcategory_id 
INNER JOIN basic_wlfl fl ON lz.material_classification_id = fl.material_classification_id 
WHERE pr.is_delete = 0 AND lz.is_delete = 0 AND fl.is_delete = 0;

-- 测试带条件的查询性能
EXPLAIN SELECT * 
FROM basic_products pr 
INNER JOIN basic_wlflz lz ON pr.material_subcategory_id = lz.material_subcategory_id 
INNER JOIN basic_wlfl fl ON lz.material_classification_id = fl.material_classification_id 
WHERE pr.is_delete = 0 AND lz.is_delete = 0 AND fl.is_delete = 0 
AND pr.product_name LIKE '%钢板%'; 