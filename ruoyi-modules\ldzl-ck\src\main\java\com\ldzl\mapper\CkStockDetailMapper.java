package com.ldzl.mapper;

import com.ldzl.dto.QueryInventoryNumDTO;
import com.ldzl.pojo.CkStockDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ck_stock_detail(库存明细表)】的数据库操作Mapper
* @createDate 2025-07-04 11:38:23
* @Entity com.ldzl.pojo.CkStockDetail
*/
public interface CkStockDetailMapper extends BaseMapper<CkStockDetail> {

    /**
     * 按条件筛选库存数据
     * @param stockDetail
     * @return
     */
    List<CkStockDetail> selectStock(CkStockDetail stockDetail);

    /**
     * 查询单个产品的所有库存
     * @param item_code
     * @return
     */
    QueryInventoryNumDTO queryStock(String item_code);

    /**
     * 查询多个产品的所有库存
     * @param listCode
     * @return
     */
    List<QueryInventoryNumDTO> queryStockList(@Param("listCode") List<String> listCode);
}




