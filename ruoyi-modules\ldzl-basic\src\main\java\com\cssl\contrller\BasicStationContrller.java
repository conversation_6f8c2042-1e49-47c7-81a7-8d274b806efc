package com.cssl.contrller;

import com.cssl.pojo.BasicProduct;
import com.cssl.pojo.BasicProductionline;
import com.cssl.pojo.BasicStation;
import com.cssl.service.BasicStationService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;
import com.ruoyi.common.core.utils.bean.BeanUtils;
import com.ruoyi.system.api.domain.BasicStationDto;

@RestController
@RequestMapping("/sta")
public class BasicStationContrller extends BaseController {
    @Resource
    private BasicStationService basicStationService;

    /**
     * 根据工序ID列表查询关联的工位列表
     */
    @PostMapping("/listByProcessIds")
    public AjaxResult listByProcessIds(@RequestBody List<Long> processIds)
    {
        List<BasicStation> list = basicStationService.selectBasicStationByProcessIds(processIds);
        List<BasicStationDto> dtoList = list.stream().map(item -> {
            BasicStationDto dto = new BasicStationDto();
            BeanUtils.copyProperties(item, dto);
            return dto;
        }).collect(Collectors.toList());
        return success(dtoList);
    }

    /**
     * 根据ID列表查询工位列表
     */
    @PostMapping("/listByIds")
    public AjaxResult listByIds(@RequestBody List<Long> stationIds)
    {
        List<BasicStation> list = basicStationService.selectBasicStationByIds(stationIds);
        List<BasicStationDto> dtoList = list.stream().map(item -> {
            BasicStationDto dto = new BasicStationDto();
            BeanUtils.copyProperties(item, dto);
            return dto;
        }).collect(Collectors.toList());
        return success(dtoList);
    }

    //查询所有工位信息
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody BasicStation basicStation)
    {
        startPage();
        List<BasicStation> list =basicStationService.listBasicStation(basicStation);
        return getDataTable(list);
    }

    //添加工位信息
    @Log(title = "添加工位信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasicStation basicStation)
    {
        return toAjax(basicStationService.addBasicStation(basicStation));
    }

    //修改工位信息
    @Log(title = "修改工位信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasicStation basicStation)
    {
        return toAjax(basicStationService.updateBasicStation(basicStation));
    }

    //删除工位信息
    @Log(title = "删除工位", businessType = BusinessType.UPDATE)
    @PutMapping("/dp/{station_id}")
    public AjaxResult edit1(@PathVariable Long station_id)
    {
        return toAjax(basicStationService.delBasicStation(station_id));
    }
}
