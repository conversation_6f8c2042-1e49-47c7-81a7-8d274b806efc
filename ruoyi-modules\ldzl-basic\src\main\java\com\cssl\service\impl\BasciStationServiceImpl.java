package com.cssl.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cssl.mapper.BasicCustomersMapper;
import com.cssl.mapper.BasicStationMapper;
import com.cssl.pojo.BasicCustomers;
import com.cssl.pojo.BasicStation;
import com.cssl.service.BasicCustomersService;
import com.cssl.service.BasicStationService;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
@Service
public class BasciStationServiceImpl extends ServiceImpl<BasicStationMapper, BasicStation> implements BasicStationService {
    @Resource
    private BasicStationMapper basicStationMapper;
    @Override
    public List<BasicStation> listBasicStation(BasicStation basicStation) {
        return basicStationMapper.listBasicStation(basicStation);
    }

    @Override
    public int addBasicStation(BasicStation basicStation) {
        basicStation.setCreate_by(SecurityUtils.getUsername());
        basicStation.setCreate_time(new Date());
        basicStation.setIs_delete("0");
        return basicStationMapper.addBasicStation(basicStation);
    }

    @Override
    public int updateBasicStation(BasicStation basicStation) {
        basicStation.setUpdate_by(SecurityUtils.getUsername());
        basicStation.setUpdate_time(new Date());
        return basicStationMapper.updateBasicStation(basicStation);
    }

    @Override
    public int delBasicStation(Long station_id) {
        return basicStationMapper.deleteById(station_id);
    }

    @Override
    public List<BasicStation> selectBasicStationByIds(List<Long> stationIds) {
        if (stationIds == null || stationIds.isEmpty()){
            return Collections.emptyList();
        }
        return basicStationMapper.selectBatchIds(stationIds);
    }

    @Override
    public List<BasicStation> selectBasicStationByProcessIds(List<Long> processIds) {
        if (processIds == null || processIds.isEmpty()){
            return Collections.emptyList();
        }
        return basicStationMapper.selectBasicStationByProcessIds(processIds);
    }
}
