package com.ruoyi.system.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 批次记录表
 * @TableName ck_batch
 */
@TableName(value ="ck_batch")
@Data
public class CkBatch implements Serializable {
    /**
     * 批次ID
     */
    @TableId(type = IdType.AUTO)
    private Long batch_id;

    /**
     * 批次编号
     */
    private String batch_code;

    /**
     * 生产日期
     */
    private Date produce_date;

    /**
     * 有效期
     */
    private Date expire_date;

    /**
     * 入库日期
     */
    private Date recpt_date;

    /**
     * 供应商ID
     */
    private Long supplier_id;

    /**
     * 供应商名称
     */
    private String supplier_name;

    /**
     * 客户ID
     */
    private Long customer_id;

    /**
     * 客户名称
     */
    private String customer_name;

    /**
     * 销售订单ID
     */
    private Long co_id;

    /**
     * 销售订单编号
     */
    private String co_code;

    /**
     * 销售订单名称
     */
    private String co_name;

    /**
     * 采购订单ID
     */
    private Long po_id;

    /**
     * 采购订单编号
     */
    private String po_code;

    /**
     * 采购订单名称
     */
    private String po_name;

    /**
     * 生产工单ID
     */
    private Long work_order_id;

    /**
     * 生产工单编号
     */
    private String work_order_code;

    /**
     * 生产工单名称
     */
    private String work_order_name;

    /**
     * 生产批号
     */
    private String lot_number;

    /**
     * 质量状态
     */
    private String quality_status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 预留字段1
     */
    private String attr1;

    /**
     * 预留字段2
     */
    private String attr2;

    /**
     * 预留字段3
     */
    private Integer attr3;

    /**
     * 预留字段4
     */
    private Integer attr4;

    /**
     * 创建者
     */
    private String create_by;

    /**
     * 创建时间
     */
    private Date create_time;

    /**
     * 更新者
     */
    private String update_by;

    /**
     * 更新时间
     */
    private Date update_time;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    private String is_delete;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        CkBatch other = (CkBatch) that;
        return (this.getBatch_id() == null ? other.getBatch_id() == null : this.getBatch_id().equals(other.getBatch_id()))
            && (this.getBatch_code() == null ? other.getBatch_code() == null : this.getBatch_code().equals(other.getBatch_code()))
            && (this.getProduce_date() == null ? other.getProduce_date() == null : this.getProduce_date().equals(other.getProduce_date()))
            && (this.getExpire_date() == null ? other.getExpire_date() == null : this.getExpire_date().equals(other.getExpire_date()))
            && (this.getRecpt_date() == null ? other.getRecpt_date() == null : this.getRecpt_date().equals(other.getRecpt_date()))
            && (this.getSupplier_id() == null ? other.getSupplier_id() == null : this.getSupplier_id().equals(other.getSupplier_id()))
            && (this.getSupplier_name() == null ? other.getSupplier_name() == null : this.getSupplier_name().equals(other.getSupplier_name()))
            && (this.getCustomer_id() == null ? other.getCustomer_id() == null : this.getCustomer_id().equals(other.getCustomer_id()))
            && (this.getCustomer_name() == null ? other.getCustomer_name() == null : this.getCustomer_name().equals(other.getCustomer_name()))
            && (this.getCo_id() == null ? other.getCo_id() == null : this.getCo_id().equals(other.getCo_id()))
            && (this.getCo_code() == null ? other.getCo_code() == null : this.getCo_code().equals(other.getCo_code()))
            && (this.getCo_name() == null ? other.getCo_name() == null : this.getCo_name().equals(other.getCo_name()))
            && (this.getPo_id() == null ? other.getPo_id() == null : this.getPo_id().equals(other.getPo_id()))
            && (this.getPo_code() == null ? other.getPo_code() == null : this.getPo_code().equals(other.getPo_code()))
            && (this.getPo_name() == null ? other.getPo_name() == null : this.getPo_name().equals(other.getPo_name()))
            && (this.getWork_order_id() == null ? other.getWork_order_id() == null : this.getWork_order_id().equals(other.getWork_order_id()))
            && (this.getWork_order_code() == null ? other.getWork_order_code() == null : this.getWork_order_code().equals(other.getWork_order_code()))
            && (this.getWork_order_name() == null ? other.getWork_order_name() == null : this.getWork_order_name().equals(other.getWork_order_name()))
            && (this.getLot_number() == null ? other.getLot_number() == null : this.getLot_number().equals(other.getLot_number()))
            && (this.getQuality_status() == null ? other.getQuality_status() == null : this.getQuality_status().equals(other.getQuality_status()))
            && (this.getRemark() == null ? other.getRemark() == null : this.getRemark().equals(other.getRemark()))
            && (this.getAttr1() == null ? other.getAttr1() == null : this.getAttr1().equals(other.getAttr1()))
            && (this.getAttr2() == null ? other.getAttr2() == null : this.getAttr2().equals(other.getAttr2()))
            && (this.getAttr3() == null ? other.getAttr3() == null : this.getAttr3().equals(other.getAttr3()))
            && (this.getAttr4() == null ? other.getAttr4() == null : this.getAttr4().equals(other.getAttr4()))
            && (this.getCreate_by() == null ? other.getCreate_by() == null : this.getCreate_by().equals(other.getCreate_by()))
            && (this.getCreate_time() == null ? other.getCreate_time() == null : this.getCreate_time().equals(other.getCreate_time()))
            && (this.getUpdate_by() == null ? other.getUpdate_by() == null : this.getUpdate_by().equals(other.getUpdate_by()))
            && (this.getUpdate_time() == null ? other.getUpdate_time() == null : this.getUpdate_time().equals(other.getUpdate_time()))
            && (this.getIs_delete() == null ? other.getIs_delete() == null : this.getIs_delete().equals(other.getIs_delete()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getBatch_id() == null) ? 0 : getBatch_id().hashCode());
        result = prime * result + ((getBatch_code() == null) ? 0 : getBatch_code().hashCode());
        result = prime * result + ((getProduce_date() == null) ? 0 : getProduce_date().hashCode());
        result = prime * result + ((getExpire_date() == null) ? 0 : getExpire_date().hashCode());
        result = prime * result + ((getRecpt_date() == null) ? 0 : getRecpt_date().hashCode());
        result = prime * result + ((getSupplier_id() == null) ? 0 : getSupplier_id().hashCode());
        result = prime * result + ((getSupplier_name() == null) ? 0 : getSupplier_name().hashCode());
        result = prime * result + ((getCustomer_id() == null) ? 0 : getCustomer_id().hashCode());
        result = prime * result + ((getCustomer_name() == null) ? 0 : getCustomer_name().hashCode());
        result = prime * result + ((getCo_id() == null) ? 0 : getCo_id().hashCode());
        result = prime * result + ((getCo_code() == null) ? 0 : getCo_code().hashCode());
        result = prime * result + ((getCo_name() == null) ? 0 : getCo_name().hashCode());
        result = prime * result + ((getPo_id() == null) ? 0 : getPo_id().hashCode());
        result = prime * result + ((getPo_code() == null) ? 0 : getPo_code().hashCode());
        result = prime * result + ((getPo_name() == null) ? 0 : getPo_name().hashCode());
        result = prime * result + ((getWork_order_id() == null) ? 0 : getWork_order_id().hashCode());
        result = prime * result + ((getWork_order_code() == null) ? 0 : getWork_order_code().hashCode());
        result = prime * result + ((getWork_order_name() == null) ? 0 : getWork_order_name().hashCode());
        result = prime * result + ((getLot_number() == null) ? 0 : getLot_number().hashCode());
        result = prime * result + ((getQuality_status() == null) ? 0 : getQuality_status().hashCode());
        result = prime * result + ((getRemark() == null) ? 0 : getRemark().hashCode());
        result = prime * result + ((getAttr1() == null) ? 0 : getAttr1().hashCode());
        result = prime * result + ((getAttr2() == null) ? 0 : getAttr2().hashCode());
        result = prime * result + ((getAttr3() == null) ? 0 : getAttr3().hashCode());
        result = prime * result + ((getAttr4() == null) ? 0 : getAttr4().hashCode());
        result = prime * result + ((getCreate_by() == null) ? 0 : getCreate_by().hashCode());
        result = prime * result + ((getCreate_time() == null) ? 0 : getCreate_time().hashCode());
        result = prime * result + ((getUpdate_by() == null) ? 0 : getUpdate_by().hashCode());
        result = prime * result + ((getUpdate_time() == null) ? 0 : getUpdate_time().hashCode());
        result = prime * result + ((getIs_delete() == null) ? 0 : getIs_delete().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", batch_id=").append(batch_id);
        sb.append(", batch_code=").append(batch_code);
        sb.append(", produce_date=").append(produce_date);
        sb.append(", expire_date=").append(expire_date);
        sb.append(", recpt_date=").append(recpt_date);
        sb.append(", supplier_id=").append(supplier_id);
        sb.append(", supplier_name=").append(supplier_name);
        sb.append(", customer_id=").append(customer_id);
        sb.append(", customer_name=").append(customer_name);
        sb.append(", co_id=").append(co_id);
        sb.append(", co_code=").append(co_code);
        sb.append(", co_name=").append(co_name);
        sb.append(", po_id=").append(po_id);
        sb.append(", po_code=").append(po_code);
        sb.append(", po_name=").append(po_name);
        sb.append(", work_order_id=").append(work_order_id);
        sb.append(", work_order_code=").append(work_order_code);
        sb.append(", work_order_name=").append(work_order_name);
        sb.append(", lot_number=").append(lot_number);
        sb.append(", quality_status=").append(quality_status);
        sb.append(", remark=").append(remark);
        sb.append(", attr1=").append(attr1);
        sb.append(", attr2=").append(attr2);
        sb.append(", attr3=").append(attr3);
        sb.append(", attr4=").append(attr4);
        sb.append(", create_by=").append(create_by);
        sb.append(", create_time=").append(create_time);
        sb.append(", update_by=").append(update_by);
        sb.append(", update_time=").append(update_time);
        sb.append(", is_delete=").append(is_delete);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}