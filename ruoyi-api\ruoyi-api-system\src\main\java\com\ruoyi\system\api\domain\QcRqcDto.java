package com.ruoyi.system.api.domain;

import com.ruoyi.common.core.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class QcRqcDto {
    private static final long serialVersionUID = 1L;

    /** 检验单ID */
    private Long rqcId;

    /** 检验单编号 */
    @Excel(name = "检验单编号")
    private String rqcCode;

    /** 检验单名称 */
    @Excel(name = "检验单名称")
    private String rqcName;

    /** 检验模板ID */
    private Long templateId;

    /** 来源单据ID */
    private Long sourceDocId;

    /** 来源单据类型 */
    @Excel(name = "来源单据类型")
    private String sourceDocType;

    /** 来源单据编号 */
    @Excel(name = "来源单据编号")
    private String sourceDocCode;

    /** 来源单据行ID */
    private Long sourceLineId;

    /** 检验类型 */
    @Excel(name = "检验类型")
    private String rqcType;

    /** 物料ID */
    private Long materialId;

    /** 物料编码 */
    @Excel(name = "物料编码")
    private String materialCode;

    /** 物料名称 */
    @Excel(name = "物料名称")
    private String materialName;

    /** 规格型号 */
    private String materialSfn;

    /** 单位 */
    @Excel(name = "单位")
    private String unit;

    /** 单位名称 */
    private String unitName;

    /** 批次ID */
    private Long batchId;

    /** 批次号 */
    private String batchCode;

    /** 检测数量 */
    @Excel(name = "检测数量")
    private BigDecimal quantityCheckNum;

    /** 不合格数 */
    private BigDecimal quantityUnqualifiedNum;

    /** 合格品数量 */
    private BigDecimal quantityQualifiedNum;

    /** 检测结果 */
    @Excel(name = "检测结果")
    private String checkResult;

    /** 检测日期 */
    private Date inspectDate;

    /** 检测人员ID */
    private Long userId;

    /** 检测人员名称 */
    private String userName;

    /** 检测人员 */
    private String nickName;

    /** 单据状态(1/合格，2/不合格，3/待检) */
    @Excel(name = "单据状态(1/合格，2/不合格，3/待检)")
    private String status;

    /** 备注 */
    private String remark;

    /** 预留字段1 */
    private String attr1;

    /** 预留字段2 */
    private String attr2;

    /** 预留字段3 */
    private Long attr3;

    /** 预留字段4 */
    private Long attr4;

    /** 删除标志（0代表存在 1代表删除） */
    private String isDelete;
}
