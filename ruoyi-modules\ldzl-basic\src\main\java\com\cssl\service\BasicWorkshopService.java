package com.cssl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cssl.pojo.BasicWorkshop;

import java.util.List;

public interface BasicWorkshopService extends IService<BasicWorkshop> {
    //查看所有车间信息
    public List<BasicWorkshop> listBasicWorkshop(BasicWorkshop basicWorkshop);

    //添加车间信息
    public int addBasicWorkshop(BasicWorkshop basicWorkshop);

    //修改车间信息
    public int updateBasicWorkshop(BasicWorkshop basicWorkshop);

    //删除车间信息
    public int delBasicWorkshop(Long workshop_id);
}
