package com.ruoyi.system.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * 质量控制模板产品数据传输对象
 * 用于存储产品质量检测相关数据
 */
@Data
public class QcTemplateProductDto {

    private Long record_id;
    private Long template_id;
    private Long material_id;
    private String material_code;
    private String material_name;
    private String material_sfn;
    private Long product_id;
    private String product_code; // 产品编码
    private String product_name; // 产品名称
    private String product_sfn; // 规格型号
    private String unit;
    private int quantity_check_num;
    private int quantity_unqualified_num;
    private double cr_rate;
    private double maj_rate;
    private double min_rate;
    private String remark;
    private String attr1;
    private String attr2;
    private int attr3;
    private int attr4;
    private String create_by;
    private LocalDateTime create_time;
    private String update_by;
    private LocalDateTime update_time;
    private int is_delete;
}
