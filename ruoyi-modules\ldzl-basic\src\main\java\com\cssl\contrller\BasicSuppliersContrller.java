package com.cssl.contrller;

import com.cssl.pojo.BasicProduct;
import com.cssl.pojo.BasicSuppliers;
import com.cssl.pojo.BasicWlgl;
import com.cssl.service.BasicSuppliersService;
import com.github.pagehelper.PageHelper;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/sup")
public class BasicSuppliersContrller extends BaseController {
    @Resource
    private BasicSuppliersService basicSuppliersService;

    //查询所有供应商信息
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody BasicSuppliers basicSuppliers)
    {
        Map<String, Object> params=basicSuppliers.getParams();
        if(params!=null && params.get("pageNum") !=null && params.get("pageSize")!=null){
            Integer pageNum = Integer.parseInt(params.get("pageNum").toString());
            Integer pageSize = Integer.parseInt(params.get("pageSize").toString());
            PageHelper.startPage(pageNum, pageSize);
        }else {
            startPage();
        }

        //startPage();
        List<BasicSuppliers> list =basicSuppliersService.listBasicSuppliers(basicSuppliers);
        return getDataTable(list);
    }

    //添加供应商信息
    @Log(title = "添加供应商信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasicSuppliers basicSuppliers)
    {

        return toAjax(basicSuppliersService.addBasicSuppliers(basicSuppliers));
    }

    //修改供应商信息
    @Log(title = "修改供应商信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasicSuppliers basicSuppliers)
    {
        return toAjax(basicSuppliersService.updateBasicSuppliers(basicSuppliers));
    }

    //删除供应商信息
    @Log(title = "删除供应商信息", businessType = BusinessType.UPDATE)
    @PutMapping("/dp/{supplier_id}")
    public AjaxResult edit1(@PathVariable Long supplier_id)
    {
        return toAjax(basicSuppliersService.delBasicSuppliers(supplier_id));
    }

    //批量删除供应商
    @Log(title = "批量删除供应商", businessType = BusinessType.UPDATE)
    @PutMapping("/batch/{supplier_ids}")
    public AjaxResult edit(@PathVariable List<Long> supplier_ids)
    {
        return toAjax(basicSuppliersService.delBatchBasicSuppliers(supplier_ids));
    }
}
