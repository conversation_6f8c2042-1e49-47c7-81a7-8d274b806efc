package com.cssl.contrller;

import com.cssl.pojo.BasicNumbers;
import com.cssl.service.BasicNumbersService;
import java.util.List;
import java.io.IOException;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;


@RestController
@RequestMapping("/basic/numbers")
public class BasicNumbersContrller extends BaseController {
    @Resource
    private BasicNumbersService basicNumbersService;

    /**
     * 查询自动生成编号列表
     */
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody BasicNumbers basicNumbers)
    {
        startPage();
        List<BasicNumbers> list = basicNumbersService.selectBasicNumbersList(basicNumbers);
        return getDataTable(list);
    }

    /**
     * 导出自动生成编号列表
     */
    @Log(title = "自动生成编号", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasicNumbers basicNumbers)
    {
        List<BasicNumbers> list = basicNumbersService.selectBasicNumbersList(basicNumbers);
        ExcelUtil<BasicNumbers> util = new ExcelUtil<BasicNumbers>(BasicNumbers.class);
        util.exportExcel(response, list, "自动生成编号数据");
    }

    /**
     * 获取自动生成编号详细信息
     */
    @GetMapping(value = "/{enCode}")
    public AjaxResult getInfo(@PathVariable("enCode") Long enCode)
    {
        return success(basicNumbersService.selectBasicNumbersByEnCode(enCode));
    }

    /**
     * 新增自动生成编号
     */
    @Log(title = "自动生成编号", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasicNumbers basicNumbers)
    {
        return toAjax(basicNumbersService.insertBasicNumbers(basicNumbers));
    }

    /**
     * 修改自动生成编号
     */
    @Log(title = "自动生成编号", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasicNumbers basicNumbers)
    {
        return toAjax(basicNumbersService.updateBasicNumbers(basicNumbers));
    }

    /**
     * 删除自动生成编号
     */
    @Log(title = "自动生成编号", businessType = BusinessType.DELETE)
    @DeleteMapping("/{enCodes}")
    public AjaxResult remove(@PathVariable Long[] enCodes)
    {
        return toAjax(basicNumbersService.deleteBasicNumbersByEnCodes(enCodes));
    }

    @GetMapping("/auto/{encode}")
    @Log(title = "自动生成编号", businessType = BusinessType.OTHER)
    public AjaxResult automaticallyNumbers(@PathVariable Long encode) {
        String str = basicNumbersService.automaticallyNumbers(encode);
        if (str==null){
            return AjaxResult.error("编号生成超过当前刷新前的最大值");
        }
        return AjaxResult.success(str);
    }
}
