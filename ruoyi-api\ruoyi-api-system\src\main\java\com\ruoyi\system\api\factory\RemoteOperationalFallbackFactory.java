package com.ruoyi.system.api.factory;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.system.api.RemoteOperationalService;
import com.ruoyi.system.api.domain.BasicOperationalDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

@Component
public class RemoteOperationalFallbackFactory implements FallbackFactory<RemoteOperationalService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteOperationalFallbackFactory.class);

    @Override
    public RemoteOperationalService create(Throwable cause) {
        log.error("工艺路线服务调用失败:{}", cause.getMessage());
        return new RemoteOperationalService() {
            @Override
            public R<List<BasicOperationalDto>> getInfoByProductId(Long productId, String source) {
                return R.fail("获取工艺路线失败:" + cause.getMessage());
            }
        };
    }
} 