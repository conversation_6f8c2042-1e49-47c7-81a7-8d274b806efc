package com.ruoyi.common.core.domain;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 统一返回类
 */
public class UR {
    private UR(){}

    /**
     * 状态码，例如，200 表示成功，500 表示服务器错误。
     */
    private int code;

    /**
     * 提示信息
     */
    private String msg;

    /**
     * 表示请求是否成功。true 表示成功，false 表示失败。
     */
    private boolean flag;

    /**
     * 存储返回的数据
     */
    private Map<String,Object> data = new HashMap<>();

    private List<Object> dataList = new ArrayList<>();

    /**
     * 成功返回 带消息
     * @param msg
     * @return
     */
    public static UR ok(String msg){
        UR UR = new UR();
        UR.setCode(200);
        UR.setFlag(true);
        UR.setMsg(msg);
        return UR;
    }

    /**
     * 成功返回 带消息和数据
     * @param msg
     * @param data
     * @return
     */
    public static UR ok(String msg, Map<String,Object> data){
        UR UR = new UR();
        UR.setCode(200);
        UR.setFlag(true);
        UR.setMsg(msg);
        UR.setData(data);
        return UR;
    }

    /**
     * 成功返回 带消息和集合数据
     * @param msg
     * @param dataList
     * @return
     */
    public static UR ok(String msg, List<Object> dataList){
        UR UR = new UR();
        UR.setCode(200);
        UR.setFlag(true);
        UR.setMsg(msg);
        UR.setDataList(dataList);
        return UR;
    }

    public static UR fail(String msg){
        UR UR = new UR();
        UR.setCode(500);
        UR.setFlag(false);
        UR.setMsg(msg);
        return UR;
    }

    public UR msg(String msg){
        this.setMsg(msg);
        return this;
    }

    public UR code(Integer code){
        this.setCode(code);
        return this;
    }

    public UR data(String key, Object value){
        this.data.put(key,value);
        return this;
    }

    public UR data(Map<String,Object> map){
        this.setData(map);
        return this;
    }


    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public boolean isFlag() {
        return flag;
    }

    public void setFlag(boolean flag) {
        this.flag = flag;
    }

    public Map<String, Object> getData() {
        return data;
    }

    public void setData(Map<String, Object> data) {
        this.data = data;
    }

    public List<Object> getDataList() {
        return dataList;
    }

    public void setDataList(List<Object> dataList) {
        this.dataList = dataList;
    }
}
