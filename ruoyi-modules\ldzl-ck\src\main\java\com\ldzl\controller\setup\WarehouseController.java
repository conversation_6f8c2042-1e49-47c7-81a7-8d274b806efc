package com.ldzl.controller.setup;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ldzl.dto.FrozenStateDTO;
import com.ldzl.pojo.BasicUnits;
import com.ldzl.pojo.CkStorageArea;
import com.ldzl.pojo.CkStorageLocation;
import com.ldzl.pojo.CkWarehouse;
import com.ldzl.service.BasicUnitsService;
import com.ldzl.service.CkStorageAreaService;
import com.ldzl.service.CkStorageLocationService;
import com.ldzl.service.CkWarehouseService;
import com.ruoyi.common.core.domain.UR;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 仓库设置控制器
 */
@RestController
@RequestMapping("/setup")
public class WarehouseController extends BaseController {

    @Autowired
    private CkWarehouseService ws;     //仓库设置

    @Autowired
    private CkStorageLocationService sls; //库区设置

    @Autowired
    private CkStorageAreaService sas; //库位设置

    @Autowired
    private BasicUnitsService us; //单位设置

    /**
     * 查询所有仓库信息
     * @return
     */
    @PostMapping("/findAll_ws")
    public TableDataInfo findAll_ws(CkWarehouse  warehouse){
        startPage();
        return getDataTable(ws.findWarehouse(warehouse));
    }

    /**
     * 获取仓库编码
     * @param code
     * @return
     */
    @GetMapping("/getNumber/{code}")
    public AjaxResult getNumber_ws(@PathVariable("code") Long code){
        return ws.getNumber(code);
    }

    /**
     * 查询指定仓库下的所有库区
     * @return
     */
    @PostMapping("/findAll_sls")
    public TableDataInfo findAll_sls(CkStorageLocation location){
        System.out.println("测试获取的库区信息："+location);

        startPage();
        return getDataTable(sls.findLocation(location));
    }


    /**
     * 查询指定库区下的所有库位
     * @return
     */
    @PostMapping("/findAll_sas")
    public TableDataInfo findAll_sas(CkStorageArea area){
        startPage();
        return getDataTable(sas.findArea(area));
    }


    /**
     * 新增/修改 仓库
     * @param warehouse
     * @return
     */
    @PostMapping("/save_ws")
    public UR save_ws(CkWarehouse warehouse){
        System.out.println( "测试获取的数据："+warehouse);
        if(ws.saveWarehouse(warehouse))
    	    return UR.ok("新增/修改：成功");
        else
            return UR.fail("新增/修改：失败");
    }

    /**
     * 新增/修改 仓库
     * @param location
     * @return
     */
    @PostMapping("/save_sls")
    public UR save_sls(CkStorageLocation location){
        System.out.println( "测试获取的数据："+location);
        if(sls.saveLocation(location))
            return UR.ok("新增/修改：成功");
        else
            return UR.fail("新增/修改：失败");
    }

    /**
     * 新增/修改 库位
     * @param area
     * @return
     */
    @PostMapping("/save_sas")
    public UR save_sas(@RequestBody CkStorageArea area){
        System.out.println( "测试获取的数据库位："+area);
        //判断是否重复
        List<CkStorageArea> listArea2 = sas.list(new QueryWrapper<CkStorageArea>()
                .eq("area_name", area.getArea_name())
                .eq("location_id", area.getLocation_id())
                .eq("is_delete",0));
        if(listArea2 != null && listArea2.size() > 0){
            System.out.println("测试库位名称重复："+listArea2.size());
            return UR.fail("库位名称重复");
        }
        List<CkStorageArea> listArea3 = sas.list(new QueryWrapper<CkStorageArea>()
                .eq("position_x", area.getPosition_x())
                .eq("position_y", area.getPosition_z())
                .eq("position_z", area.getPosition_y())
                .eq("location_id", area.getLocation_id())
                .eq("is_delete",0));
        if(listArea3 != null && listArea3.size() > 0){
            System.out.println("测试库位位置重复："+listArea3.size());
            return UR.fail("位置重复");
        }
        //新增/修改
        if(sas.saveArea(area))
            return UR.ok("新增/修改：成功");
        else
            return UR.fail("新增/修改：失败");
    }

    /**
     * 查询所有单位
     * @return
     */
    @PostMapping("/findUnit")
    public UR findUnit(){
    	QueryWrapper<BasicUnits> queryWrapper = new QueryWrapper<>();
    	queryWrapper.eq("is_delete", "0");
    	List<BasicUnits> units = us.list(queryWrapper);
    	return UR.ok("查询成功", (List)units);
    }


    /**
     * 修改仓库冻结状态
     * @return
     */
    @PostMapping("/updateFrozen")
    public UR updateFrozen(FrozenStateDTO fst){
        if(ws.updateFrozen(fst))
            return UR.ok("修改成功");
        else
            return UR.fail("修改失败");
    }

    /**
     * 查询仓库/库区/库位
     * @return
     */
    @PostMapping("/selectWarehouse_w_l_a")
    public UR selectWarehouse_w_l_a(){
    	List<CkWarehouse> warehouse = ws.selectWarehouse_w_l_a();
    	return UR.ok("查询成功",(List)warehouse);
    }
}
