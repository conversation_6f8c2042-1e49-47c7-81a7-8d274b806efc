<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cssl.mapper.BasicBomMapper">
    <resultMap id="selectBasicBom" type="com.cssl.pojo.BasicBom">
        <id column="bom_id" property="bom_id"/>
        <association property="basicProduct" javaType="com.cssl.pojo.BasicProduct">
            <id column="product_id" property="product_id"/>
        </association>
        <collection property="basicBomdetails" ofType="com.cssl.pojo.BasicBomdetails">
            <id column="bomdetails_id" property="bomdetails_id"/>
        </collection>

    </resultMap>

    <insert id="addBasicBom" parameterType="com.cssl.pojo.BasicBom" useGeneratedKeys="true" keyProperty="bom_id">
        INSERT into basic_bom
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bom_code!= null and bom_code!=''">bom_code,</if>
            <if test="bom_status != null and bom_status != ''">bom_status,</if>
            <if test="product_id != null and product_id !='' ">product_id,</if>
            <if test="bom_version != null and bom_version != ''" >bom_version,</if>
            <if test="bom_output != null ">bom_output,</if>
            <if test="remarks != null and remarks !=''">remarks,</if>
            <if test="is_delete != null ">is_delete,</if>
            <if test="create_by != null and create_by !='' ">create_by,</if>
            <if test="create_time!= null ">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bom_code!= null and bom_code!=''">#{bom_code},</if>
            <if test="bom_status != null and bom_status != ''">#{bom_status},</if>
            <if test="product_id != null and product_id !='' ">#{product_id},</if>
            <if test="bom_version != null and bom_version != ''" >#{bom_version},</if>
            <if test="bom_output != null ">#{bom_output},</if>
            <if test="remarks != null and remarks !=''">#{remarks},</if>
            <if test="is_delete != null ">#{is_delete},</if>
            <if test="create_by != null and create_by !='' ">#{create_by},</if>
            <if test="create_time!= null ">#{create_time},</if>
        </trim>
    </insert>

    <update id="updateBasicBom">
        update basic_bom
        <trim prefix="SET" suffixOverrides=",">
            <if test="bom_status != null and bom_status != ''">bom_status=#{bom_status},</if>
            <if test="bom_version != null and bom_version !='' ">bom_version=#{bom_version},</if>
            <if test="bom_output != null and bom_output != ''" >bom_output=#{bom_output},</if>
            <if test="product_id != null and product_id !='' ">product_id=#{product_id},</if>
            <if test="remarks != null and remarks !=''">remarks=#{remarks},</if>
            <if test="update_by != null and update_by !='' ">update_by=#{update_by},</if>
            <if test="update_time!= null ">update_time=#{update_time},</if>
        </trim>
        where bom_id = #{bom_id}
    </update>

    <update id="deleteBasicBom">
        update basic_bom
        <trim prefix="SET" suffixOverrides=",">
            <if test="is_delete != null ">is_delete =1,</if>
        </trim>
        where bom_id = #{bom_id}
    </update>
    <update id="deleteBasicBomByIds">
        update basic_bom set is_delete = '1' where bom_id in
        <foreach item="bom_ids" collection="list" open="(" separator="," close=")">
            #{bom_ids}
        </foreach>
    </update>

    <select id="listBasicBom" resultType="com.cssl.pojo.BasicBom">
        SELECT * from basic_bom bb inner join basic_products bp on bb.product_id=bp.product_id inner join basic_bomdetails bm on bm.bom_id=bb.bom_id WHERE bb.is_delete=0 and bp.is_delete=0 and bm.is_detele=0
        <if test="bom_code !=null and bom_code !=''">and bb.bom_code like CONCAT('%',#{bom_code},'%')</if>
        <if test="product_name !=null and product_name !=''">and bp.product_name like CONCAT('%',#{product_name},'%')</if>
        <if test="bom_version !=null and bom_version !=''">and bb.bom_version like CONCAT('%',#{bom_version},'%')</if>
        <if test="product_id !=null and product_id !=''">and bb.product_id=#{product_id}</if>
    </select>
    <select id="getBasicBom" resultType="com.cssl.pojo.BasicBom">
        SELECT * from basic_bom bb inner join basic_products bp on bb.product_id=bp.product_id WHERE bb.is_delete=0 and bp.is_delete=0
        <if test="bom_id !=null and bom_id !=''">and bb.bom_id=#{bom_id}</if>
    </select>
    <select id="getBomIdByProductId" resultType="com.cssl.pojo.BasicBom">
        SELECT bb.bom_id from basic_bom bb inner join basic_bomdetails bm on bb.bom_id=bm.bom_id WHERE bb.product_id in
        <foreach item="product_ids" collection="list" open="(" separator="," close=")">
        #{product_ids}
        </foreach>
        and bb.is_delete=0 and bm.is_detele=0
    </select>
</mapper>