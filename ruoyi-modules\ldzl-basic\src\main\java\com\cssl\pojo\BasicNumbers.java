package com.cssl.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("basic_numbers")
public class BasicNumbers extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 编号归职 */
    @Excel(name = "编号归职")
    @TableId(value ="enCode",type = IdType.AUTO)
    private Long enCode;

    /** 目标表单 */
    @Excel(name = "目标表单")
    private String enForm;

    /** 编号前缀 */
    @Excel(name = "编号前缀")
    private String enPrefix;

    /** 时间规则 */
    @Excel(name = "时间规则")
    private String enTime;

    /** 流水号/位 */
    @Excel(name = "流水号/位")
    private Long enNum;

    /** 步长 */
    @Excel(name = "步长")
    private Long enStep;

    /** 按时间刷新（1.天,2.月3.年） */
    @Excel(name = "按时间刷新", readConverterExp = "1=.天,2.月3.年")
    private Long enFlushed;

    /** 编号生成规则 */
    @Excel(name = "编号生成规则")
    private String enRules;

    /** 逻辑删除 */
    private Long isDel;

    /** 最大值 */
    @Excel(name = "最大值")
    private Long maxNum;

    /** 最后一次更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "最后一次更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date lastDate;
}
