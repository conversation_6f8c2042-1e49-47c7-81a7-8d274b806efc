package com.cssl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cssl.pojo.BasicCustomers;
import com.cssl.pojo.BasicProduct;

import java.util.List;

public interface BasicCustomersService extends IService<BasicCustomers> {
    //查询所有客户信息
    public List<BasicCustomers> listBasicCustomers(BasicCustomers basicCustomers);

    //添加客户信息
    public int addBasicCustomers(BasicCustomers basicCustomers);
    //修改客户信息
    public int updateCustomers(BasicCustomers basicCustomers);

    //删除客户信息
    public int delCustomers(Long customer_id);

    //批量删除
    public int delBatchCustomers(List<Long> customer_ids);

}
