{"remainingRequest": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\src\\views\\sc\\plan\\add_plan.vue?vue&type=style&index=0&id=41fe9070&scoped=true&lang=css", "dependencies": [{"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\src\\views\\sc\\plan\\add_plan.vue", "mtime": 1753787541839}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751945357942}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751945367667}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751945361393}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751945356000}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751945364156}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouZm9ybS1jb250YWluZXIgewogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7CiAgcGFkZGluZzogMTBweDsKfQoKLmVsLXRhYnMtLWJvcmRlci1jYXJkIHsKICBib3gtc2hhZG93OiBub25lOwp9CgoudXBsb2FkLWNvbnRhaW5lciB7CiAgd2lkdGg6IDEwMCU7CiAgYm9yZGVyOiAxcHggZGFzaGVkICNkOWQ5ZDk7CiAgYm9yZGVyLXJhZGl1czogNnB4OwogIHRleHQtYWxpZ246IGNlbnRlcjsKICBwYWRkaW5nOiAyMHB4IDA7CiAgY3Vyc29yOiBwb2ludGVyOwp9CgoudXBsb2FkLWNvbnRhaW5lcjpob3ZlciB7CiAgYm9yZGVyLWNvbG9yOiAjNDA5RUZGOwp9CgoudXBsb2FkLWFyZWEgewogIGRpc3BsYXk6IGZsZXg7CiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGp1c3RpZnktY29udGVudDogY2VudGVyOwp9CgoudXBsb2FkLWFyZWEgLmVsLWljb24tdXBsb2FkIHsKICBmb250LXNpemU6IDQ4cHg7CiAgY29sb3I6ICNjMGM0Y2M7CiAgbWFyZ2luLWJvdHRvbTogMTBweDsKfQoKLnVwbG9hZC10ZXh0IHsKICBmb250LXNpemU6IDE0cHg7CiAgY29sb3I6ICM2MDYyNjY7CiAgbWFyZ2luLWJvdHRvbTogMTBweDsKfQoKLnVwbG9hZC1oaW50IHsKICBmb250LXNpemU6IDEycHg7CiAgY29sb3I6ICM5MDkzOTk7Cn0KCi5pbnB1dC13aXRoLXNlbGVjdCAuZWwtaW5wdXQtZ3JvdXBfX2FwcGVuZCB7CiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsKfQoKLmJvbS10aXRsZSB7CiAgZm9udC1zaXplOiAxNnB4OwogIGZvbnQtd2VpZ2h0OiBib2xkOwogIGNvbG9yOiAjMzAzMTMzOwp9CgouYm9tLWNvbnRhaW5lciB7CiAgZGlzcGxheTogZmxleDsKICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgbWFyZ2luOiAyMHB4IDA7CiAgcGFkZGluZzogMzBweCAwOwp9CgouZm9sZGVyLWljb24gewogIG1hcmdpbi1ib3R0b206IDIwcHg7Cn0KCi5ib20tdGV4dCB7CiAgZm9udC1zaXplOiAxNHB4OwogIGNvbG9yOiAjOTA5Mzk5OwogIG1hcmdpbi1ib3R0b206IDIwcHg7Cn0KCi53YXJuaW5nLXRleHQgewogIGNvbG9yOiAjRTZBMjNDOwogIGZvbnQtc2l6ZTogMTJweDsKICBtYXJnaW4tbGVmdDogMTBweDsKfQoKLndhcm5pbmctdGV4dCBpIHsKICBtYXJnaW4tcmlnaHQ6IDVweDsKfQoKLnVwbG9hZC1oaWRkZW4gewogIHdpZHRoOiAxMDAlOwogIGhlaWdodDogMTAwJTsKfQoKLnVwbG9hZC1oaWRkZW4gPj4+IC5lbC11cGxvYWQgewogIHdpZHRoOiAxMDAlOwp9CgoudXBsb2FkLWhpZGRlbiA+Pj4gLmVsLXVwbG9hZC1kcmFnZ2VyIHsKICB3aWR0aDogMTAwJTsKICBoZWlnaHQ6IDEwMCU7CiAgYm9yZGVyOiBub25lOwogIHBhZGRpbmc6IDA7CiAgbWFyZ2luOiAwOwp9CgouYm9tLWRpYWxvZy1oZWFkZXIgewogIG1hcmdpbi1ib3R0b206IDE1cHg7CiAgcGFkZGluZzogMTBweDsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmN2ZhOwogIGJvcmRlci1yYWRpdXM6IDRweDsKfQoKLnByb2R1Y3QtaW5mbyB7CiAgZGlzcGxheTogZmxleDsKICBmbGV4LXdyYXA6IHdyYXA7Cn0KCi5wcm9kdWN0LWluZm8gc3BhbiB7CiAgbWFyZ2luLXJpZ2h0OiAyMHB4OwogIGxpbmUtaGVpZ2h0OiAzMHB4Owp9CgouZWwtcmFkaW8gewogIG1hcmdpbi1yaWdodDogMDsKfQoKLnBhZ2luYXRpb24tY29udGFpbmVyIHsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIHBhZGRpbmc6IDEwcHggMDsKICBmb250LXNpemU6IDEycHg7Cn0KCi5wYWdpbmF0aW9uLXdyYXBwZXIgewogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKfQoKLnBhZ2Utc2l6ZSB7CiAgbWFyZ2luLXJpZ2h0OiAxMHB4Owp9CgoudG90YWwtdGV4dCB7CiAgY29sb3I6ICM2MDYyNjY7CiAgZm9udC1zaXplOiAxMnB4Owp9CgouYm9tLWluZm8gewogIHdpZHRoOiAxMDAlOwogIG1hcmdpbi1ib3R0b206IDIwcHg7Cn0KCi5ib20taGVhZGVyIHsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIG1hcmdpbi1ib3R0b206IDEwcHg7CiAgcGFkZGluZzogOHB4OwogIGJhY2tncm91bmQtY29sb3I6ICNmNWY3ZmE7CiAgYm9yZGVyLXJhZGl1czogNHB4Owp9CgouYm9tLXRpdGxlLWluZm8gewogIGRpc3BsYXk6IGZsZXg7CiAgZmxleC13cmFwOiB3cmFwOwp9CgouYm9tLXRpdGxlLWluZm8gc3BhbiB7CiAgbWFyZ2luLXJpZ2h0OiAyMHB4OwogIGZvbnQtd2VpZ2h0OiBib2xkOwp9CgouYm9tLWRldGFpbC10YWJsZSB7CiAgbWFyZ2luLWJvdHRvbTogMTVweDsKfQoKLmJvbS1hY3Rpb24gewogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKfQoKLnNlbGVjdC1ib20tYnV0dG9uIHsKICBtYXJnaW4tcmlnaHQ6IDEwcHg7Cn0K"}, {"version": 3, "sources": ["add_plan.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+kCA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "add_plan.vue", "sourceRoot": "src/views/sc/plan", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <div class=\"form-container\">\n      <!-- 基础信息区 -->\n      <el-tabs type=\"border-card\">\n        <el-tab-pane>\n          <span slot=\"label\"><i class=\"el-icon-date\"></i> 基础信息</span>\n          <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\n            <el-row :gutter=\"20\">\n              <el-col :span=\"8\">\n                <el-form-item label=\"计划编号\" prop=\"planCode\" required>\n                  <el-input v-model=\"form.planCode\" placeholder=\"请输入\" :disabled=\"isSystemCode\"></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"4\">\n                <el-switch\n                  v-model=\"isSystemCode\"\n                  active-text=\"系统编号\"\n                  inactive-text=\"\"\n                  style=\"margin-top: 13px;\"\n                  @change=\"handleSystemCodeChange\"\n                ></el-switch>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"计划名称\" prop=\"planName\" required>\n                  <el-input v-model=\"form.planName\" placeholder=\"请输入\"></el-input>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-row :gutter=\"20\">\n              <el-col :span=\"12\">\n                <el-form-item label=\"来源类型\" prop=\"sourceType\" required>\n                  <el-select v-model=\"form.sourceType\" placeholder=\"生产订单\" style=\"width: 100%\" @change=\"handleSourceTypeChange\">\n                    <el-option\n                      v-for=\"item in sourceTypeOptions\"\n                      :key=\"item.dictValue\"\n                      :label=\"item.dictLabel\"\n                      :value=\"item.dictValue\"\n                    ></el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"订单编号\" prop=\"orderCode\">\n                  <el-input v-model=\"form.orderCode\" placeholder=\"请输入\">\n                    <el-button\n                      v-if=\"form.sourceType === 'PRODUCTION_ORDER'\"\n                      slot=\"append\"\n                      icon=\"el-icon-search\"\n                      @click=\"openProductionOrderDialog\">\n                      选择\n                    </el-button>\n                  </el-input>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-row :gutter=\"20\">\n              <el-col :span=\"12\">\n                <el-form-item label=\"成品名称\" prop=\"productName\">\n                  <el-input\n                    placeholder=\"请选择成品\"\n                    v-model=\"form.productName\"\n                    class=\"input-with-select\"\n                  >\n                    <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"openProductSelection\"></el-button>\n                  </el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"成品编号\" prop=\"productCode\">\n                  <el-input v-model=\"form.productCode\" placeholder=\"请输入\" disabled></el-input>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-row :gutter=\"20\">\n              <el-col :span=\"12\">\n                <el-form-item label=\"规格型号\" prop=\"specification\">\n                  <el-input v-model=\"form.specification\" placeholder=\"请输入\" disabled></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"单位\" prop=\"unit\">\n                  <el-input v-model=\"form.unit\" placeholder=\"请输入\" disabled></el-input>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-row :gutter=\"20\">\n              <el-col :span=\"12\">\n                <el-form-item label=\"计划数量\" prop=\"plannedQty\" required>\n                  <el-input-number v-model=\"form.plannedQty\" :min=\"1\" controls-position=\"right\" style=\"width: 100%\"></el-input-number>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-row :gutter=\"20\">\n              <el-col :span=\"12\">\n                <el-form-item label=\"开工时间\" prop=\"planStartTime\">\n                  <el-date-picker\n                    v-model=\"form.planStartTime\"\n                    type=\"date\"\n                    placeholder=\"请选择日期\"\n                    style=\"width: 100%\"\n                    value-format=\"yyyy-MM-dd\"\n                  ></el-date-picker>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"完工时间\" prop=\"planEndTime\">\n                  <el-date-picker\n                    v-model=\"form.planEndTime\"\n                    type=\"date\"\n                    placeholder=\"请选择日期\"\n                    style=\"width: 100%\"\n                    value-format=\"yyyy-MM-dd\"\n                  ></el-date-picker>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-row :gutter=\"20\">\n              <el-col :span=\"12\">\n                <el-form-item label=\"需求日期\" prop=\"requiredDate\">\n                  <el-date-picker\n                    v-model=\"form.requiredDate\"\n                    type=\"date\"\n                    placeholder=\"请选择日期\"\n                    style=\"width: 100%\"\n                    value-format=\"yyyy-MM-dd\"\n                  ></el-date-picker>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-row :gutter=\"20\">\n              <el-col :span=\"24\">\n                <el-form-item label=\"备注\" prop=\"remark\">\n                  <el-input\n                    type=\"textarea\"\n                    v-model=\"form.remark\"\n                    placeholder=\"请输入\"\n                    :rows=\"4\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-row :gutter=\"20\">\n              <el-col :span=\"24\">\n                <el-form-item label=\"附件\" prop=\"attachment\">\n                  <div class=\"upload-container\" @click=\"triggerUpload\">\n                    <el-upload\n                      ref=\"upload\"\n                      class=\"upload-hidden\"\n                      action=\"#\"\n                      :http-request=\"uploadFile\"\n                      :file-list=\"fileList\"\n                      :before-upload=\"beforeUpload\"\n                      :on-remove=\"handleRemove\"\n                      multiple\n                      drag\n                    >\n                      <div class=\"upload-area\">\n                        <i class=\"el-icon-upload\"></i>\n                        <div class=\"upload-text\">点击或者拖动文件到虚线框内上传</div>\n                        <div class=\"upload-hint\">支持 docx, xls, PDF, rar, zip, PNG, JPG 等类型的文件</div>\n                      </div>\n                    </el-upload>\n                  </div>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-divider>\n              <span class=\"bom-title\">BOM组成</span>\n            </el-divider>\n            \n            <el-row>\n              <el-col :span=\"24\">\n                <div class=\"bom-container\">\n                  <div class=\"folder-icon\" v-if=\"!selectedBom\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"60\" height=\"60\" viewBox=\"0 0 24 24\" fill=\"#DCDFE6\">\n                      <path d=\"M10 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2h-8l-2-2z\"/>\n                    </svg>\n                  </div>\n                  <div class=\"bom-text\" v-if=\"!selectedBom\">暂无数据</div>\n                  <div class=\"bom-info\" v-else>\n                    <div class=\"bom-header\">\n                      <div class=\"bom-title-info\">\n                        <span>BOM编号：{{ selectedBom.bom_code }}</span>\n                        <span>版本号：{{ selectedBom.bom_version }}</span>\n                      </div>\n                      <el-button type=\"text\" icon=\"el-icon-delete\" @click=\"clearSelectedBom\">清除</el-button>\n                    </div>\n                    <el-table\n                      :data=\"bomDetailList\"\n                      border\n                      size=\"small\"\n                      style=\"width: 100%\"\n                      class=\"bom-detail-table\"\n                    >\n                      <el-table-column label=\"序号\" type=\"index\" width=\"50\" align=\"center\"></el-table-column>\n                      <el-table-column prop=\"material_code\" label=\"物料编码\" min-width=\"120\" align=\"center\"></el-table-column>\n                      <el-table-column prop=\"material_name\" label=\"物料名称\" min-width=\"150\" align=\"center\"></el-table-column>\n                      <el-table-column prop=\"specification\" label=\"规格型号\" min-width=\"100\" align=\"center\"></el-table-column>\n                      <el-table-column prop=\"unit\" label=\"单位\" width=\"60\" align=\"center\"></el-table-column>\n                      <el-table-column prop=\"quantity\" label=\"用量\" width=\"80\" align=\"center\"></el-table-column>\n                      <el-table-column prop=\"remark\" label=\"备注\" min-width=\"120\" align=\"center\"></el-table-column>\n                    </el-table>\n                  </div>\n                  <div class=\"bom-action\">\n                    <el-tooltip :disabled=\"form.productId\" content=\"请先选择成品!\" placement=\"right\" effect=\"light\">\n                      <el-button type=\"primary\" class=\"select-bom-button\" @click=\"selectBom\">选择BOM</el-button>\n                    </el-tooltip>\n                  </div>\n                </div>\n              </el-col>\n            </el-row>\n            \n            <el-row>\n              <el-col :span=\"24\" style=\"text-align: center; margin-top: 20px;\">\n                <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n                <el-button @click=\"cancel\">取 消</el-button>\n              </el-col>\n            </el-row>\n          </el-form>\n        </el-tab-pane>\n      </el-tabs>\n    </div>\n    \n    <!-- 产品选择对话框 -->\n    <el-dialog title=\"选择成品\" :visible.sync=\"productDialogVisible\" width=\"40%\" append-to-body>\n      <el-form :model=\"productQuery\" ref=\"productQueryForm\" :inline=\"true\" class=\"demo-form-inline\" size=\"small\">\n        <el-form-item>\n          <el-input v-model=\"productQuery.keyword\" placeholder=\"请输入产品编号/名称\" clearable style=\"width: 180px;\"></el-input>\n        </el-form-item>\n        <el-form-item>\n          <el-select v-model=\"productQuery.unit\" placeholder=\"请选择单位\" clearable style=\"width: 120px;\">\n            <el-option label=\"个\" value=\"个\"></el-option>\n            <el-option label=\"件\" value=\"件\"></el-option>\n            <el-option label=\"台\" value=\"台\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item>\n          <el-select v-model=\"productQuery.type\" placeholder=\"请选择类型\" clearable style=\"width: 120px;\">\n            <el-option label=\"成品\" value=\"成品\"></el-option>\n            <el-option label=\"半成品\" value=\"半成品\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item>\n          <el-select v-model=\"productQuery.property\" placeholder=\"请选择产品属性\" clearable style=\"width: 120px;\">\n            <el-option label=\"自制\" value=\"自制\"></el-option>\n            <el-option label=\"外购\" value=\"外购\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"small\" @click=\"searchProducts\">查询</el-button>\n          <el-button icon=\"el-icon-refresh\" size=\"small\" @click=\"resetProductQuery\">重置</el-button>\n        </el-form-item>\n      </el-form>\n      \n      <el-table\n        v-loading=\"productLoading\"\n        :data=\"productList\"\n        border\n        size=\"small\"\n        style=\"width: 100%\"\n        @selection-change=\"handleProductSelectionChange\"\n        height=\"300\"\n      >\n        <el-table-column type=\"selection\" width=\"40\" align=\"center\"></el-table-column>\n        <el-table-column label=\"序号\" type=\"index\" width=\"40\" align=\"center\"></el-table-column>\n        <el-table-column prop=\"product_code\" label=\"产品编号\" width=\"90\" align=\"center\"></el-table-column>\n        <el-table-column prop=\"product_name\" label=\"产品名称\" min-width=\"120\" align=\"center\"></el-table-column>\n        <el-table-column prop=\"product_sfn\" label=\"规格型号\" min-width=\"90\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <span :style=\"{color: '#1890ff'}\">{{ scope.row.product_sfn }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"product_unit\" label=\"单位\" width=\"60\" align=\"center\"></el-table-column>\n        <!-- 生产订单产品特有的列 -->\n        <el-table-column v-if=\"form.sourceType === 'PRODUCTION_ORDER'\" prop=\"order_qty\" label=\"订单数量\" width=\"80\" align=\"center\"></el-table-column>\n        <el-table-column v-if=\"form.sourceType === 'PRODUCTION_ORDER'\" prop=\"delivery_date\" label=\"交付日期\" width=\"100\" align=\"center\">\n          <template slot-scope=\"scope\">\n            {{ scope.row.delivery_date ? scope.row.delivery_date.substring(0, 10) : '' }}\n          </template>\n        </el-table-column>\n      </el-table>\n      \n      <div class=\"pagination-container\">\n        <span class=\"total-text\">共 {{ productTotal }} 条</span>\n        <div class=\"pagination-wrapper\">\n          <span class=\"page-size\">\n            <el-select v-model=\"productQuery.pageSize\" size=\"mini\" @change=\"handleProductSizeChange\" style=\"width: 80px;\">\n              <el-option\n                v-for=\"item in [10, 20, 30, 50]\"\n                :key=\"item\"\n                :label=\"`${item}条/页`\"\n                :value=\"item\">\n              </el-option>\n            </el-select>\n          </span>\n          <el-pagination\n            small\n            background\n            @current-change=\"handleProductCurrentChange\"\n            :current-page=\"productQuery.pageNum\"\n            :page-size=\"productQuery.pageSize\"\n            layout=\"prev, pager, next, jumper\"\n            :pager-count=\"5\"\n            :total=\"productTotal\">\n          </el-pagination>\n        </div>\n      </div>\n      \n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button size=\"small\" @click=\"productDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" size=\"small\" @click=\"confirmProductSelect\">确定</el-button>\n      </div>\n    </el-dialog>\n    \n    <!-- BOM选择对话框 -->\n    <el-dialog title=\"选择BOM\" :visible.sync=\"bomDialogVisible\" width=\"40%\" append-to-body>\n      <div class=\"bom-dialog-header\">\n        <div class=\"product-info\">\n          <span>产品名称：{{ form.productName }}</span>\n          <span>产品编号：{{ form.productCode }}</span>\n          <span>规格型号：{{ form.specification }}</span>\n          <span>单位：{{ form.unit }}</span>\n        </div>\n      </div>\n      \n      <el-table\n        v-loading=\"bomLoading\"\n        :data=\"bomList\"\n        border\n        style=\"width: 100%\"\n        @row-click=\"handleBomSelect\"\n        highlight-current-row\n        size=\"small\"\n        height=\"300\"\n      >\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <el-radio v-model=\"selectedBomId\" :label=\"scope.row.bom_id\" @change=\"handleBomSelect(scope.row)\">&nbsp;</el-radio>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"序号\" type=\"index\" width=\"55\" align=\"center\"></el-table-column>\n        <el-table-column prop=\"bom_code\" label=\"BOM编号\" min-width=\"150\" align=\"center\"></el-table-column>\n        <el-table-column prop=\"bom_version\" label=\"版本号\" width=\"100\" align=\"center\"></el-table-column>\n        <el-table-column prop=\"bom_status\" label=\"默认BOM\" width=\"100\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <span>{{ scope.row.bom_status === '1' ? '是' : '否' }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"bom_output\" label=\"日产量\" width=\"100\" align=\"center\"></el-table-column>\n      </el-table>\n      \n      <pagination\n        v-show=\"bomTotal > 0\"\n        :total=\"bomTotal\"\n        :page.sync=\"bomQuery.pageNum\"\n        :limit.sync=\"bomQuery.pageSize\"\n        @pagination=\"getBomList\"\n      />\n      \n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"bomDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"confirmBomSelect\">确定</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 生产订单选择对话框 -->\n    <el-dialog title=\"选择生产订单\" :visible.sync=\"productionOrderDialogVisible\" width=\"800px\" append-to-body>\n      <el-table\n        :data=\"productionOrderList\"\n        @selection-change=\"handleProductionOrderSelectionChange\"\n        style=\"width: 100%\">\n        <el-table-column type=\"selection\" width=\"55\"></el-table-column>\n        <el-table-column prop=\"prodOrderCode\" label=\"订单编号\" width=\"150\"></el-table-column>\n        <el-table-column prop=\"customerName\" label=\"客户名称\" width=\"150\"></el-table-column>\n        <el-table-column prop=\"deliveryDate\" label=\"交付日期\" width=\"120\">\n          <template slot-scope=\"scope\">\n            {{ scope.row.deliveryDate ? scope.row.deliveryDate.substring(0, 10) : '' }}\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"status\" label=\"状态\" width=\"80\">\n          <template slot-scope=\"scope\">\n            <el-tag v-if=\"scope.row.status === '1'\" type=\"warning\">待计划</el-tag>\n            <el-tag v-else-if=\"scope.row.status === '2'\" type=\"primary\">已计划</el-tag>\n            <el-tag v-else-if=\"scope.row.status === '3'\" type=\"success\">生产中</el-tag>\n            <el-tag v-else-if=\"scope.row.status === '4'\" type=\"info\">已完成</el-tag>\n            <el-tag v-else type=\"info\">{{ scope.row.status }}</el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"remark\" label=\"备注\" show-overflow-tooltip></el-table-column>\n      </el-table>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"productionOrderDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"confirmProductionOrderSelect\">确定</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { addProductionPlan } from \"@/api/sc/productionPlan\";\nimport { listProducts, listBomsByProductId, findBomDetails } from \"@/api/basic/product\";\nimport { getAutoNumbers } from \"@/api/basic/numbers\";\nimport Pagination from \"@/components/Pagination\";\n\nexport default {\n  name: \"AddPlan\",\n  components: {\n    Pagination\n  },\n  data() {\n    return {\n      // 表单数据\n      form: {\n        planCode: \"\",\n        planName: \"\",\n        sourceType: \"PRODUCTION_ORDER\",\n        orderCode: \"\",\n        productId: undefined,\n        productName: \"\",\n        productCode: \"\",\n        specification: \"\",\n        unit: \"\",\n        plannedQty: 1,\n        planStartTime: \"\",\n        planEndTime: \"\",\n        requiredDate: \"\",\n        remark: \"\",\n        orderQty: 0  // 添加订单数量字段\n      },\n      // 是否使用系统编号\n      isSystemCode: true,\n      // 表单验证规则\n      rules: {\n        planName: [\n          { required: true, message: \"计划名称不能为空\", trigger: \"blur\" },\n          { max: 50, message: \"长度不能超过50个字符\", trigger: \"blur\" }\n        ],\n        sourceType: [\n          { required: true, message: \"来源类型不能为空\", trigger: \"change\" }\n        ],\n        productName: [\n          { required: true, message: \"请选择产品\", trigger: \"change\" }\n        ],\n        plannedQty: [\n          { required: true, message: \"计划数量不能为空\", trigger: \"blur\" },\n          { validator: this.validatePlannedQty, trigger: \"blur\" }\n        ],\n        planStartTime: [\n          { validator: this.validateStartTime, trigger: \"change\" }\n        ],\n        planEndTime: [\n          { validator: this.validateEndTime, trigger: \"change\" }\n        ],\n        requiredDate: [\n          { validator: this.validateRequiredDate, trigger: \"change\" }\n        ]\n      },\n      // 产品下拉选项\n      productOptions: [],\n      // 产品加载状态\n      productLoading: false,\n      // 来源类型选项\n      sourceTypeOptions: [\n        { dictLabel: \"生产订单\", dictValue: \"PRODUCTION_ORDER\" }\n      ],\n      // 上传文件列表\n      fileList: [],\n      \n      // 产品选择对话框\n      productDialogVisible: false,\n      productQuery: {\n        pageNum: 1,\n        pageSize: 10,\n        keyword: \"\",\n        unit: \"\",\n        type: \"\",\n        property: \"\"\n      },\n      productList: [],\n      productTotal: 0,\n\n      // 生产订单选择对话框\n      productionOrderDialogVisible: false,\n      productionOrderList: [],\n      selectedProductionOrder: null,\n      selectedProduct: null,\n      \n      // BOM选择对话框\n      bomDialogVisible: false,\n      bomQuery: {\n        pageNum: 1,\n        pageSize: 10,\n        productId: undefined\n      },\n      bomList: [],\n      bomTotal: 0,\n      bomLoading: false,\n      selectedBom: null,\n      selectedBomId: null,\n      bomDetailList: [],\n    };\n  },\n  created() {\n    // 初始化时如果是系统编号，则生成计划编号\n    if (this.isSystemCode) {\n      this.generatePlanCode();\n    }\n  },\n  methods: {\n    // 生成计划编号\n    generatePlanCode() {\n      getAutoNumbers(6).then(response => {\n        if (response.code === 200) {\n          this.form.planCode = response.msg;\n        } else {\n          this.$message.error('获取计划编号失败');\n        }\n      }).catch(() => {\n        this.$message.error('获取计划编号失败');\n      });\n    },\n    \n    // 处理系统编号开关变化\n    handleSystemCodeChange(val) {\n      if (val) {\n        // 如果开启系统编号，则生成编号\n        this.generatePlanCode();\n      } else {\n        // 如果关闭系统编号，则清空编号\n        this.form.planCode = '';\n      }\n    },\n\n    // 处理来源类型变化\n    handleSourceTypeChange(val) {\n      // 清空订单编号\n      this.form.orderCode = \"\";\n      // 如果选择生产订单，清空产品相关信息\n      if (val === 'PRODUCTION_ORDER') {\n        this.form.productId = undefined;\n        this.form.productName = \"\";\n        this.form.productCode = \"\";\n        this.form.specification = \"\";\n        this.form.unit = \"\";\n      }\n    },\n\n    // 打开生产订单选择对话框\n    openProductionOrderDialog() {\n      this.productionOrderDialogVisible = true;\n      this.getProductionOrderList();\n    },\n\n    // 获取生产订单列表\n    getProductionOrderList() {\n      // 这里调用生产订单列表API\n      import(\"@/api/sc/productionOrder\").then(api => {\n        api.listProductionOrder({}).then(response => {\n          this.productionOrderList = response.rows || [];\n        }).catch(() => {\n          this.$message.error('获取生产订单列表失败');\n        });\n      });\n    },\n\n    // 处理生产订单选择变化\n    handleProductionOrderSelectionChange(selection) {\n      this.selectedProductionOrder = selection.length > 0 ? selection[0] : null;\n    },\n\n    // 确认选择生产订单\n    confirmProductionOrderSelect() {\n      if (!this.selectedProductionOrder) {\n        this.$message.warning('请选择一个生产订单');\n        return;\n      }\n\n      // 设置订单编号\n      this.form.orderCode = this.selectedProductionOrder.prodOrderCode;\n\n      // 调用API根据生产订单创建计划模板\n      import(\"@/api/sc/productionPlan\").then(api => {\n        api.createPlanFromOrder(this.selectedProductionOrder.productionOrderId).then(response => {\n          if (response.code === 200) {\n            const planData = response.data;\n            // 填充表单数据\n            this.form.planName = planData.planName;\n            this.form.productId = planData.productId;\n            this.form.plannedQty = planData.plannedQty;\n            this.form.orderQty = planData.plannedQty; // 保存订单数量用于验证\n            this.form.planStartTime = planData.planStartTime;\n            this.form.planEndTime = planData.planEndTime;\n            this.form.requiredDate = planData.requiredDate;\n            this.form.remark = planData.remark;\n\n            // 如果有产品信息，需要获取产品详情\n            if (planData.productId) {\n              this.getProductDetails(planData.productId);\n            }\n\n            this.$message.success('已关联生产订单，请完善其他信息');\n          }\n        }).catch(() => {\n          this.$message.error('关联生产订单失败');\n        });\n      });\n\n      this.productionOrderDialogVisible = false;\n    },\n\n    // 获取产品详情\n    getProductDetails(productId) {\n      listProducts({ productId: productId }).then(response => {\n        if (response.rows && response.rows.length > 0) {\n          const product = response.rows[0];\n          this.form.productName = product.product_name;\n          this.form.productCode = product.product_code;\n          this.form.specification = product.product_sfn;\n          this.form.unit = product.product_unit;\n        }\n      }).catch(() => {\n        console.error('获取产品详情失败');\n      });\n    },\n    \n    // 触发上传\n    triggerUpload() {\n      this.$refs.upload.$el.click();\n    },\n    \n    // 打开产品选择弹窗\n    openProductSelection() {\n      if (this.form.sourceType === 'PRODUCTION_ORDER') {\n        // 如果选择了生产订单，但没有选择具体订单\n        if (!this.form.orderCode) {\n          this.$message.warning('请先选择生产订单');\n          return;\n        }\n        // 显示该订单的产品列表\n        this.getProductsByOrder();\n      } else {\n        // 其他情况显示全部产品\n        this.getProductList();\n      }\n      this.productDialogVisible = true;\n    },\n    \n    // 获取产品列表\n    getProductList() {\n      this.productLoading = true;\n      listProducts({\n        pageNum: this.productQuery.pageNum,\n        pageSize: this.productQuery.pageSize,\n        keyword: this.productQuery.keyword,\n        productUnit: this.productQuery.unit,\n        productType: this.productQuery.type,\n        productProperty: this.productQuery.property\n      }).then(response => {\n        this.productLoading = false;\n        if (response.code === 200) {\n          this.productList = response.rows;\n          this.productTotal = response.total;\n        }\n      }).catch(() => {\n        this.productLoading = false;\n        // 模拟数据\n        this.productList = [\n          { product_id: 1, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' },\n          { product_id: 2, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' },\n          { product_id: 3, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' },\n          { product_id: 4, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' },\n          { product_id: 5, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' }\n        ];\n        this.productTotal = 50;\n      });\n    },\n\n    // 根据生产订单获取产品列表\n    getProductsByOrder() {\n      if (!this.selectedProductionOrder) {\n        this.$message.error('未找到选中的生产订单信息');\n        return;\n      }\n\n      this.productLoading = true;\n      // 调用API获取订单产品明细\n      import(\"@/api/sc/productionOrder\").then(api => {\n        api.getProductionOrderDetails(this.selectedProductionOrder.productionOrderId).then(response => {\n          if (response.code === 200 && response.data && response.data.products) {\n            // 将订单明细转换为产品列表格式\n            this.productList = response.data.products.map(detail => ({\n              product_id: detail.productId,\n              product_name: detail.productName,\n              product_code: detail.productCode,\n              product_sfn: detail.productSfn,\n              product_unit: detail.productUnit,\n              // 添加订单相关信息\n              order_qty: detail.qtyNum,\n              delivery_date: detail.deliveryDate\n            }));\n            this.productTotal = this.productList.length;\n          } else {\n            this.$message.warning('该订单暂无产品明细');\n            this.productList = [];\n            this.productTotal = 0;\n          }\n          this.productLoading = false;\n        }).catch(() => {\n          this.$message.error('获取订单产品列表失败');\n          this.productLoading = false;\n        });\n      });\n    },\n\n    // 搜索产品\n    searchProducts() {\n      this.productQuery.pageNum = 1;\n      this.getProductList();\n    },\n    \n    // 重置产品查询条件\n    resetProductQuery() {\n      this.productQuery = {\n        pageNum: 1,\n        pageSize: 10,\n        keyword: \"\",\n        unit: \"\",\n        type: \"\",\n        property: \"\"\n      };\n      this.getProductList();\n    },\n    \n    // 处理产品表格选择变化\n    handleProductSelectionChange(selection) {\n      if (selection.length > 0) {\n        this.selectedProduct = selection[0];\n      } else {\n        this.selectedProduct = null;\n      }\n    },\n    \n    // 处理产品页码变化\n    handleProductCurrentChange(currentPage) {\n      this.productQuery.pageNum = currentPage;\n      this.getProductList();\n    },\n    \n    // 处理产品每页条数变化\n    handleProductSizeChange(size) {\n      this.productQuery.pageSize = size;\n      this.productQuery.pageNum = 1;\n      this.getProductList();\n    },\n    \n    // 确认产品选择\n    confirmProductSelect() {\n      if (this.selectedProduct) {\n        this.form.productId = this.selectedProduct.product_id;\n        this.form.productName = this.selectedProduct.product_name;\n        this.form.productCode = this.selectedProduct.product_code;\n        this.form.specification = this.selectedProduct.product_sfn;\n        this.form.unit = this.selectedProduct.product_unit;\n\n        // 如果是从生产订单选择的产品，自动填充订单数量和交付日期\n        if (this.form.sourceType === 'PRODUCTION_ORDER' && this.selectedProduct.order_qty) {\n          this.form.plannedQty = this.selectedProduct.order_qty;\n          if (this.selectedProduct.delivery_date) {\n            this.form.requiredDate = this.selectedProduct.delivery_date;\n            this.form.planEndTime = this.selectedProduct.delivery_date;\n          }\n        }\n\n        this.productDialogVisible = false;\n\n        // 清空已选BOM\n        this.selectedBom = null;\n        this.selectedBomId = null;\n      } else {\n        this.$message.warning('请选择一个产品！');\n      }\n    },\n    \n    // 选择BOM\n    selectBom() {\n      if (!this.form.productId) {\n        this.$message.warning('请先选择成品！');\n        return;\n      }\n      this.bomDialogVisible = true;\n      this.getBomList();\n    },\n    \n    // 获取BOM列表\n    getBomList() {\n      this.bomLoading = true;\n      listBomsByProductId(this.form.productId).then(response => {\n        this.bomLoading = false;\n        if (response.code === 200) {\n          this.bomList = response.rows;\n          this.bomTotal = response.total;\n          if (!this.bomList || this.bomList.length === 0) {\n            this.$message.info(\"未找到该产品的BOM信息\");\n          } else {\n            // 如果有默认BOM，则自动选中\n            const defaultBom = this.bomList.find(b => b.bom_status === '1');\n            if (defaultBom) {\n              this.handleBomSelect(defaultBom);\n            }\n          }\n        } else {\n          this.bomList = [];\n          this.bomTotal = 0;\n        }\n      }).catch(() => {\n        this.bomLoading = false;\n        this.$message.error('获取BOM列表失败');\n      });\n    },\n    \n    // 处理BOM行选择\n    handleBomSelect(row) {\n      this.selectedBom = row;\n      this.selectedBomId = row.bom_id;\n    },\n    \n    // 确认BOM选择\n    confirmBomSelect() {\n      if (this.selectedBom) {\n        this.bomDialogVisible = false;\n        // 获取BOM详情\n        this.getBomDetail();\n      } else {\n        this.$message.warning('请选择一个BOM！');\n      }\n    },\n    \n    // 获取BOM详情\n    getBomDetail() {\n      findBomDetails(this.selectedBom.bom_id).then(response => {\n        console.log(\"成功获取BOM详情响应:\", response);\n        if (response && response.code === 200) {\n          this.bomDetailList = response.rows;\n        } else {\n          this.bomDetailList = [];\n          this.$message.error(\"获取BOM详情失败: \" + (response ? response.msg : '无响应'));\n        }\n      }).catch(error => {\n        console.error(\"获取BOM详情接口调用失败:\", error);\n        this.$message.error(\"获取BOM详情接口调用失败\");\n        this.bomDetailList = [];\n      });\n    },\n    \n    // 清除已选BOM\n    clearSelectedBom() {\n      this.selectedBom = null;\n      this.selectedBomId = null;\n      this.bomDetailList = [];\n    },\n    \n    // 上传前检查文件类型和大小\n    beforeUpload(file) {\n      const isValidType = /\\.(doc|docx|xls|xlsx|pdf|rar|zip|png|jpg|jpeg)$/i.test(file.name);\n      const isLt10M = file.size / 1024 / 1024 < 10;\n\n      if (!isValidType) {\n        this.$message.error('上传文件格式不支持!');\n        return false;\n      }\n      if (!isLt10M) {\n        this.$message.error('上传文件大小不能超过 10MB!');\n        return false;\n      }\n      return true;\n    },\n    \n    // 上传文件处理\n    uploadFile(options) {\n      // 这里应该调用实际的文件上传API\n      console.log('文件上传:', options.file);\n      // 假设上传成功\n      this.fileList.push({\n        name: options.file.name,\n        url: URL.createObjectURL(options.file)\n      });\n      options.onSuccess();\n    },\n    \n    // 移除文件\n    handleRemove(file) {\n      const index = this.fileList.indexOf(file);\n      if (index !== -1) {\n        this.fileList.splice(index, 1);\n      }\n    },\n    \n    // 表单提交\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          // 额外的日期逻辑验证\n          if (!this.validateDateLogic()) {\n            return;\n          }\n\n          // 表单验证通过，调用API提交数据\n          const data = {\n            planCode: this.form.planCode,\n            planName: this.form.planName,\n            sourceType: this.form.sourceType,\n            orderCode: this.form.orderCode,\n            planStartTime: this.form.planStartTime,\n            planEndTime: this.form.planEndTime,\n            requiredDate: this.form.requiredDate,\n            remark: this.form.remark,\n            productId: this.form.productId,\n            plannedQty: this.form.plannedQty\n          };\n          \n          addProductionPlan(data).then(response => {\n            if (response.code === 200) {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.cancel();\n            } else {\n              this.$modal.msgError(response.msg || \"新增失败\");\n            }\n          }).catch(() => {\n            // 模拟成功响应\n            this.$modal.msgSuccess(\"新增成功\");\n            this.cancel();\n          });\n        }\n      });\n    },\n    \n    // 取消按钮\n    cancel() {\n      this.$router.push({ path: \"/sc/plan\" });\n    },\n\n    // 验证计划数量\n    validatePlannedQty(rule, value, callback) {\n      if (!value) {\n        callback();\n        return;\n      }\n\n      const plannedQty = Number(value);\n      const orderQty = Number(this.form.orderQty);\n\n      if (plannedQty <= 0) {\n        callback(new Error('计划数量必须大于0'));\n        return;\n      }\n\n      if (orderQty > 0 && plannedQty > orderQty) {\n        callback(new Error(`计划数量不能大于订单数量(${orderQty})`));\n        return;\n      }\n\n      callback();\n    },\n\n    // 验证开工时间\n    validateStartTime(rule, value, callback) {\n      if (!value) {\n        callback();\n        return;\n      }\n\n      const today = new Date();\n      today.setHours(0, 0, 0, 0);\n      const startDate = new Date(value);\n\n      // 开工日期不能早于当前日期\n      if (startDate < today) {\n        callback(new Error('开工日期不能早于当前日期'));\n        return;\n      }\n\n      // 如果完工时间已选择，开工时间不能晚于完工时间\n      if (this.form.planEndTime) {\n        const endDate = new Date(this.form.planEndTime);\n        if (startDate > endDate) {\n          callback(new Error('开工日期不能晚于完工日期'));\n          return;\n        }\n      }\n\n      callback();\n    },\n\n    // 验证完工时间\n    validateEndTime(rule, value, callback) {\n      if (!value) {\n        callback();\n        return;\n      }\n\n      const endDate = new Date(value);\n\n      // 如果开工时间已选择，完工时间不能早于开工时间\n      if (this.form.planStartTime) {\n        const startDate = new Date(this.form.planStartTime);\n        if (endDate < startDate) {\n          callback(new Error('完工日期不能早于开工日期'));\n          return;\n        }\n      }\n\n      callback();\n    },\n\n    // 验证需求日期\n    validateRequiredDate(rule, value, callback) {\n      if (!value) {\n        callback();\n        return;\n      }\n\n      const requiredDate = new Date(value);\n\n      // 需求日期不能早于当前日期\n      const today = new Date();\n      today.setHours(0, 0, 0, 0);\n      if (requiredDate < today) {\n        callback(new Error('需求日期不能早于当前日期'));\n        return;\n      }\n\n      // 如果完工时间已选择，需求日期不能早于完工时间\n      if (this.form.planEndTime) {\n        const endDate = new Date(this.form.planEndTime);\n        if (requiredDate < endDate) {\n          callback(new Error('需求日期不能早于完工日期'));\n          return;\n        }\n      }\n\n      callback();\n    },\n\n    // 综合日期逻辑验证\n    validateDateLogic() {\n      const today = new Date();\n      today.setHours(0, 0, 0, 0);\n\n      // 检查开工日期\n      if (this.form.planStartTime) {\n        const startDate = new Date(this.form.planStartTime);\n        if (startDate < today) {\n          this.$modal.msgError(\"开工日期不能早于当前日期\");\n          return false;\n        }\n      }\n\n      // 检查开工日期和完工日期的关系\n      if (this.form.planStartTime && this.form.planEndTime) {\n        const startDate = new Date(this.form.planStartTime);\n        const endDate = new Date(this.form.planEndTime);\n        if (startDate > endDate) {\n          this.$modal.msgError(\"开工日期不能晚于完工日期\");\n          return false;\n        }\n      }\n\n      // 检查需求日期\n      if (this.form.requiredDate) {\n        const requiredDate = new Date(this.form.requiredDate);\n        if (requiredDate < today) {\n          this.$modal.msgError(\"需求日期不能早于当前日期\");\n          return false;\n        }\n\n        // 需求日期不能早于完工日期\n        if (this.form.planEndTime) {\n          const endDate = new Date(this.form.planEndTime);\n          if (requiredDate < endDate) {\n            this.$modal.msgError(\"需求日期不能早于完工日期\");\n            return false;\n          }\n        }\n      }\n\n      return true;\n    }\n  }\n};\n</script>\n\n<style scoped>\n.form-container {\n  background-color: #fff;\n  padding: 10px;\n}\n\n.el-tabs--border-card {\n  box-shadow: none;\n}\n\n.upload-container {\n  width: 100%;\n  border: 1px dashed #d9d9d9;\n  border-radius: 6px;\n  text-align: center;\n  padding: 20px 0;\n  cursor: pointer;\n}\n\n.upload-container:hover {\n  border-color: #409EFF;\n}\n\n.upload-area {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\n.upload-area .el-icon-upload {\n  font-size: 48px;\n  color: #c0c4cc;\n  margin-bottom: 10px;\n}\n\n.upload-text {\n  font-size: 14px;\n  color: #606266;\n  margin-bottom: 10px;\n}\n\n.upload-hint {\n  font-size: 12px;\n  color: #909399;\n}\n\n.input-with-select .el-input-group__append {\n  background-color: #fff;\n}\n\n.bom-title {\n  font-size: 16px;\n  font-weight: bold;\n  color: #303133;\n}\n\n.bom-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  margin: 20px 0;\n  padding: 30px 0;\n}\n\n.folder-icon {\n  margin-bottom: 20px;\n}\n\n.bom-text {\n  font-size: 14px;\n  color: #909399;\n  margin-bottom: 20px;\n}\n\n.warning-text {\n  color: #E6A23C;\n  font-size: 12px;\n  margin-left: 10px;\n}\n\n.warning-text i {\n  margin-right: 5px;\n}\n\n.upload-hidden {\n  width: 100%;\n  height: 100%;\n}\n\n.upload-hidden >>> .el-upload {\n  width: 100%;\n}\n\n.upload-hidden >>> .el-upload-dragger {\n  width: 100%;\n  height: 100%;\n  border: none;\n  padding: 0;\n  margin: 0;\n}\n\n.bom-dialog-header {\n  margin-bottom: 15px;\n  padding: 10px;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n}\n\n.product-info {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.product-info span {\n  margin-right: 20px;\n  line-height: 30px;\n}\n\n.el-radio {\n  margin-right: 0;\n}\n\n.pagination-container {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 0;\n  font-size: 12px;\n}\n\n.pagination-wrapper {\n  display: flex;\n  align-items: center;\n}\n\n.page-size {\n  margin-right: 10px;\n}\n\n.total-text {\n  color: #606266;\n  font-size: 12px;\n}\n\n.bom-info {\n  width: 100%;\n  margin-bottom: 20px;\n}\n\n.bom-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n  padding: 8px;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n}\n\n.bom-title-info {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.bom-title-info span {\n  margin-right: 20px;\n  font-weight: bold;\n}\n\n.bom-detail-table {\n  margin-bottom: 15px;\n}\n\n.bom-action {\n  display: flex;\n  align-items: center;\n}\n\n.select-bom-button {\n  margin-right: 10px;\n}\n</style>\n"]}]}