{"remainingRequest": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\src\\views\\sc\\plan\\edit_plan.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\src\\views\\sc\\plan\\edit_plan.vue", "mtime": 1753786864337}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751945356000}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751945361444}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751945356000}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751945364156}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBnZXRQcm9kdWN0aW9uUGxhbiwgdXBkYXRlUHJvZHVjdGlvblBsYW4sIGFkZFByb2R1Y3Rpb25QbGFuIH0gZnJvbSAiQC9hcGkvc2MvcHJvZHVjdGlvblBsYW4iOw0KaW1wb3J0IHsgbGlzdFByb2R1Y3RzLCBsaXN0Qm9tc0J5UHJvZHVjdElkLCBmaW5kQm9tRGV0YWlscyB9IGZyb20gIkAvYXBpL2Jhc2ljL3Byb2R1Y3QiOw0KaW1wb3J0IHsgZ2V0QXV0b051bWJlcnMgfSBmcm9tICJAL2FwaS9iYXNpYy9udW1iZXJzIjsNCmltcG9ydCBQYWdpbmF0aW9uIGZyb20gIkAvY29tcG9uZW50cy9QYWdpbmF0aW9uIjsNCmltcG9ydCBEaWN0VGFnIGZyb20gIkAvY29tcG9uZW50cy9EaWN0VGFnIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiRWRpdFBsYW4iLA0KICBkaWN0czogWydwcm9kdWN0X3R5cGUnXSwNCiAgY29tcG9uZW50czogew0KICAgIFBhZ2luYXRpb24sDQogICAgRGljdFRhZw0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICAvLyDpobXpnaLmoIfpopgNCiAgICAgIHRpdGxlOiAi5L+u5pS555Sf5Lqn6K6h5YiSIiwNCiAgICAgIC8vIOaYr+WQpuS4uuS/ruaUueaooeW8jw0KICAgICAgaXNFZGl0OiB0cnVlLA0KICAgICAgLy8g6KGo5Y2V5pWw5o2uDQogICAgICBmb3JtOiB7DQogICAgICAgIHBsYW5Db2RlOiAiIiwNCiAgICAgICAgcGxhbk5hbWU6ICIiLA0KICAgICAgICBzb3VyY2VUeXBlOiAiUFJPRFVDVElPTl9PUkRFUiIsDQogICAgICAgIG9yZGVyQ29kZTogIiIsDQogICAgICAgIHByb2R1Y3RJZDogdW5kZWZpbmVkLA0KICAgICAgICBwcm9kdWN0TmFtZTogIiIsDQogICAgICAgIHByb2R1Y3RDb2RlOiAiIiwNCiAgICAgICAgc3BlY2lmaWNhdGlvbjogIiIsDQogICAgICAgIHByb2R1Y3RUeXBlOiAiIiwNCiAgICAgICAgdW5pdDogIiIsDQogICAgICAgIHBsYW5uZWRRdHk6IDEsDQogICAgICAgIHBsYW5TdGFydFRpbWU6ICIiLA0KICAgICAgICBwbGFuRW5kVGltZTogIiIsDQogICAgICAgIHJlcXVpcmVkRGF0ZTogIiIsDQogICAgICAgIHJlbWFyazogIiIsDQogICAgICAgIG9yZGVyUXR5OiAwICAvLyDmt7vliqDorqLljZXmlbDph4/lrZfmrrUNCiAgICAgIH0sDQogICAgICAvLyDmmK/lkKbkvb/nlKjns7vnu5/nvJblj7cNCiAgICAgIGlzU3lzdGVtQ29kZTogdHJ1ZSwNCiAgICAgIC8vIOihqOWNlemqjOivgeinhOWImQ0KICAgICAgcnVsZXM6IHsNCiAgICAgICAgcGxhbk5hbWU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K6h5YiS5ZCN56ew5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgICAgeyBtYXg6IDUwLCBtZXNzYWdlOiAi6ZW/5bqm5LiN6IO96LaF6L+HNTDkuKrlrZfnrKYiLCB0cmlnZ2VyOiAiYmx1ciIgfQ0KICAgICAgICBdLA0KICAgICAgICBzb3VyY2VUeXBlOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuadpea6kOexu+Wei+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0NCiAgICAgICAgXSwNCiAgICAgICAgcHJvZHVjdE5hbWU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36YCJ5oup5Lqn5ZOBIiwgdHJpZ2dlcjogImNoYW5nZSIgfQ0KICAgICAgICBdLA0KICAgICAgICBwbGFubmVkUXR5OiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuiuoeWIkuaVsOmHj+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICAgIHsgdmFsaWRhdG9yOiB0aGlzLnZhbGlkYXRlUGxhbm5lZFF0eSwgdHJpZ2dlcjogImJsdXIiIH0NCiAgICAgICAgXSwNCiAgICAgICAgcGxhblN0YXJ0VGltZTogWw0KICAgICAgICAgIHsgdmFsaWRhdG9yOiB0aGlzLnZhbGlkYXRlU3RhcnRUaW1lLCB0cmlnZ2VyOiAiY2hhbmdlIiB9DQogICAgICAgIF0sDQogICAgICAgIHBsYW5FbmRUaW1lOiBbDQogICAgICAgICAgeyB2YWxpZGF0b3I6IHRoaXMudmFsaWRhdGVFbmRUaW1lLCB0cmlnZ2VyOiAiY2hhbmdlIiB9DQogICAgICAgIF0sDQogICAgICAgIHJlcXVpcmVkRGF0ZTogWw0KICAgICAgICAgIHsgdmFsaWRhdG9yOiB0aGlzLnZhbGlkYXRlUmVxdWlyZWREYXRlLCB0cmlnZ2VyOiAiY2hhbmdlIiB9DQogICAgICAgIF0NCiAgICAgIH0sDQogICAgICAvLyDkuqflk4HkuIvmi4npgInpobkNCiAgICAgIHByb2R1Y3RPcHRpb25zOiBbXSwNCiAgICAgIC8vIOS6p+WTgeWKoOi9veeKtuaAgQ0KICAgICAgcHJvZHVjdExvYWRpbmc6IGZhbHNlLA0KICAgICAgLy8g5p2l5rqQ57G75Z6L6YCJ6aG5DQogICAgICBzb3VyY2VUeXBlT3B0aW9uczogWw0KICAgICAgICB7IGRpY3RMYWJlbDogIueUn+S6p+iuouWNlSIsIGRpY3RWYWx1ZTogIlBST0RVQ1RJT05fT1JERVIiIH0NCiAgICAgIF0sDQogICAgICAvLyDkuIrkvKDmlofku7bliJfooagNCiAgICAgIGZpbGVMaXN0OiBbXSwNCiAgICAgIA0KICAgICAgLy8g5Lqn5ZOB6YCJ5oup5a+56K+d5qGGDQogICAgICBwcm9kdWN0RGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICBwcm9kdWN0UXVlcnk6IHsNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDEwLA0KICAgICAgICBrZXl3b3JkOiAiIiwNCiAgICAgICAgdW5pdDogIiIsDQogICAgICAgIHR5cGU6ICIiLA0KICAgICAgICBwcm9wZXJ0eTogIiINCiAgICAgIH0sDQogICAgICBwcm9kdWN0TGlzdDogW10sDQogICAgICBwcm9kdWN0VG90YWw6IDAsDQogICAgICBzZWxlY3RlZFByb2R1Y3Q6IG51bGwsDQogICAgICANCiAgICAgIC8vIEJPTemAieaLqeWvueivneahhg0KICAgICAgYm9tRGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICBib21RdWVyeTogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgIHByb2R1Y3RJZDogdW5kZWZpbmVkDQogICAgICB9LA0KICAgICAgYm9tTGlzdDogW10sDQogICAgICBib21Ub3RhbDogMCwNCiAgICAgIGJvbUxvYWRpbmc6IGZhbHNlLA0KICAgICAgc2VsZWN0ZWRCb206IG51bGwsDQogICAgICBzZWxlY3RlZEJvbUlkOiBudWxsLA0KICAgICAgYm9tRGV0YWlsTGlzdDogW10sDQogICAgfTsNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICBjb25zdCBwbGFuQ29kZSA9IHRoaXMuJHJvdXRlLnF1ZXJ5LnBsYW5Db2RlOw0KICAgIGlmIChwbGFuQ29kZSkgew0KICAgICAgdGhpcy5pc0VkaXQgPSB0cnVlOw0KICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnnlJ/kuqforqHliJIiOw0KICAgICAgdGhpcy5nZXRQbGFuRGF0YShwbGFuQ29kZSk7DQogICAgfSBlbHNlIHsNCiAgICAgIHRoaXMuaXNFZGl0ID0gZmFsc2U7DQogICAgICB0aGlzLnRpdGxlID0gIuaWsOWinueUn+S6p+iuoeWIkiI7DQogICAgICB0aGlzLmdlbmVyYXRlUGxhbkNvZGUoKTsNCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvLyDojrflj5borqHliJLmlbDmja4NCiAgICBnZXRQbGFuRGF0YShwbGFuQ29kZSkgew0KICAgICAgZ2V0UHJvZHVjdGlvblBsYW4ocGxhbkNvZGUpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhOw0KDQogICAgICAgIC8vIOWmguaenOaYr+eUn+S6p+iuouWNleadpea6kO+8jOiOt+WPluiuouWNleaVsOmHj+S/oeaBrw0KICAgICAgICBpZiAodGhpcy5mb3JtLnNvdXJjZVR5cGUgPT09ICdQUk9EVUNUSU9OX09SREVSJyAmJiB0aGlzLmZvcm0ucHJvZHVjdGlvbk9yZGVySWQpIHsNCiAgICAgICAgICB0aGlzLmdldE9yZGVyUXR5SW5mbyh0aGlzLmZvcm0ucHJvZHVjdGlvbk9yZGVySWQsIHRoaXMuZm9ybS5wcm9kdWN0SWQpOw0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5aaC5p6c5pyJ5YWz6IGU55qE5Lqn5ZOB77yM5YiZ6Ieq5Yqo5Yqg6L295YW2Qk9N5L+h5oGvDQogICAgICAgIGlmICh0aGlzLmZvcm0ucHJvZHVjdElkKSB7DQogICAgICAgICAgdGhpcy5sb2FkQXNzb2NpYXRlZEJvbSgpOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgLy8g6I635Y+W6K6i5Y2V5pWw6YeP5L+h5oGvDQogICAgZ2V0T3JkZXJRdHlJbmZvKHByb2R1Y3Rpb25PcmRlcklkLCBwcm9kdWN0SWQpIHsNCiAgICAgIGltcG9ydCgiQC9hcGkvc2MvcHJvZHVjdGlvbk9yZGVyIikudGhlbihhcGkgPT4gew0KICAgICAgICBhcGkuZ2V0UHJvZHVjdGlvbk9yZGVyRGV0YWlscyhwcm9kdWN0aW9uT3JkZXJJZCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCAmJiByZXNwb25zZS5kYXRhICYmIHJlc3BvbnNlLmRhdGEucHJvZHVjdHMpIHsNCiAgICAgICAgICAgIGNvbnN0IHByb2R1Y3QgPSByZXNwb25zZS5kYXRhLnByb2R1Y3RzLmZpbmQocCA9PiBwLnByb2R1Y3RJZCA9PT0gcHJvZHVjdElkKTsNCiAgICAgICAgICAgIGlmIChwcm9kdWN0KSB7DQogICAgICAgICAgICAgIHRoaXMuZm9ybS5vcmRlclF0eSA9IHByb2R1Y3QucXR5TnVtIHx8IDA7DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W6K6i5Y2V5pWw6YeP5L+h5oGv5aSx6LSlJyk7DQogICAgICAgIH0pOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8vIOWKoOi9veWFs+iBlOeahEJPTQ0KICAgIGxvYWRBc3NvY2lhdGVkQm9tKCkgew0KICAgICAgbGlzdEJvbXNCeVByb2R1Y3RJZCh0aGlzLmZvcm0ucHJvZHVjdElkKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCAmJiByZXNwb25zZS5yb3dzICYmIHJlc3BvbnNlLnJvd3MubGVuZ3RoID4gMCkgew0KICAgICAgICAgIC8vIOafpeaJvum7mOiupOeahEJPTSAoc3RhdHVzICcxJykNCiAgICAgICAgICBjb25zdCBhY3RpdmVCb20gPSByZXNwb25zZS5yb3dzLmZpbmQoYiA9PiBiLmJvbV9zdGF0dXMgPT09ICcxJyk7DQogICAgICAgICAgaWYgKGFjdGl2ZUJvbSkgew0KICAgICAgICAgICAgdGhpcy5zZWxlY3RlZEJvbSA9IGFjdGl2ZUJvbTsNCiAgICAgICAgICAgIHRoaXMuZ2V0Qm9tRGV0YWlsKCk7IC8vIOWKoOi9vUJPTeivpuaDhQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8vIOeUn+aIkOiuoeWIkue8luWPtw0KICAgIGdlbmVyYXRlUGxhbkNvZGUoKSB7DQogICAgICBnZXRBdXRvTnVtYmVycyg2KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIHRoaXMuZm9ybS5wbGFuQ29kZSA9IHJlc3BvbnNlLm1zZzsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfojrflj5borqHliJLnvJblj7flpLHotKUnKTsNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfojrflj5borqHliJLnvJblj7flpLHotKUnKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgDQogICAgLy8g5aSE55CG57O757uf57yW5Y+35byA5YWz5Y+Y5YyWDQogICAgaGFuZGxlU3lzdGVtQ29kZUNoYW5nZSh2YWwpIHsNCiAgICAgIGlmICh2YWwpIHsNCiAgICAgICAgLy8g5aaC5p6c5byA5ZCv57O757uf57yW5Y+377yM5YiZ55Sf5oiQ57yW5Y+3DQogICAgICAgIHRoaXMuZ2VuZXJhdGVQbGFuQ29kZSgpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy8g5aaC5p6c5YWz6Zet57O757uf57yW5Y+377yM5YiZ5riF56m657yW5Y+3DQogICAgICAgIHRoaXMuZm9ybS5wbGFuQ29kZSA9ICcnOw0KICAgICAgfQ0KICAgIH0sDQogICAgDQogICAgLy8g6Kem5Y+R5LiK5LygDQogICAgdHJpZ2dlclVwbG9hZCgpIHsNCiAgICAgIHRoaXMuJHJlZnMudXBsb2FkLiRlbC5jbGljaygpOw0KICAgIH0sDQogICAgDQogICAgLy8g5omT5byA5Lqn5ZOB6YCJ5oup5by556qXDQogICAgb3BlblByb2R1Y3RTZWxlY3Rpb24oKSB7DQogICAgICAvLyDkv67mlLnmqKHlvI/kuIvkuI3lhYHorrjmm7TmjaLkuqflk4ENCiAgICAgIGlmICh0aGlzLmlzRWRpdCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuS/ruaUueaooeW8j+S4i+S4jeWFgeiuuOabtOaNouS6p+WTgeOAgiIpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICB0aGlzLnByb2R1Y3REaWFsb2dWaXNpYmxlID0gdHJ1ZTsNCiAgICAgIHRoaXMuZ2V0UHJvZHVjdExpc3QoKTsNCiAgICB9LA0KICAgIA0KICAgIC8vIOiOt+WPluS6p+WTgeWIl+ihqA0KICAgIGdldFByb2R1Y3RMaXN0KCkgew0KICAgICAgdGhpcy5wcm9kdWN0TG9hZGluZyA9IHRydWU7DQogICAgICBsaXN0UHJvZHVjdHMoew0KICAgICAgICBwYWdlTnVtOiB0aGlzLnByb2R1Y3RRdWVyeS5wYWdlTnVtLA0KICAgICAgICBwYWdlU2l6ZTogdGhpcy5wcm9kdWN0UXVlcnkucGFnZVNpemUsDQogICAgICAgIGtleXdvcmQ6IHRoaXMucHJvZHVjdFF1ZXJ5LmtleXdvcmQsDQogICAgICAgIHByb2R1Y3RVbml0OiB0aGlzLnByb2R1Y3RRdWVyeS51bml0LA0KICAgICAgICBwcm9kdWN0VHlwZTogdGhpcy5wcm9kdWN0UXVlcnkudHlwZSwNCiAgICAgICAgcHJvZHVjdFByb3BlcnR5OiB0aGlzLnByb2R1Y3RRdWVyeS5wcm9wZXJ0eQ0KICAgICAgfSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMucHJvZHVjdExvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIHRoaXMucHJvZHVjdExpc3QgPSByZXNwb25zZS5yb3dzOw0KICAgICAgICAgIHRoaXMucHJvZHVjdFRvdGFsID0gcmVzcG9uc2UudG90YWw7DQogICAgICAgIH0NCiAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgdGhpcy5wcm9kdWN0TG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAvLyDmqKHmi5/mlbDmja4NCiAgICAgICAgdGhpcy5wcm9kdWN0TGlzdCA9IFsNCiAgICAgICAgICB7IHByb2R1Y3RfaWQ6IDEsIHByb2R1Y3RfY29kZTogJ0NQMDI1JywgcHJvZHVjdF9uYW1lOiAn5Lqn5ZOB5LiAJywgcHJvZHVjdF9zZm46ICfok53oibInLCBwcm9kdWN0X3R5cGU6ICfmiJDlk4EnLCBwcm9kdWN0X3VuaXQ6ICfkuKonLCBwcm9kdWN0X3Byb3BlcnR5OiAn6Ieq5Yi2JyB9LA0KICAgICAgICAgIHsgcHJvZHVjdF9pZDogMiwgcHJvZHVjdF9jb2RlOiAnQ1AwMjUnLCBwcm9kdWN0X25hbWU6ICfkuqflk4HkuIAnLCBwcm9kdWN0X3NmbjogJ+iTneiJsicsIHByb2R1Y3RfdHlwZTogJ+aIkOWTgScsIHByb2R1Y3RfdW5pdDogJ+S4qicsIHByb2R1Y3RfcHJvcGVydHk6ICfoh6rliLYnIH0sDQogICAgICAgICAgeyBwcm9kdWN0X2lkOiAzLCBwcm9kdWN0X2NvZGU6ICdDUDAyNScsIHByb2R1Y3RfbmFtZTogJ+S6p+WTgeS4gCcsIHByb2R1Y3Rfc2ZuOiAn6JOd6ImyJywgcHJvZHVjdF90eXBlOiAn5oiQ5ZOBJywgcHJvZHVjdF91bml0OiAn5LiqJywgcHJvZHVjdF9wcm9wZXJ0eTogJ+iHquWIticgfSwNCiAgICAgICAgICB7IHByb2R1Y3RfaWQ6IDQsIHByb2R1Y3RfY29kZTogJ0NQMDI1JywgcHJvZHVjdF9uYW1lOiAn5Lqn5ZOB5LiAJywgcHJvZHVjdF9zZm46ICfok53oibInLCBwcm9kdWN0X3R5cGU6ICfmiJDlk4EnLCBwcm9kdWN0X3VuaXQ6ICfkuKonLCBwcm9kdWN0X3Byb3BlcnR5OiAn6Ieq5Yi2JyB9LA0KICAgICAgICAgIHsgcHJvZHVjdF9pZDogNSwgcHJvZHVjdF9jb2RlOiAnQ1AwMjUnLCBwcm9kdWN0X25hbWU6ICfkuqflk4HkuIAnLCBwcm9kdWN0X3NmbjogJ+iTneiJsicsIHByb2R1Y3RfdHlwZTogJ+aIkOWTgScsIHByb2R1Y3RfdW5pdDogJ+S4qicsIHByb2R1Y3RfcHJvcGVydHk6ICfoh6rliLYnIH0NCiAgICAgICAgXTsNCiAgICAgICAgdGhpcy5wcm9kdWN0VG90YWwgPSA1MDsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgDQogICAgLy8g5pCc57Si5Lqn5ZOBDQogICAgc2VhcmNoUHJvZHVjdHMoKSB7DQogICAgICB0aGlzLnByb2R1Y3RRdWVyeS5wYWdlTnVtID0gMTsNCiAgICAgIHRoaXMuZ2V0UHJvZHVjdExpc3QoKTsNCiAgICB9LA0KICAgIA0KICAgIC8vIOmHjee9ruS6p+WTgeafpeivouadoeS7tg0KICAgIHJlc2V0UHJvZHVjdFF1ZXJ5KCkgew0KICAgICAgdGhpcy5wcm9kdWN0UXVlcnkgPSB7DQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgICAga2V5d29yZDogIiIsDQogICAgICAgIHVuaXQ6ICIiLA0KICAgICAgICB0eXBlOiAiIiwNCiAgICAgICAgcHJvcGVydHk6ICIiDQogICAgICB9Ow0KICAgICAgdGhpcy5nZXRQcm9kdWN0TGlzdCgpOw0KICAgIH0sDQogICAgDQogICAgLy8g5aSE55CG5Lqn5ZOB6KGo5qC86YCJ5oup5Y+Y5YyWDQogICAgaGFuZGxlUHJvZHVjdFNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIGlmIChzZWxlY3Rpb24ubGVuZ3RoID4gMCkgew0KICAgICAgICB0aGlzLnNlbGVjdGVkUHJvZHVjdCA9IHNlbGVjdGlvblswXTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuc2VsZWN0ZWRQcm9kdWN0ID0gbnVsbDsNCiAgICAgIH0NCiAgICB9LA0KICAgIA0KICAgIC8vIOWkhOeQhuS6p+WTgemhteeggeWPmOWMlg0KICAgIGhhbmRsZVByb2R1Y3RDdXJyZW50Q2hhbmdlKGN1cnJlbnRQYWdlKSB7DQogICAgICB0aGlzLnByb2R1Y3RRdWVyeS5wYWdlTnVtID0gY3VycmVudFBhZ2U7DQogICAgICB0aGlzLmdldFByb2R1Y3RMaXN0KCk7DQogICAgfSwNCiAgICANCiAgICAvLyDlpITnkIbkuqflk4Hmr4/pobXmnaHmlbDlj5jljJYNCiAgICBoYW5kbGVQcm9kdWN0U2l6ZUNoYW5nZShzaXplKSB7DQogICAgICB0aGlzLnByb2R1Y3RRdWVyeS5wYWdlU2l6ZSA9IHNpemU7DQogICAgICB0aGlzLnByb2R1Y3RRdWVyeS5wYWdlTnVtID0gMTsNCiAgICAgIHRoaXMuZ2V0UHJvZHVjdExpc3QoKTsNCiAgICB9LA0KICAgIA0KICAgIC8vIOehruiupOS6p+WTgemAieaLqQ0KICAgIGNvbmZpcm1Qcm9kdWN0U2VsZWN0KCkgew0KICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRQcm9kdWN0KSB7DQogICAgICAgIHRoaXMuZm9ybS5wcm9kdWN0SWQgPSB0aGlzLnNlbGVjdGVkUHJvZHVjdC5wcm9kdWN0X2lkOw0KICAgICAgICB0aGlzLmZvcm0ucHJvZHVjdE5hbWUgPSB0aGlzLnNlbGVjdGVkUHJvZHVjdC5wcm9kdWN0X25hbWU7DQogICAgICAgIHRoaXMuZm9ybS5wcm9kdWN0Q29kZSA9IHRoaXMuc2VsZWN0ZWRQcm9kdWN0LnByb2R1Y3RfY29kZTsNCiAgICAgICAgdGhpcy5mb3JtLnNwZWNpZmljYXRpb24gPSB0aGlzLnNlbGVjdGVkUHJvZHVjdC5wcm9kdWN0X3NmbjsNCiAgICAgICAgdGhpcy5mb3JtLnByb2R1Y3RUeXBlID0gdGhpcy5mb3JtYXRQcm9kdWN0VHlwZSh0aGlzLnNlbGVjdGVkUHJvZHVjdCk7DQogICAgICAgIHRoaXMuZm9ybS51bml0ID0gdGhpcy5zZWxlY3RlZFByb2R1Y3QucHJvZHVjdF91bml0Ow0KICAgICAgICB0aGlzLnByb2R1Y3REaWFsb2dWaXNpYmxlID0gZmFsc2U7DQogICAgICAgIA0KICAgICAgICAvLyDmuIXnqbrlt7LpgIlCT00NCiAgICAgICAgdGhpcy5zZWxlY3RlZEJvbSA9IG51bGw7DQogICAgICAgIHRoaXMuc2VsZWN0ZWRCb21JZCA9IG51bGw7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+mAieaLqeS4gOS4quS6p+WTge+8gScpOw0KICAgICAgfQ0KICAgIH0sDQogICAgDQogICAgLy8g5qC85byP5YyW5Lqn5ZOB57G75Z6LDQogICAgZm9ybWF0UHJvZHVjdFR5cGUocm93LCBjb2x1bW4pIHsNCiAgICAgIGNvbnN0IHR5cGUgPSByb3cucHJvZHVjdF90eXBlOw0KICAgICAgY29uc3Qgb3B0aW9uID0gdGhpcy5kaWN0LnR5cGUucHJvZHVjdF90eXBlLmZpbmQoaXRlbSA9PiBpdGVtLmRpY3RWYWx1ZSA9PSB0eXBlKTsNCiAgICAgIHJldHVybiBvcHRpb24gPyBvcHRpb24uZGljdExhYmVsIDogdHlwZTsNCiAgICB9LA0KICAgIA0KICAgIC8vIOmAieaLqUJPTQ0KICAgIHNlbGVjdEJvbSgpIHsNCiAgICAgIGlmICghdGhpcy5mb3JtLnByb2R1Y3RJZCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+WFiOmAieaLqeaIkOWTge+8gScpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICB0aGlzLmJvbURpYWxvZ1Zpc2libGUgPSB0cnVlOw0KICAgICAgdGhpcy5nZXRCb21MaXN0KCk7DQogICAgfSwNCiAgICANCiAgICAvLyDojrflj5ZCT03liJfooagNCiAgICBnZXRCb21MaXN0KCkgew0KICAgICAgdGhpcy5ib21Mb2FkaW5nID0gdHJ1ZTsNCiAgICAgIGxpc3RCb21zQnlQcm9kdWN0SWQodGhpcy5mb3JtLnByb2R1Y3RJZCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuYm9tTG9hZGluZyA9IGZhbHNlOw0KICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgdGhpcy5ib21MaXN0ID0gcmVzcG9uc2Uucm93czsNCiAgICAgICAgICB0aGlzLmJvbVRvdGFsID0gcmVzcG9uc2UudG90YWw7DQogICAgICAgICAgaWYgKCF0aGlzLmJvbUxpc3QgfHwgdGhpcy5ib21MaXN0Lmxlbmd0aCA9PT0gMCkgew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKCLmnKrmib7liLDor6Xkuqflk4HnmoRCT03kv6Hmga8iKTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgLy8g5aaC5p6c5pyJ6buY6K6kQk9N77yM5YiZ6Ieq5Yqo6YCJ5LitDQogICAgICAgICAgICBjb25zdCBkZWZhdWx0Qm9tID0gdGhpcy5ib21MaXN0LmZpbmQoYiA9PiBiLmJvbV9zdGF0dXMgPT09ICcxJyk7DQogICAgICAgICAgICBpZiAoZGVmYXVsdEJvbSkgew0KICAgICAgICAgICAgICB0aGlzLmhhbmRsZUJvbVNlbGVjdChkZWZhdWx0Qm9tKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy5ib21MaXN0ID0gW107DQogICAgICAgICAgdGhpcy5ib21Ub3RhbCA9IDA7DQogICAgICAgIH0NCiAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgdGhpcy5ib21Mb2FkaW5nID0gZmFsc2U7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPlkJPTeWIl+ihqOWksei0pScpOw0KICAgICAgfSk7DQogICAgfSwNCiAgICANCiAgICAvLyDlpITnkIZCT03ooYzpgInmi6kNCiAgICBoYW5kbGVCb21TZWxlY3Qocm93KSB7DQogICAgICB0aGlzLnNlbGVjdGVkQm9tID0gcm93Ow0KICAgICAgdGhpcy5zZWxlY3RlZEJvbUlkID0gcm93LmJvbV9pZDsNCiAgICB9LA0KICAgIA0KICAgIC8vIOehruiupEJPTemAieaLqQ0KICAgIGNvbmZpcm1Cb21TZWxlY3QoKSB7DQogICAgICBpZiAodGhpcy5zZWxlY3RlZEJvbSkgew0KICAgICAgICB0aGlzLmJvbURpYWxvZ1Zpc2libGUgPSBmYWxzZTsNCiAgICAgICAgLy8g6I635Y+WQk9N6K+m5oOFDQogICAgICAgIHRoaXMuZ2V0Qm9tRGV0YWlsKCk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+mAieaLqeS4gOS4qkJPTe+8gScpOw0KICAgICAgfQ0KICAgIH0sDQogICAgDQogICAgLy8g6I635Y+WQk9N6K+m5oOFDQogICAgZ2V0Qm9tRGV0YWlsKCkgew0KICAgICAgZmluZEJvbURldGFpbHModGhpcy5zZWxlY3RlZEJvbS5ib21faWQpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBjb25zb2xlLmxvZygi5oiQ5Yqf6I635Y+WQk9N6K+m5oOF5ZON5bqUOiIsIHJlc3BvbnNlKTsNCiAgICAgICAgaWYgKHJlc3BvbnNlICYmIHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIHRoaXMuYm9tRGV0YWlsTGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy5ib21EZXRhaWxMaXN0ID0gW107DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi6I635Y+WQk9N6K+m5oOF5aSx6LSlOiAiICsgKHJlc3BvbnNlID8gcmVzcG9uc2UubXNnIDogJ+aXoOWTjeW6lCcpKTsNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gew0KICAgICAgICBjb25zb2xlLmVycm9yKCLojrflj5ZCT03or6bmg4XmjqXlj6PosIPnlKjlpLHotKU6IiwgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLojrflj5ZCT03or6bmg4XmjqXlj6PosIPnlKjlpLHotKUiKTsNCiAgICAgICAgdGhpcy5ib21EZXRhaWxMaXN0ID0gW107DQogICAgICB9KTsNCiAgICB9LA0KICAgIA0KICAgIC8vIOa4hemZpOW3sumAiUJPTQ0KICAgIGNsZWFyU2VsZWN0ZWRCb20oKSB7DQogICAgICB0aGlzLnNlbGVjdGVkQm9tID0gbnVsbDsNCiAgICAgIHRoaXMuc2VsZWN0ZWRCb21JZCA9IG51bGw7DQogICAgICB0aGlzLmJvbURldGFpbExpc3QgPSBbXTsNCiAgICB9LA0KICAgIA0KICAgIC8vIOS4iuS8oOWJjeajgOafpeaWh+S7tuexu+Wei+WSjOWkp+Wwjw0KICAgIGJlZm9yZVVwbG9hZChmaWxlKSB7DQogICAgICBjb25zdCBpc1ZhbGlkVHlwZSA9IC9cLihkb2N8ZG9jeHx4bHN8eGxzeHxwZGZ8cmFyfHppcHxwbmd8anBnfGpwZWcpJC9pLnRlc3QoZmlsZS5uYW1lKTsNCiAgICAgIGNvbnN0IGlzTHQxME0gPSBmaWxlLnNpemUgLyAxMDI0IC8gMTAyNCA8IDEwOw0KDQogICAgICBpZiAoIWlzVmFsaWRUeXBlKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+S4iuS8oOaWh+S7tuagvOW8j+S4jeaUr+aMgSEnKTsNCiAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgfQ0KICAgICAgaWYgKCFpc0x0MTBNKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+S4iuS8oOaWh+S7tuWkp+Wwj+S4jeiDvei2hei/hyAxME1CIScpOw0KICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICB9DQogICAgICByZXR1cm4gdHJ1ZTsNCiAgICB9LA0KICAgIA0KICAgIC8vIOS4iuS8oOaWh+S7tuWkhOeQhg0KICAgIHVwbG9hZEZpbGUob3B0aW9ucykgew0KICAgICAgLy8g6L+Z6YeM5bqU6K+l6LCD55So5a6e6ZmF55qE5paH5Lu25LiK5LygQVBJDQogICAgICBjb25zb2xlLmxvZygn5paH5Lu25LiK5LygOicsIG9wdGlvbnMuZmlsZSk7DQogICAgICAvLyDlgYforr7kuIrkvKDmiJDlip8NCiAgICAgIHRoaXMuZmlsZUxpc3QucHVzaCh7DQogICAgICAgIG5hbWU6IG9wdGlvbnMuZmlsZS5uYW1lLA0KICAgICAgICB1cmw6IFVSTC5jcmVhdGVPYmplY3RVUkwob3B0aW9ucy5maWxlKQ0KICAgICAgfSk7DQogICAgICBvcHRpb25zLm9uU3VjY2VzcygpOw0KICAgIH0sDQogICAgDQogICAgLy8g56e76Zmk5paH5Lu2DQogICAgaGFuZGxlUmVtb3ZlKGZpbGUpIHsNCiAgICAgIGNvbnN0IGluZGV4ID0gdGhpcy5maWxlTGlzdC5pbmRleE9mKGZpbGUpOw0KICAgICAgaWYgKGluZGV4ICE9PSAtMSkgew0KICAgICAgICB0aGlzLmZpbGVMaXN0LnNwbGljZShpbmRleCwgMSk7DQogICAgICB9DQogICAgfSwNCiAgICANCiAgICAvLyDooajljZXmj5DkuqQNCiAgICBzdWJtaXRGb3JtKCkgew0KICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgLy8g6aKd5aSW55qE5pel5pyf6YC76L6R6aqM6K+BDQogICAgICAgICAgaWYgKCF0aGlzLnZhbGlkYXRlRGF0ZUxvZ2ljKCkpIHsNCiAgICAgICAgICAgIHJldHVybjsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICAvLyDmoLnmja5pc0VkaXTmoIflv5flhrPlrprosIPnlKjlk6rkuKpBUEkNCiAgICAgICAgICBjb25zdCBhcGlDYWxsID0gdGhpcy5pc0VkaXQgPyB1cGRhdGVQcm9kdWN0aW9uUGxhbiA6IGFkZFByb2R1Y3Rpb25QbGFuOw0KICAgICAgICAgIA0KICAgICAgICAgIGFwaUNhbGwodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcyh0aGlzLmlzRWRpdCA/ICLkv67mlLnmiJDlip8iIDogIuaWsOWinuaIkOWKnyIpOw0KICAgICAgICAgICAgICB0aGlzLmNhbmNlbCgpOw0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IocmVzcG9uc2UubXNnIHx8ICh0aGlzLmlzRWRpdCA/ICLkv67mlLnlpLHotKUiIDogIuaWsOWinuWksei0pSIpKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi5pON5L2c5aSx6LSl77yM6K+356iN5ZCO6YeN6K+VIik7DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgDQogICAgLy8g5Y+W5raI5oyJ6ZKuDQogICAgY2FuY2VsKCkgew0KICAgICAgdGhpcy4kcm91dGVyLnB1c2goeyBwYXRoOiAiL3NjL3BsYW4iIH0pOw0KICAgIH0sDQoNCiAgICAvLyDpqozor4HlvIDlt6Xml7bpl7QNCiAgICB2YWxpZGF0ZVN0YXJ0VGltZShydWxlLCB2YWx1ZSwgY2FsbGJhY2spIHsNCiAgICAgIGlmICghdmFsdWUpIHsNCiAgICAgICAgY2FsbGJhY2soKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICBjb25zdCB0b2RheSA9IG5ldyBEYXRlKCk7DQogICAgICB0b2RheS5zZXRIb3VycygwLCAwLCAwLCAwKTsNCiAgICAgIGNvbnN0IHN0YXJ0RGF0ZSA9IG5ldyBEYXRlKHZhbHVlKTsNCg0KICAgICAgLy8g5byA5bel5pel5pyf5LiN6IO95pep5LqO5b2T5YmN5pel5pyfDQogICAgICBpZiAoc3RhcnREYXRlIDwgdG9kYXkpIHsNCiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCflvIDlt6Xml6XmnJ/kuI3og73ml6nkuo7lvZPliY3ml6XmnJ8nKSk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgLy8g5aaC5p6c5a6M5bel5pe26Ze05bey6YCJ5oup77yM5byA5bel5pe26Ze05LiN6IO95pma5LqO5a6M5bel5pe26Ze0DQogICAgICBpZiAodGhpcy5mb3JtLnBsYW5FbmRUaW1lKSB7DQogICAgICAgIGNvbnN0IGVuZERhdGUgPSBuZXcgRGF0ZSh0aGlzLmZvcm0ucGxhbkVuZFRpbWUpOw0KICAgICAgICBpZiAoc3RhcnREYXRlID4gZW5kRGF0ZSkgew0KICAgICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcign5byA5bel5pel5pyf5LiN6IO95pma5LqO5a6M5bel5pel5pyfJykpOw0KICAgICAgICAgIHJldHVybjsNCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICBjYWxsYmFjaygpOw0KICAgIH0sDQoNCiAgICAvLyDpqozor4Hlrozlt6Xml7bpl7QNCiAgICB2YWxpZGF0ZUVuZFRpbWUocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSB7DQogICAgICBpZiAoIXZhbHVlKSB7DQogICAgICAgIGNhbGxiYWNrKCk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgY29uc3QgZW5kRGF0ZSA9IG5ldyBEYXRlKHZhbHVlKTsNCg0KICAgICAgLy8g5aaC5p6c5byA5bel5pe26Ze05bey6YCJ5oup77yM5a6M5bel5pe26Ze05LiN6IO95pep5LqO5byA5bel5pe26Ze0DQogICAgICBpZiAodGhpcy5mb3JtLnBsYW5TdGFydFRpbWUpIHsNCiAgICAgICAgY29uc3Qgc3RhcnREYXRlID0gbmV3IERhdGUodGhpcy5mb3JtLnBsYW5TdGFydFRpbWUpOw0KICAgICAgICBpZiAoZW5kRGF0ZSA8IHN0YXJ0RGF0ZSkgew0KICAgICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcign5a6M5bel5pel5pyf5LiN6IO95pep5LqO5byA5bel5pel5pyfJykpOw0KICAgICAgICAgIHJldHVybjsNCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICBjYWxsYmFjaygpOw0KICAgIH0sDQoNCiAgICAvLyDpqozor4HorqHliJLmlbDph48NCiAgICB2YWxpZGF0ZVBsYW5uZWRRdHkocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSB7DQogICAgICBpZiAoIXZhbHVlKSB7DQogICAgICAgIGNhbGxiYWNrKCk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgY29uc3QgcGxhbm5lZFF0eSA9IE51bWJlcih2YWx1ZSk7DQogICAgICBjb25zdCBvcmRlclF0eSA9IE51bWJlcih0aGlzLmZvcm0ub3JkZXJRdHkpOw0KDQogICAgICBpZiAocGxhbm5lZFF0eSA8PSAwKSB7DQogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcign6K6h5YiS5pWw6YeP5b+F6aG75aSn5LqOMCcpKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICBpZiAob3JkZXJRdHkgPiAwICYmIHBsYW5uZWRRdHkgPiBvcmRlclF0eSkgew0KICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoYOiuoeWIkuaVsOmHj+S4jeiDveWkp+S6juiuouWNleaVsOmHjygke29yZGVyUXR5fSlgKSk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgY2FsbGJhY2soKTsNCiAgICB9LA0KDQogICAgLy8g6aqM6K+B6ZyA5rGC5pel5pyfDQogICAgdmFsaWRhdGVSZXF1aXJlZERhdGUocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSB7DQogICAgICBpZiAoIXZhbHVlKSB7DQogICAgICAgIGNhbGxiYWNrKCk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgY29uc3QgcmVxdWlyZWREYXRlID0gbmV3IERhdGUodmFsdWUpOw0KDQogICAgICAvLyDpnIDmsYLml6XmnJ/kuI3og73ml6nkuo7lvZPliY3ml6XmnJ8NCiAgICAgIGNvbnN0IHRvZGF5ID0gbmV3IERhdGUoKTsNCiAgICAgIHRvZGF5LnNldEhvdXJzKDAsIDAsIDAsIDApOw0KICAgICAgaWYgKHJlcXVpcmVkRGF0ZSA8IHRvZGF5KSB7DQogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcign6ZyA5rGC5pel5pyf5LiN6IO95pep5LqO5b2T5YmN5pel5pyfJykpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIC8vIOWmguaenOWujOW3peaXtumXtOW3sumAieaLqe+8jOmcgOaxguaXpeacn+S4jeiDveaXqeS6juWujOW3peaXtumXtA0KICAgICAgaWYgKHRoaXMuZm9ybS5wbGFuRW5kVGltZSkgew0KICAgICAgICBjb25zdCBlbmREYXRlID0gbmV3IERhdGUodGhpcy5mb3JtLnBsYW5FbmRUaW1lKTsNCiAgICAgICAgaWYgKHJlcXVpcmVkRGF0ZSA8IGVuZERhdGUpIHsNCiAgICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoJ+mcgOaxguaXpeacn+S4jeiDveaXqeS6juWujOW3peaXpeacnycpKTsNCiAgICAgICAgICByZXR1cm47DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgY2FsbGJhY2soKTsNCiAgICB9LA0KDQogICAgLy8g57u85ZCI5pel5pyf6YC76L6R6aqM6K+BDQogICAgdmFsaWRhdGVEYXRlTG9naWMoKSB7DQogICAgICBjb25zdCB0b2RheSA9IG5ldyBEYXRlKCk7DQogICAgICB0b2RheS5zZXRIb3VycygwLCAwLCAwLCAwKTsNCg0KICAgICAgLy8g5qOA5p+l5byA5bel5pel5pyfDQogICAgICBpZiAodGhpcy5mb3JtLnBsYW5TdGFydFRpbWUpIHsNCiAgICAgICAgY29uc3Qgc3RhcnREYXRlID0gbmV3IERhdGUodGhpcy5mb3JtLnBsYW5TdGFydFRpbWUpOw0KICAgICAgICBpZiAoc3RhcnREYXRlIDwgdG9kYXkpIHsNCiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi5byA5bel5pel5pyf5LiN6IO95pep5LqO5b2T5YmN5pel5pyfIik7DQogICAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIC8vIOajgOafpeW8gOW3peaXpeacn+WSjOWujOW3peaXpeacn+eahOWFs+ezuw0KICAgICAgaWYgKHRoaXMuZm9ybS5wbGFuU3RhcnRUaW1lICYmIHRoaXMuZm9ybS5wbGFuRW5kVGltZSkgew0KICAgICAgICBjb25zdCBzdGFydERhdGUgPSBuZXcgRGF0ZSh0aGlzLmZvcm0ucGxhblN0YXJ0VGltZSk7DQogICAgICAgIGNvbnN0IGVuZERhdGUgPSBuZXcgRGF0ZSh0aGlzLmZvcm0ucGxhbkVuZFRpbWUpOw0KICAgICAgICBpZiAoc3RhcnREYXRlID4gZW5kRGF0ZSkgew0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLlvIDlt6Xml6XmnJ/kuI3og73mmZrkuo7lrozlt6Xml6XmnJ8iKTsNCiAgICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgLy8g5qOA5p+l6ZyA5rGC5pel5pyfDQogICAgICBpZiAodGhpcy5mb3JtLnJlcXVpcmVkRGF0ZSkgew0KICAgICAgICBjb25zdCByZXF1aXJlZERhdGUgPSBuZXcgRGF0ZSh0aGlzLmZvcm0ucmVxdWlyZWREYXRlKTsNCiAgICAgICAgaWYgKHJlcXVpcmVkRGF0ZSA8IHRvZGF5KSB7DQogICAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIumcgOaxguaXpeacn+S4jeiDveaXqeS6juW9k+WJjeaXpeacnyIpOw0KICAgICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOmcgOaxguaXpeacn+S4jeiDveaXqeS6juWujOW3peaXpeacnw0KICAgICAgICBpZiAodGhpcy5mb3JtLnBsYW5FbmRUaW1lKSB7DQogICAgICAgICAgY29uc3QgZW5kRGF0ZSA9IG5ldyBEYXRlKHRoaXMuZm9ybS5wbGFuRW5kVGltZSk7DQogICAgICAgICAgaWYgKHJlcXVpcmVkRGF0ZSA8IGVuZERhdGUpIHsNCiAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLpnIDmsYLml6XmnJ/kuI3og73ml6nkuo7lrozlt6Xml6XmnJ8iKTsNCiAgICAgICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgcmV0dXJuIHRydWU7DQogICAgfQ0KICB9DQp9Ow0K"}, {"version": 3, "sources": ["edit_plan.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAi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file": "edit_plan.vue", "sourceRoot": "src/views/sc/plan", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"form-container\">\r\n      <!-- 基础信息区 -->\r\n      <el-tabs type=\"border-card\">\r\n        <el-tab-pane>\r\n          <span slot=\"label\"><i class=\"el-icon-date\"></i> 基础信息</span>\r\n          <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"isEdit ? 12 : 8\">\r\n                <el-form-item label=\"计划编号\" prop=\"planCode\" required>\r\n                  <el-input v-model=\"form.planCode\" placeholder=\"请输入\" :disabled=\"isSystemCode || isEdit\"></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"4\" v-if=\"!isEdit\">\r\n                <el-switch\r\n                  v-model=\"isSystemCode\"\r\n                  active-text=\"系统编号\"\r\n                  inactive-text=\"\"\r\n                  style=\"margin-top: 13px;\"\r\n                  @change=\"handleSystemCodeChange\"\r\n                ></el-switch>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"计划名称\" prop=\"planName\" required>\r\n                  <el-input v-model=\"form.planName\" placeholder=\"请输入\"></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"来源类型\" prop=\"sourceType\" required>\r\n                  <el-select v-model=\"form.sourceType\" placeholder=\"生产订单\" style=\"width: 100%\">\r\n                    <el-option\r\n                      v-for=\"item in sourceTypeOptions\"\r\n                      :key=\"item.dictValue\"\r\n                      :label=\"item.dictLabel\"\r\n                      :value=\"item.dictValue\"\r\n                    ></el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"订单编号\" prop=\"orderCode\">\r\n                  <el-input v-model=\"form.orderCode\" placeholder=\"请输入\" :disabled=\"isEdit\"></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"成品名称\" prop=\"productName\">\r\n                  <el-input\r\n                    placeholder=\"请选择成品\"\r\n                    v-model=\"form.productName\"\r\n                    class=\"input-with-select\"\r\n                  >\r\n                    <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"openProductSelection\"></el-button>\r\n                  </el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"成品编号\" prop=\"productCode\">\r\n                  <el-input v-model=\"form.productCode\" placeholder=\"请输入\" disabled></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"规格型号\" prop=\"specification\">\r\n                  <el-input v-model=\"form.specification\" placeholder=\"请输入\" disabled></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"成品类型\" prop=\"productType\">\r\n                  <dict-tag :options=\"dict.type.product_type\" :value=\"form.productType\" />\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"单位\" prop=\"unit\">\r\n                  <el-input v-model=\"form.unit\" placeholder=\"请输入\" disabled></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"计划数量\" prop=\"plannedQty\" required>\r\n                  <el-input-number v-model=\"form.plannedQty\" :min=\"1\" controls-position=\"right\" style=\"width: 100%\"></el-input-number>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"开工时间\" prop=\"planStartTime\">\r\n                  <el-date-picker\r\n                    v-model=\"form.planStartTime\"\r\n                    type=\"date\"\r\n                    placeholder=\"请选择日期\"\r\n                    style=\"width: 100%\"\r\n                    value-format=\"yyyy-MM-dd\"\r\n                  ></el-date-picker>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"完工时间\" prop=\"planEndTime\">\r\n                  <el-date-picker\r\n                    v-model=\"form.planEndTime\"\r\n                    type=\"date\"\r\n                    placeholder=\"请选择日期\"\r\n                    style=\"width: 100%\"\r\n                    value-format=\"yyyy-MM-dd\"\r\n                  ></el-date-picker>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"需求日期\" prop=\"requiredDate\">\r\n                  <el-date-picker\r\n                    v-model=\"form.requiredDate\"\r\n                    type=\"date\"\r\n                    placeholder=\"请选择日期\"\r\n                    style=\"width: 100%\"\r\n                    value-format=\"yyyy-MM-dd\"\r\n                  ></el-date-picker>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"备注\" prop=\"remark\">\r\n                  <el-input\r\n                    type=\"textarea\"\r\n                    v-model=\"form.remark\"\r\n                    placeholder=\"请输入\"\r\n                    :rows=\"4\"\r\n                  ></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"附件\" prop=\"attachment\">\r\n                  <div class=\"upload-container\" @click=\"triggerUpload\">\r\n                    <el-upload\r\n                      ref=\"upload\"\r\n                      class=\"upload-hidden\"\r\n                      action=\"#\"\r\n                      :http-request=\"uploadFile\"\r\n                      :file-list=\"fileList\"\r\n                      :before-upload=\"beforeUpload\"\r\n                      :on-remove=\"handleRemove\"\r\n                      multiple\r\n                      drag\r\n                    >\r\n                      <div class=\"upload-area\">\r\n                        <i class=\"el-icon-upload\"></i>\r\n                        <div class=\"upload-text\">点击或者拖动文件到虚线框内上传</div>\r\n                        <div class=\"upload-hint\">支持 docx, xls, PDF, rar, zip, PNG, JPG 等类型的文件</div>\r\n                      </div>\r\n                    </el-upload>\r\n                  </div>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-divider>\r\n              <span class=\"bom-title\">BOM组成</span>\r\n            </el-divider>\r\n            \r\n            <el-row>\r\n              <el-col :span=\"24\">\r\n                <div class=\"bom-container\">\r\n                  <div class=\"folder-icon\" v-if=\"!selectedBom\">\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"60\" height=\"60\" viewBox=\"0 0 24 24\" fill=\"#DCDFE6\">\r\n                      <path d=\"M10 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2h-8l-2-2z\"/>\r\n                    </svg>\r\n                  </div>\r\n                  <div class=\"bom-text\" v-if=\"!selectedBom\">暂无数据</div>\r\n                  <div class=\"bom-info\" v-else>\r\n                    <div class=\"bom-header\">\r\n                      <div class=\"bom-title-info\">\r\n                        <span>BOM编号：{{ selectedBom.bom_code }}</span>\r\n                        <span>版本号：{{ selectedBom.bom_version }}</span>\r\n                      </div>\r\n                      <el-button type=\"text\" icon=\"el-icon-delete\" @click=\"clearSelectedBom\">清除</el-button>\r\n                    </div>\r\n                    <el-table\r\n                      :data=\"bomDetailList\"\r\n                      border\r\n                      size=\"small\"\r\n                      style=\"width: 100%\"\r\n                      class=\"bom-detail-table\"\r\n                    >\r\n                      <el-table-column label=\"序号\" type=\"index\" width=\"50\" align=\"center\"></el-table-column>\r\n                      <el-table-column prop=\"material_code\" label=\"物料编码\" min-width=\"120\" align=\"center\"></el-table-column>\r\n                      <el-table-column prop=\"material_name\" label=\"物料名称\" min-width=\"150\" align=\"center\"></el-table-column>\r\n                      <el-table-column prop=\"specification\" label=\"规格型号\" min-width=\"100\" align=\"center\"></el-table-column>\r\n                      <el-table-column prop=\"unit\" label=\"单位\" width=\"60\" align=\"center\"></el-table-column>\r\n                      <el-table-column prop=\"quantity\" label=\"用量\" width=\"80\" align=\"center\"></el-table-column>\r\n                      <el-table-column prop=\"remark\" label=\"备注\" min-width=\"120\" align=\"center\"></el-table-column>\r\n                    </el-table>\r\n                  </div>\r\n                  <div class=\"bom-action\">\r\n                    <el-tooltip :disabled=\"form.productId\" content=\"请先选择成品!\" placement=\"right\" effect=\"light\">\r\n                      <el-button type=\"primary\" class=\"select-bom-button\" @click=\"selectBom\">选择BOM</el-button>\r\n                    </el-tooltip>\r\n                  </div>\r\n                </div>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row>\r\n              <el-col :span=\"24\" style=\"text-align: center; margin-top: 20px;\">\r\n                <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n                <el-button @click=\"cancel\">取 消</el-button>\r\n              </el-col>\r\n            </el-row>\r\n          </el-form>\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n    </div>\r\n    \r\n    <!-- 产品选择对话框 -->\r\n    <el-dialog title=\"选择成品\" :visible.sync=\"productDialogVisible\" width=\"40%\" append-to-body>\r\n      <el-form :model=\"productQuery\" ref=\"productQueryForm\" :inline=\"true\" class=\"demo-form-inline\" size=\"small\">\r\n        <el-form-item>\r\n          <el-input v-model=\"productQuery.keyword\" placeholder=\"请输入产品编号/名称\" clearable style=\"width: 180px;\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-select v-model=\"productQuery.unit\" placeholder=\"请选择单位\" clearable style=\"width: 120px;\">\r\n            <el-option label=\"个\" value=\"个\"></el-option>\r\n            <el-option label=\"件\" value=\"件\"></el-option>\r\n            <el-option label=\"台\" value=\"台\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-select v-model=\"productQuery.type\" placeholder=\"请选择类型\" clearable style=\"width: 120px;\">\r\n            <el-option label=\"成品\" value=\"成品\"></el-option>\r\n            <el-option label=\"半成品\" value=\"半成品\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-select v-model=\"productQuery.property\" placeholder=\"请选择产品属性\" clearable style=\"width: 120px;\">\r\n            <el-option label=\"自制\" value=\"自制\"></el-option>\r\n            <el-option label=\"外购\" value=\"外购\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"small\" @click=\"searchProducts\">查询</el-button>\r\n          <el-button icon=\"el-icon-refresh\" size=\"small\" @click=\"resetProductQuery\">重置</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n      \r\n      <el-table\r\n        v-loading=\"productLoading\"\r\n        :data=\"productList\"\r\n        border\r\n        size=\"small\"\r\n        style=\"width: 100%\"\r\n        @selection-change=\"handleProductSelectionChange\"\r\n        height=\"300\"\r\n      >\r\n        <el-table-column type=\"selection\" width=\"40\" align=\"center\"></el-table-column>\r\n        <el-table-column label=\"序号\" type=\"index\" width=\"40\" align=\"center\"></el-table-column>\r\n        <el-table-column prop=\"product_code\" label=\"产品编号\" width=\"90\" align=\"center\"></el-table-column>\r\n        <el-table-column prop=\"product_name\" label=\"产品名称\" min-width=\"120\" align=\"center\"></el-table-column>\r\n        <el-table-column prop=\"product_sfn\" label=\"规格型号\" min-width=\"90\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <span :style=\"{color: '#1890ff'}\">{{ scope.row.product_sfn }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"product_unit\" label=\"单位\" width=\"60\" align=\"center\"></el-table-column>\r\n        <el-table-column prop=\"product_type\" label=\"产品类型\" width=\"70\" align=\"center\" :formatter=\"formatProductType\"></el-table-column>\r\n      </el-table>\r\n      \r\n      <div class=\"pagination-container\">\r\n        <span class=\"total-text\">共 {{ productTotal }} 条</span>\r\n        <div class=\"pagination-wrapper\">\r\n          <span class=\"page-size\">\r\n            <el-select v-model=\"productQuery.pageSize\" size=\"mini\" @change=\"handleProductSizeChange\" style=\"width: 80px;\">\r\n              <el-option\r\n                v-for=\"item in [10, 20, 30, 50]\"\r\n                :key=\"item\"\r\n                :label=\"`${item}条/页`\"\r\n                :value=\"item\">\r\n              </el-option>\r\n            </el-select>\r\n          </span>\r\n          <el-pagination\r\n            small\r\n            background\r\n            @current-change=\"handleProductCurrentChange\"\r\n            :current-page=\"productQuery.pageNum\"\r\n            :page-size=\"productQuery.pageSize\"\r\n            layout=\"prev, pager, next, jumper\"\r\n            :pager-count=\"5\"\r\n            :total=\"productTotal\">\r\n          </el-pagination>\r\n        </div>\r\n      </div>\r\n      \r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button size=\"small\" @click=\"productDialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" size=\"small\" @click=\"confirmProductSelect\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    \r\n    <!-- BOM选择对话框 -->\r\n    <el-dialog title=\"选择BOM\" :visible.sync=\"bomDialogVisible\" width=\"40%\" append-to-body>\r\n      <div class=\"bom-dialog-header\">\r\n        <div class=\"product-info\">\r\n          <span>产品名称：{{ form.productName }}</span>\r\n          <span>产品编号：{{ form.productCode }}</span>\r\n          <span>规格型号：{{ form.specification }}</span>\r\n          <span>单位：{{ form.unit }}</span>\r\n        </div>\r\n      </div>\r\n      \r\n      <el-table\r\n        v-loading=\"bomLoading\"\r\n        :data=\"bomList\"\r\n        border\r\n        style=\"width: 100%\"\r\n        @row-click=\"handleBomSelect\"\r\n        highlight-current-row\r\n        size=\"small\"\r\n        height=\"300\"\r\n      >\r\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-radio v-model=\"selectedBomId\" :label=\"scope.row.bom_id\" @change=\"handleBomSelect(scope.row)\">&nbsp;</el-radio>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"序号\" type=\"index\" width=\"55\" align=\"center\"></el-table-column>\r\n        <el-table-column prop=\"bom_code\" label=\"BOM编号\" min-width=\"150\" align=\"center\"></el-table-column>\r\n        <el-table-column prop=\"bom_version\" label=\"版本号\" width=\"100\" align=\"center\"></el-table-column>\r\n        <el-table-column prop=\"bom_status\" label=\"默认BOM\" width=\"100\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ scope.row.bom_status === '1' ? '是' : '否' }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"bom_output\" label=\"日产量\" width=\"100\" align=\"center\"></el-table-column>\r\n      </el-table>\r\n      \r\n      <pagination\r\n        v-show=\"bomTotal > 0\"\r\n        :total=\"bomTotal\"\r\n        :page.sync=\"bomQuery.pageNum\"\r\n        :limit.sync=\"bomQuery.pageSize\"\r\n        @pagination=\"getBomList\"\r\n      />\r\n      \r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"bomDialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmBomSelect\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getProductionPlan, updateProductionPlan, addProductionPlan } from \"@/api/sc/productionPlan\";\r\nimport { listProducts, listBomsByProductId, findBomDetails } from \"@/api/basic/product\";\r\nimport { getAutoNumbers } from \"@/api/basic/numbers\";\r\nimport Pagination from \"@/components/Pagination\";\r\nimport DictTag from \"@/components/DictTag\";\r\n\r\nexport default {\r\n  name: \"EditPlan\",\r\n  dicts: ['product_type'],\r\n  components: {\r\n    Pagination,\r\n    DictTag\r\n  },\r\n  data() {\r\n    return {\r\n      // 页面标题\r\n      title: \"修改生产计划\",\r\n      // 是否为修改模式\r\n      isEdit: true,\r\n      // 表单数据\r\n      form: {\r\n        planCode: \"\",\r\n        planName: \"\",\r\n        sourceType: \"PRODUCTION_ORDER\",\r\n        orderCode: \"\",\r\n        productId: undefined,\r\n        productName: \"\",\r\n        productCode: \"\",\r\n        specification: \"\",\r\n        productType: \"\",\r\n        unit: \"\",\r\n        plannedQty: 1,\r\n        planStartTime: \"\",\r\n        planEndTime: \"\",\r\n        requiredDate: \"\",\r\n        remark: \"\",\r\n        orderQty: 0  // 添加订单数量字段\r\n      },\r\n      // 是否使用系统编号\r\n      isSystemCode: true,\r\n      // 表单验证规则\r\n      rules: {\r\n        planName: [\r\n          { required: true, message: \"计划名称不能为空\", trigger: \"blur\" },\r\n          { max: 50, message: \"长度不能超过50个字符\", trigger: \"blur\" }\r\n        ],\r\n        sourceType: [\r\n          { required: true, message: \"来源类型不能为空\", trigger: \"change\" }\r\n        ],\r\n        productName: [\r\n          { required: true, message: \"请选择产品\", trigger: \"change\" }\r\n        ],\r\n        plannedQty: [\r\n          { required: true, message: \"计划数量不能为空\", trigger: \"blur\" },\r\n          { validator: this.validatePlannedQty, trigger: \"blur\" }\r\n        ],\r\n        planStartTime: [\r\n          { validator: this.validateStartTime, trigger: \"change\" }\r\n        ],\r\n        planEndTime: [\r\n          { validator: this.validateEndTime, trigger: \"change\" }\r\n        ],\r\n        requiredDate: [\r\n          { validator: this.validateRequiredDate, trigger: \"change\" }\r\n        ]\r\n      },\r\n      // 产品下拉选项\r\n      productOptions: [],\r\n      // 产品加载状态\r\n      productLoading: false,\r\n      // 来源类型选项\r\n      sourceTypeOptions: [\r\n        { dictLabel: \"生产订单\", dictValue: \"PRODUCTION_ORDER\" }\r\n      ],\r\n      // 上传文件列表\r\n      fileList: [],\r\n      \r\n      // 产品选择对话框\r\n      productDialogVisible: false,\r\n      productQuery: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        keyword: \"\",\r\n        unit: \"\",\r\n        type: \"\",\r\n        property: \"\"\r\n      },\r\n      productList: [],\r\n      productTotal: 0,\r\n      selectedProduct: null,\r\n      \r\n      // BOM选择对话框\r\n      bomDialogVisible: false,\r\n      bomQuery: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        productId: undefined\r\n      },\r\n      bomList: [],\r\n      bomTotal: 0,\r\n      bomLoading: false,\r\n      selectedBom: null,\r\n      selectedBomId: null,\r\n      bomDetailList: [],\r\n    };\r\n  },\r\n  created() {\r\n    const planCode = this.$route.query.planCode;\r\n    if (planCode) {\r\n      this.isEdit = true;\r\n      this.title = \"修改生产计划\";\r\n      this.getPlanData(planCode);\r\n    } else {\r\n      this.isEdit = false;\r\n      this.title = \"新增生产计划\";\r\n      this.generatePlanCode();\r\n    }\r\n  },\r\n  methods: {\r\n    // 获取计划数据\r\n    getPlanData(planCode) {\r\n      getProductionPlan(planCode).then(response => {\r\n        this.form = response.data;\r\n\r\n        // 如果是生产订单来源，获取订单数量信息\r\n        if (this.form.sourceType === 'PRODUCTION_ORDER' && this.form.productionOrderId) {\r\n          this.getOrderQtyInfo(this.form.productionOrderId, this.form.productId);\r\n        }\r\n\r\n        // 如果有关联的产品，则自动加载其BOM信息\r\n        if (this.form.productId) {\r\n          this.loadAssociatedBom();\r\n        }\r\n      });\r\n    },\r\n\r\n    // 获取订单数量信息\r\n    getOrderQtyInfo(productionOrderId, productId) {\r\n      import(\"@/api/sc/productionOrder\").then(api => {\r\n        api.getProductionOrderDetails(productionOrderId).then(response => {\r\n          if (response.code === 200 && response.data && response.data.products) {\r\n            const product = response.data.products.find(p => p.productId === productId);\r\n            if (product) {\r\n              this.form.orderQty = product.qtyNum || 0;\r\n            }\r\n          }\r\n        }).catch(() => {\r\n          console.error('获取订单数量信息失败');\r\n        });\r\n      });\r\n    },\r\n\r\n    // 加载关联的BOM\r\n    loadAssociatedBom() {\r\n      listBomsByProductId(this.form.productId).then(response => {\r\n        if (response.code === 200 && response.rows && response.rows.length > 0) {\r\n          // 查找默认的BOM (status '1')\r\n          const activeBom = response.rows.find(b => b.bom_status === '1');\r\n          if (activeBom) {\r\n            this.selectedBom = activeBom;\r\n            this.getBomDetail(); // 加载BOM详情\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    // 生成计划编号\r\n    generatePlanCode() {\r\n      getAutoNumbers(6).then(response => {\r\n        if (response.code === 200) {\r\n          this.form.planCode = response.msg;\r\n        } else {\r\n          this.$message.error('获取计划编号失败');\r\n        }\r\n      }).catch(() => {\r\n        this.$message.error('获取计划编号失败');\r\n      });\r\n    },\r\n    \r\n    // 处理系统编号开关变化\r\n    handleSystemCodeChange(val) {\r\n      if (val) {\r\n        // 如果开启系统编号，则生成编号\r\n        this.generatePlanCode();\r\n      } else {\r\n        // 如果关闭系统编号，则清空编号\r\n        this.form.planCode = '';\r\n      }\r\n    },\r\n    \r\n    // 触发上传\r\n    triggerUpload() {\r\n      this.$refs.upload.$el.click();\r\n    },\r\n    \r\n    // 打开产品选择弹窗\r\n    openProductSelection() {\r\n      // 修改模式下不允许更换产品\r\n      if (this.isEdit) {\r\n        this.$message.warning(\"修改模式下不允许更换产品。\");\r\n        return;\r\n      }\r\n      this.productDialogVisible = true;\r\n      this.getProductList();\r\n    },\r\n    \r\n    // 获取产品列表\r\n    getProductList() {\r\n      this.productLoading = true;\r\n      listProducts({\r\n        pageNum: this.productQuery.pageNum,\r\n        pageSize: this.productQuery.pageSize,\r\n        keyword: this.productQuery.keyword,\r\n        productUnit: this.productQuery.unit,\r\n        productType: this.productQuery.type,\r\n        productProperty: this.productQuery.property\r\n      }).then(response => {\r\n        this.productLoading = false;\r\n        if (response.code === 200) {\r\n          this.productList = response.rows;\r\n          this.productTotal = response.total;\r\n        }\r\n      }).catch(() => {\r\n        this.productLoading = false;\r\n        // 模拟数据\r\n        this.productList = [\r\n          { product_id: 1, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' },\r\n          { product_id: 2, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' },\r\n          { product_id: 3, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' },\r\n          { product_id: 4, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' },\r\n          { product_id: 5, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' }\r\n        ];\r\n        this.productTotal = 50;\r\n      });\r\n    },\r\n    \r\n    // 搜索产品\r\n    searchProducts() {\r\n      this.productQuery.pageNum = 1;\r\n      this.getProductList();\r\n    },\r\n    \r\n    // 重置产品查询条件\r\n    resetProductQuery() {\r\n      this.productQuery = {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        keyword: \"\",\r\n        unit: \"\",\r\n        type: \"\",\r\n        property: \"\"\r\n      };\r\n      this.getProductList();\r\n    },\r\n    \r\n    // 处理产品表格选择变化\r\n    handleProductSelectionChange(selection) {\r\n      if (selection.length > 0) {\r\n        this.selectedProduct = selection[0];\r\n      } else {\r\n        this.selectedProduct = null;\r\n      }\r\n    },\r\n    \r\n    // 处理产品页码变化\r\n    handleProductCurrentChange(currentPage) {\r\n      this.productQuery.pageNum = currentPage;\r\n      this.getProductList();\r\n    },\r\n    \r\n    // 处理产品每页条数变化\r\n    handleProductSizeChange(size) {\r\n      this.productQuery.pageSize = size;\r\n      this.productQuery.pageNum = 1;\r\n      this.getProductList();\r\n    },\r\n    \r\n    // 确认产品选择\r\n    confirmProductSelect() {\r\n      if (this.selectedProduct) {\r\n        this.form.productId = this.selectedProduct.product_id;\r\n        this.form.productName = this.selectedProduct.product_name;\r\n        this.form.productCode = this.selectedProduct.product_code;\r\n        this.form.specification = this.selectedProduct.product_sfn;\r\n        this.form.productType = this.formatProductType(this.selectedProduct);\r\n        this.form.unit = this.selectedProduct.product_unit;\r\n        this.productDialogVisible = false;\r\n        \r\n        // 清空已选BOM\r\n        this.selectedBom = null;\r\n        this.selectedBomId = null;\r\n      } else {\r\n        this.$message.warning('请选择一个产品！');\r\n      }\r\n    },\r\n    \r\n    // 格式化产品类型\r\n    formatProductType(row, column) {\r\n      const type = row.product_type;\r\n      const option = this.dict.type.product_type.find(item => item.dictValue == type);\r\n      return option ? option.dictLabel : type;\r\n    },\r\n    \r\n    // 选择BOM\r\n    selectBom() {\r\n      if (!this.form.productId) {\r\n        this.$message.warning('请先选择成品！');\r\n        return;\r\n      }\r\n      this.bomDialogVisible = true;\r\n      this.getBomList();\r\n    },\r\n    \r\n    // 获取BOM列表\r\n    getBomList() {\r\n      this.bomLoading = true;\r\n      listBomsByProductId(this.form.productId).then(response => {\r\n        this.bomLoading = false;\r\n        if (response.code === 200) {\r\n          this.bomList = response.rows;\r\n          this.bomTotal = response.total;\r\n          if (!this.bomList || this.bomList.length === 0) {\r\n            this.$message.info(\"未找到该产品的BOM信息\");\r\n          } else {\r\n            // 如果有默认BOM，则自动选中\r\n            const defaultBom = this.bomList.find(b => b.bom_status === '1');\r\n            if (defaultBom) {\r\n              this.handleBomSelect(defaultBom);\r\n            }\r\n          }\r\n        } else {\r\n          this.bomList = [];\r\n          this.bomTotal = 0;\r\n        }\r\n      }).catch(() => {\r\n        this.bomLoading = false;\r\n        this.$message.error('获取BOM列表失败');\r\n      });\r\n    },\r\n    \r\n    // 处理BOM行选择\r\n    handleBomSelect(row) {\r\n      this.selectedBom = row;\r\n      this.selectedBomId = row.bom_id;\r\n    },\r\n    \r\n    // 确认BOM选择\r\n    confirmBomSelect() {\r\n      if (this.selectedBom) {\r\n        this.bomDialogVisible = false;\r\n        // 获取BOM详情\r\n        this.getBomDetail();\r\n      } else {\r\n        this.$message.warning('请选择一个BOM！');\r\n      }\r\n    },\r\n    \r\n    // 获取BOM详情\r\n    getBomDetail() {\r\n      findBomDetails(this.selectedBom.bom_id).then(response => {\r\n        console.log(\"成功获取BOM详情响应:\", response);\r\n        if (response && response.code === 200) {\r\n          this.bomDetailList = response.rows;\r\n        } else {\r\n          this.bomDetailList = [];\r\n          this.$message.error(\"获取BOM详情失败: \" + (response ? response.msg : '无响应'));\r\n        }\r\n      }).catch(error => {\r\n        console.error(\"获取BOM详情接口调用失败:\", error);\r\n        this.$message.error(\"获取BOM详情接口调用失败\");\r\n        this.bomDetailList = [];\r\n      });\r\n    },\r\n    \r\n    // 清除已选BOM\r\n    clearSelectedBom() {\r\n      this.selectedBom = null;\r\n      this.selectedBomId = null;\r\n      this.bomDetailList = [];\r\n    },\r\n    \r\n    // 上传前检查文件类型和大小\r\n    beforeUpload(file) {\r\n      const isValidType = /\\.(doc|docx|xls|xlsx|pdf|rar|zip|png|jpg|jpeg)$/i.test(file.name);\r\n      const isLt10M = file.size / 1024 / 1024 < 10;\r\n\r\n      if (!isValidType) {\r\n        this.$message.error('上传文件格式不支持!');\r\n        return false;\r\n      }\r\n      if (!isLt10M) {\r\n        this.$message.error('上传文件大小不能超过 10MB!');\r\n        return false;\r\n      }\r\n      return true;\r\n    },\r\n    \r\n    // 上传文件处理\r\n    uploadFile(options) {\r\n      // 这里应该调用实际的文件上传API\r\n      console.log('文件上传:', options.file);\r\n      // 假设上传成功\r\n      this.fileList.push({\r\n        name: options.file.name,\r\n        url: URL.createObjectURL(options.file)\r\n      });\r\n      options.onSuccess();\r\n    },\r\n    \r\n    // 移除文件\r\n    handleRemove(file) {\r\n      const index = this.fileList.indexOf(file);\r\n      if (index !== -1) {\r\n        this.fileList.splice(index, 1);\r\n      }\r\n    },\r\n    \r\n    // 表单提交\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          // 额外的日期逻辑验证\r\n          if (!this.validateDateLogic()) {\r\n            return;\r\n          }\r\n\r\n          // 根据isEdit标志决定调用哪个API\r\n          const apiCall = this.isEdit ? updateProductionPlan : addProductionPlan;\r\n          \r\n          apiCall(this.form).then(response => {\r\n            if (response.code === 200) {\r\n              this.$modal.msgSuccess(this.isEdit ? \"修改成功\" : \"新增成功\");\r\n              this.cancel();\r\n            } else {\r\n              this.$modal.msgError(response.msg || (this.isEdit ? \"修改失败\" : \"新增失败\"));\r\n            }\r\n          }).catch(() => {\r\n            this.$modal.msgError(\"操作失败，请稍后重试\");\r\n          });\r\n        }\r\n      });\r\n    },\r\n    \r\n    // 取消按钮\r\n    cancel() {\r\n      this.$router.push({ path: \"/sc/plan\" });\r\n    },\r\n\r\n    // 验证开工时间\r\n    validateStartTime(rule, value, callback) {\r\n      if (!value) {\r\n        callback();\r\n        return;\r\n      }\r\n\r\n      const today = new Date();\r\n      today.setHours(0, 0, 0, 0);\r\n      const startDate = new Date(value);\r\n\r\n      // 开工日期不能早于当前日期\r\n      if (startDate < today) {\r\n        callback(new Error('开工日期不能早于当前日期'));\r\n        return;\r\n      }\r\n\r\n      // 如果完工时间已选择，开工时间不能晚于完工时间\r\n      if (this.form.planEndTime) {\r\n        const endDate = new Date(this.form.planEndTime);\r\n        if (startDate > endDate) {\r\n          callback(new Error('开工日期不能晚于完工日期'));\r\n          return;\r\n        }\r\n      }\r\n\r\n      callback();\r\n    },\r\n\r\n    // 验证完工时间\r\n    validateEndTime(rule, value, callback) {\r\n      if (!value) {\r\n        callback();\r\n        return;\r\n      }\r\n\r\n      const endDate = new Date(value);\r\n\r\n      // 如果开工时间已选择，完工时间不能早于开工时间\r\n      if (this.form.planStartTime) {\r\n        const startDate = new Date(this.form.planStartTime);\r\n        if (endDate < startDate) {\r\n          callback(new Error('完工日期不能早于开工日期'));\r\n          return;\r\n        }\r\n      }\r\n\r\n      callback();\r\n    },\r\n\r\n    // 验证计划数量\r\n    validatePlannedQty(rule, value, callback) {\r\n      if (!value) {\r\n        callback();\r\n        return;\r\n      }\r\n\r\n      const plannedQty = Number(value);\r\n      const orderQty = Number(this.form.orderQty);\r\n\r\n      if (plannedQty <= 0) {\r\n        callback(new Error('计划数量必须大于0'));\r\n        return;\r\n      }\r\n\r\n      if (orderQty > 0 && plannedQty > orderQty) {\r\n        callback(new Error(`计划数量不能大于订单数量(${orderQty})`));\r\n        return;\r\n      }\r\n\r\n      callback();\r\n    },\r\n\r\n    // 验证需求日期\r\n    validateRequiredDate(rule, value, callback) {\r\n      if (!value) {\r\n        callback();\r\n        return;\r\n      }\r\n\r\n      const requiredDate = new Date(value);\r\n\r\n      // 需求日期不能早于当前日期\r\n      const today = new Date();\r\n      today.setHours(0, 0, 0, 0);\r\n      if (requiredDate < today) {\r\n        callback(new Error('需求日期不能早于当前日期'));\r\n        return;\r\n      }\r\n\r\n      // 如果完工时间已选择，需求日期不能早于完工时间\r\n      if (this.form.planEndTime) {\r\n        const endDate = new Date(this.form.planEndTime);\r\n        if (requiredDate < endDate) {\r\n          callback(new Error('需求日期不能早于完工日期'));\r\n          return;\r\n        }\r\n      }\r\n\r\n      callback();\r\n    },\r\n\r\n    // 综合日期逻辑验证\r\n    validateDateLogic() {\r\n      const today = new Date();\r\n      today.setHours(0, 0, 0, 0);\r\n\r\n      // 检查开工日期\r\n      if (this.form.planStartTime) {\r\n        const startDate = new Date(this.form.planStartTime);\r\n        if (startDate < today) {\r\n          this.$modal.msgError(\"开工日期不能早于当前日期\");\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 检查开工日期和完工日期的关系\r\n      if (this.form.planStartTime && this.form.planEndTime) {\r\n        const startDate = new Date(this.form.planStartTime);\r\n        const endDate = new Date(this.form.planEndTime);\r\n        if (startDate > endDate) {\r\n          this.$modal.msgError(\"开工日期不能晚于完工日期\");\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 检查需求日期\r\n      if (this.form.requiredDate) {\r\n        const requiredDate = new Date(this.form.requiredDate);\r\n        if (requiredDate < today) {\r\n          this.$modal.msgError(\"需求日期不能早于当前日期\");\r\n          return false;\r\n        }\r\n\r\n        // 需求日期不能早于完工日期\r\n        if (this.form.planEndTime) {\r\n          const endDate = new Date(this.form.planEndTime);\r\n          if (requiredDate < endDate) {\r\n            this.$modal.msgError(\"需求日期不能早于完工日期\");\r\n            return false;\r\n          }\r\n        }\r\n      }\r\n\r\n      return true;\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.form-container {\r\n  background-color: #fff;\r\n  padding: 10px;\r\n}\r\n\r\n.el-tabs--border-card {\r\n  box-shadow: none;\r\n}\r\n\r\n.upload-container {\r\n  width: 100%;\r\n  border: 1px dashed #d9d9d9;\r\n  border-radius: 6px;\r\n  text-align: center;\r\n  padding: 20px 0;\r\n  cursor: pointer;\r\n}\r\n\r\n.upload-container:hover {\r\n  border-color: #409EFF;\r\n}\r\n\r\n.upload-area {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.upload-area .el-icon-upload {\r\n  font-size: 48px;\r\n  color: #c0c4cc;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.upload-text {\r\n  font-size: 14px;\r\n  color: #606266;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.upload-hint {\r\n  font-size: 12px;\r\n  color: #909399;\r\n}\r\n\r\n.input-with-select .el-input-group__append {\r\n  background-color: #fff;\r\n}\r\n\r\n.bom-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.bom-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin: 20px 0;\r\n  padding: 30px 0;\r\n}\r\n\r\n.folder-icon {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.bom-text {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.warning-text {\r\n  color: #E6A23C;\r\n  font-size: 12px;\r\n  margin-left: 10px;\r\n}\r\n\r\n.warning-text i {\r\n  margin-right: 5px;\r\n}\r\n\r\n.upload-hidden {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.upload-hidden >>> .el-upload {\r\n  width: 100%;\r\n}\r\n\r\n.upload-hidden >>> .el-upload-dragger {\r\n  width: 100%;\r\n  height: 100%;\r\n  border: none;\r\n  padding: 0;\r\n  margin: 0;\r\n}\r\n\r\n.bom-dialog-header {\r\n  margin-bottom: 15px;\r\n  padding: 10px;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n}\r\n\r\n.product-info {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.product-info span {\r\n  margin-right: 20px;\r\n  line-height: 30px;\r\n}\r\n\r\n.el-radio {\r\n  margin-right: 0;\r\n}\r\n\r\n.pagination-container {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 10px 0;\r\n  font-size: 12px;\r\n}\r\n\r\n.pagination-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.page-size {\r\n  margin-right: 10px;\r\n}\r\n\r\n.total-text {\r\n  color: #606266;\r\n  font-size: 12px;\r\n}\r\n\r\n.bom-info {\r\n  width: 100%;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.bom-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n  padding: 8px;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n}\r\n\r\n.bom-title-info {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.bom-title-info span {\r\n  margin-right: 20px;\r\n  font-weight: bold;\r\n}\r\n\r\n.bom-detail-table {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.bom-action {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.select-bom-button {\r\n  margin-right: 10px;\r\n}\r\n</style>\r\n"]}]}