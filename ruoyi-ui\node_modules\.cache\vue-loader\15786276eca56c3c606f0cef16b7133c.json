{"remainingRequest": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\src\\views\\sc\\plan\\edit_plan.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\src\\views\\sc\\plan\\edit_plan.vue", "mtime": 1753786056445}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751945356000}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751945361444}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751945356000}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751945364156}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBnZXRQcm9kdWN0aW9uUGxhbiwgdXBkYXRlUHJvZHVjdGlvblBsYW4sIGFkZFByb2R1Y3Rpb25QbGFuIH0gZnJvbSAiQC9hcGkvc2MvcHJvZHVjdGlvblBsYW4iOw0KaW1wb3J0IHsgbGlzdFByb2R1Y3RzLCBsaXN0Qm9tc0J5UHJvZHVjdElkLCBmaW5kQm9tRGV0YWlscyB9IGZyb20gIkAvYXBpL2Jhc2ljL3Byb2R1Y3QiOw0KaW1wb3J0IHsgZ2V0QXV0b051bWJlcnMgfSBmcm9tICJAL2FwaS9iYXNpYy9udW1iZXJzIjsNCmltcG9ydCBQYWdpbmF0aW9uIGZyb20gIkAvY29tcG9uZW50cy9QYWdpbmF0aW9uIjsNCmltcG9ydCBEaWN0VGFnIGZyb20gIkAvY29tcG9uZW50cy9EaWN0VGFnIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiRWRpdFBsYW4iLA0KICBkaWN0czogWydwcm9kdWN0X3R5cGUnXSwNCiAgY29tcG9uZW50czogew0KICAgIFBhZ2luYXRpb24sDQogICAgRGljdFRhZw0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICAvLyDpobXpnaLmoIfpopgNCiAgICAgIHRpdGxlOiAi5L+u5pS555Sf5Lqn6K6h5YiSIiwNCiAgICAgIC8vIOaYr+WQpuS4uuS/ruaUueaooeW8jw0KICAgICAgaXNFZGl0OiB0cnVlLA0KICAgICAgLy8g6KGo5Y2V5pWw5o2uDQogICAgICBmb3JtOiB7DQogICAgICAgIHBsYW5Db2RlOiAiIiwNCiAgICAgICAgcGxhbk5hbWU6ICIiLA0KICAgICAgICBzb3VyY2VUeXBlOiAi6ZSA5ZSu6K6i5Y2VIiwNCiAgICAgICAgb3JkZXJDb2RlOiAiIiwNCiAgICAgICAgcHJvZHVjdElkOiB1bmRlZmluZWQsDQogICAgICAgIHByb2R1Y3ROYW1lOiAiIiwNCiAgICAgICAgcHJvZHVjdENvZGU6ICIiLA0KICAgICAgICBzcGVjaWZpY2F0aW9uOiAiIiwNCiAgICAgICAgcHJvZHVjdFR5cGU6ICIiLA0KICAgICAgICB1bml0OiAiIiwNCiAgICAgICAgcGxhbm5lZFF0eTogMSwNCiAgICAgICAgcGxhblN0YXJ0VGltZTogIiIsDQogICAgICAgIHBsYW5FbmRUaW1lOiAiIiwNCiAgICAgICAgcmVxdWlyZWREYXRlOiAiIiwNCiAgICAgICAgcmVtYXJrOiAiIg0KICAgICAgfSwNCiAgICAgIC8vIOaYr+WQpuS9v+eUqOezu+e7n+e8luWPtw0KICAgICAgaXNTeXN0ZW1Db2RlOiB0cnVlLA0KICAgICAgLy8g6KGo5Y2V6aqM6K+B6KeE5YiZDQogICAgICBydWxlczogew0KICAgICAgICBwbGFuTmFtZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLorqHliJLlkI3np7DkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgICB7IG1heDogNTAsIG1lc3NhZ2U6ICLplb/luqbkuI3og73otoXov4c1MOS4quWtl+espiIsIHRyaWdnZXI6ICJibHVyIiB9DQogICAgICAgIF0sDQogICAgICAgIHNvdXJjZVR5cGU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5p2l5rqQ57G75Z6L5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImNoYW5nZSIgfQ0KICAgICAgICBdLA0KICAgICAgICBwcm9kdWN0TmFtZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fpgInmi6nkuqflk4EiLCB0cmlnZ2VyOiAiY2hhbmdlIiB9DQogICAgICAgIF0sDQogICAgICAgIHBsYW5uZWRRdHk6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K6h5YiS5pWw6YeP5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0NCiAgICAgICAgXSwNCiAgICAgICAgcGxhblN0YXJ0VGltZTogWw0KICAgICAgICAgIHsgdmFsaWRhdG9yOiB0aGlzLnZhbGlkYXRlU3RhcnRUaW1lLCB0cmlnZ2VyOiAiY2hhbmdlIiB9DQogICAgICAgIF0sDQogICAgICAgIHBsYW5FbmRUaW1lOiBbDQogICAgICAgICAgeyB2YWxpZGF0b3I6IHRoaXMudmFsaWRhdGVFbmRUaW1lLCB0cmlnZ2VyOiAiY2hhbmdlIiB9DQogICAgICAgIF0sDQogICAgICAgIHJlcXVpcmVkRGF0ZTogWw0KICAgICAgICAgIHsgdmFsaWRhdG9yOiB0aGlzLnZhbGlkYXRlUmVxdWlyZWREYXRlLCB0cmlnZ2VyOiAiY2hhbmdlIiB9DQogICAgICAgIF0NCiAgICAgIH0sDQogICAgICAvLyDkuqflk4HkuIvmi4npgInpobkNCiAgICAgIHByb2R1Y3RPcHRpb25zOiBbXSwNCiAgICAgIC8vIOS6p+WTgeWKoOi9veeKtuaAgQ0KICAgICAgcHJvZHVjdExvYWRpbmc6IGZhbHNlLA0KICAgICAgLy8g5p2l5rqQ57G75Z6L6YCJ6aG5DQogICAgICBzb3VyY2VUeXBlT3B0aW9uczogWw0KICAgICAgICB7IGRpY3RMYWJlbDogIumUgOWUruiuouWNlSIsIGRpY3RWYWx1ZTogIumUgOWUruiuouWNlSIgfSwNCiAgICAgICAgeyBkaWN0TGFiZWw6ICLlupPlrZjlpIfotKciLCBkaWN0VmFsdWU6ICLlupPlrZjlpIfotKciIH0NCiAgICAgIF0sDQogICAgICAvLyDkuIrkvKDmlofku7bliJfooagNCiAgICAgIGZpbGVMaXN0OiBbXSwNCiAgICAgIA0KICAgICAgLy8g5Lqn5ZOB6YCJ5oup5a+56K+d5qGGDQogICAgICBwcm9kdWN0RGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICBwcm9kdWN0UXVlcnk6IHsNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDEwLA0KICAgICAgICBrZXl3b3JkOiAiIiwNCiAgICAgICAgdW5pdDogIiIsDQogICAgICAgIHR5cGU6ICIiLA0KICAgICAgICBwcm9wZXJ0eTogIiINCiAgICAgIH0sDQogICAgICBwcm9kdWN0TGlzdDogW10sDQogICAgICBwcm9kdWN0VG90YWw6IDAsDQogICAgICBzZWxlY3RlZFByb2R1Y3Q6IG51bGwsDQogICAgICANCiAgICAgIC8vIEJPTemAieaLqeWvueivneahhg0KICAgICAgYm9tRGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICBib21RdWVyeTogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgIHByb2R1Y3RJZDogdW5kZWZpbmVkDQogICAgICB9LA0KICAgICAgYm9tTGlzdDogW10sDQogICAgICBib21Ub3RhbDogMCwNCiAgICAgIGJvbUxvYWRpbmc6IGZhbHNlLA0KICAgICAgc2VsZWN0ZWRCb206IG51bGwsDQogICAgICBzZWxlY3RlZEJvbUlkOiBudWxsLA0KICAgICAgYm9tRGV0YWlsTGlzdDogW10sDQogICAgfTsNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICBjb25zdCBwbGFuQ29kZSA9IHRoaXMuJHJvdXRlLnF1ZXJ5LnBsYW5Db2RlOw0KICAgIGlmIChwbGFuQ29kZSkgew0KICAgICAgdGhpcy5pc0VkaXQgPSB0cnVlOw0KICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnnlJ/kuqforqHliJIiOw0KICAgICAgdGhpcy5nZXRQbGFuRGF0YShwbGFuQ29kZSk7DQogICAgfSBlbHNlIHsNCiAgICAgIHRoaXMuaXNFZGl0ID0gZmFsc2U7DQogICAgICB0aGlzLnRpdGxlID0gIuaWsOWinueUn+S6p+iuoeWIkiI7DQogICAgICB0aGlzLmdlbmVyYXRlUGxhbkNvZGUoKTsNCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvLyDojrflj5borqHliJLmlbDmja4NCiAgICBnZXRQbGFuRGF0YShwbGFuQ29kZSkgew0KICAgICAgZ2V0UHJvZHVjdGlvblBsYW4ocGxhbkNvZGUpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhOw0KICAgICAgICAvLyDlpoLmnpzmnInlhbPogZTnmoTkuqflk4HvvIzliJnoh6rliqjliqDovb3lhbZCT03kv6Hmga8NCiAgICAgICAgaWYgKHRoaXMuZm9ybS5wcm9kdWN0SWQpIHsNCiAgICAgICAgICB0aGlzLmxvYWRBc3NvY2lhdGVkQm9tKCk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvLyDliqDovb3lhbPogZTnmoRCT00NCiAgICBsb2FkQXNzb2NpYXRlZEJvbSgpIHsNCiAgICAgIGxpc3RCb21zQnlQcm9kdWN0SWQodGhpcy5mb3JtLnByb2R1Y3RJZCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDAgJiYgcmVzcG9uc2Uucm93cyAmJiByZXNwb25zZS5yb3dzLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAvLyDmn6Xmib7pu5jorqTnmoRCT00gKHN0YXR1cyAnMScpDQogICAgICAgICAgY29uc3QgYWN0aXZlQm9tID0gcmVzcG9uc2Uucm93cy5maW5kKGIgPT4gYi5ib21fc3RhdHVzID09PSAnMScpOw0KICAgICAgICAgIGlmIChhY3RpdmVCb20pIHsNCiAgICAgICAgICAgIHRoaXMuc2VsZWN0ZWRCb20gPSBhY3RpdmVCb207DQogICAgICAgICAgICB0aGlzLmdldEJvbURldGFpbCgpOyAvLyDliqDovb1CT03or6bmg4UNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvLyDnlJ/miJDorqHliJLnvJblj7cNCiAgICBnZW5lcmF0ZVBsYW5Db2RlKCkgew0KICAgICAgZ2V0QXV0b051bWJlcnMoNikudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICB0aGlzLmZvcm0ucGxhbkNvZGUgPSByZXNwb25zZS5tc2c7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6I635Y+W6K6h5YiS57yW5Y+35aSx6LSlJyk7DQogICAgICAgIH0NCiAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6I635Y+W6K6h5YiS57yW5Y+35aSx6LSlJyk7DQogICAgICB9KTsNCiAgICB9LA0KICAgIA0KICAgIC8vIOWkhOeQhuezu+e7n+e8luWPt+W8gOWFs+WPmOWMlg0KICAgIGhhbmRsZVN5c3RlbUNvZGVDaGFuZ2UodmFsKSB7DQogICAgICBpZiAodmFsKSB7DQogICAgICAgIC8vIOWmguaenOW8gOWQr+ezu+e7n+e8luWPt++8jOWImeeUn+aIkOe8luWPtw0KICAgICAgICB0aGlzLmdlbmVyYXRlUGxhbkNvZGUoKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIC8vIOWmguaenOWFs+mXreezu+e7n+e8luWPt++8jOWImea4heepuue8luWPtw0KICAgICAgICB0aGlzLmZvcm0ucGxhbkNvZGUgPSAnJzsNCiAgICAgIH0NCiAgICB9LA0KICAgIA0KICAgIC8vIOinpuWPkeS4iuS8oA0KICAgIHRyaWdnZXJVcGxvYWQoKSB7DQogICAgICB0aGlzLiRyZWZzLnVwbG9hZC4kZWwuY2xpY2soKTsNCiAgICB9LA0KICAgIA0KICAgIC8vIOaJk+W8gOS6p+WTgemAieaLqeW8ueeqlw0KICAgIG9wZW5Qcm9kdWN0U2VsZWN0aW9uKCkgew0KICAgICAgLy8g5L+u5pS55qih5byP5LiL5LiN5YWB6K645pu05o2i5Lqn5ZOBDQogICAgICBpZiAodGhpcy5pc0VkaXQpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLkv67mlLnmqKHlvI/kuIvkuI3lhYHorrjmm7TmjaLkuqflk4HjgIIiKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgdGhpcy5wcm9kdWN0RGlhbG9nVmlzaWJsZSA9IHRydWU7DQogICAgICB0aGlzLmdldFByb2R1Y3RMaXN0KCk7DQogICAgfSwNCiAgICANCiAgICAvLyDojrflj5bkuqflk4HliJfooagNCiAgICBnZXRQcm9kdWN0TGlzdCgpIHsNCiAgICAgIHRoaXMucHJvZHVjdExvYWRpbmcgPSB0cnVlOw0KICAgICAgbGlzdFByb2R1Y3RzKHsNCiAgICAgICAgcGFnZU51bTogdGhpcy5wcm9kdWN0UXVlcnkucGFnZU51bSwNCiAgICAgICAgcGFnZVNpemU6IHRoaXMucHJvZHVjdFF1ZXJ5LnBhZ2VTaXplLA0KICAgICAgICBrZXl3b3JkOiB0aGlzLnByb2R1Y3RRdWVyeS5rZXl3b3JkLA0KICAgICAgICBwcm9kdWN0VW5pdDogdGhpcy5wcm9kdWN0UXVlcnkudW5pdCwNCiAgICAgICAgcHJvZHVjdFR5cGU6IHRoaXMucHJvZHVjdFF1ZXJ5LnR5cGUsDQogICAgICAgIHByb2R1Y3RQcm9wZXJ0eTogdGhpcy5wcm9kdWN0UXVlcnkucHJvcGVydHkNCiAgICAgIH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLnByb2R1Y3RMb2FkaW5nID0gZmFsc2U7DQogICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICB0aGlzLnByb2R1Y3RMaXN0ID0gcmVzcG9uc2Uucm93czsNCiAgICAgICAgICB0aGlzLnByb2R1Y3RUb3RhbCA9IHJlc3BvbnNlLnRvdGFsOw0KICAgICAgICB9DQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgIHRoaXMucHJvZHVjdExvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgLy8g5qih5ouf5pWw5o2uDQogICAgICAgIHRoaXMucHJvZHVjdExpc3QgPSBbDQogICAgICAgICAgeyBwcm9kdWN0X2lkOiAxLCBwcm9kdWN0X2NvZGU6ICdDUDAyNScsIHByb2R1Y3RfbmFtZTogJ+S6p+WTgeS4gCcsIHByb2R1Y3Rfc2ZuOiAn6JOd6ImyJywgcHJvZHVjdF90eXBlOiAn5oiQ5ZOBJywgcHJvZHVjdF91bml0OiAn5LiqJywgcHJvZHVjdF9wcm9wZXJ0eTogJ+iHquWIticgfSwNCiAgICAgICAgICB7IHByb2R1Y3RfaWQ6IDIsIHByb2R1Y3RfY29kZTogJ0NQMDI1JywgcHJvZHVjdF9uYW1lOiAn5Lqn5ZOB5LiAJywgcHJvZHVjdF9zZm46ICfok53oibInLCBwcm9kdWN0X3R5cGU6ICfmiJDlk4EnLCBwcm9kdWN0X3VuaXQ6ICfkuKonLCBwcm9kdWN0X3Byb3BlcnR5OiAn6Ieq5Yi2JyB9LA0KICAgICAgICAgIHsgcHJvZHVjdF9pZDogMywgcHJvZHVjdF9jb2RlOiAnQ1AwMjUnLCBwcm9kdWN0X25hbWU6ICfkuqflk4HkuIAnLCBwcm9kdWN0X3NmbjogJ+iTneiJsicsIHByb2R1Y3RfdHlwZTogJ+aIkOWTgScsIHByb2R1Y3RfdW5pdDogJ+S4qicsIHByb2R1Y3RfcHJvcGVydHk6ICfoh6rliLYnIH0sDQogICAgICAgICAgeyBwcm9kdWN0X2lkOiA0LCBwcm9kdWN0X2NvZGU6ICdDUDAyNScsIHByb2R1Y3RfbmFtZTogJ+S6p+WTgeS4gCcsIHByb2R1Y3Rfc2ZuOiAn6JOd6ImyJywgcHJvZHVjdF90eXBlOiAn5oiQ5ZOBJywgcHJvZHVjdF91bml0OiAn5LiqJywgcHJvZHVjdF9wcm9wZXJ0eTogJ+iHquWIticgfSwNCiAgICAgICAgICB7IHByb2R1Y3RfaWQ6IDUsIHByb2R1Y3RfY29kZTogJ0NQMDI1JywgcHJvZHVjdF9uYW1lOiAn5Lqn5ZOB5LiAJywgcHJvZHVjdF9zZm46ICfok53oibInLCBwcm9kdWN0X3R5cGU6ICfmiJDlk4EnLCBwcm9kdWN0X3VuaXQ6ICfkuKonLCBwcm9kdWN0X3Byb3BlcnR5OiAn6Ieq5Yi2JyB9DQogICAgICAgIF07DQogICAgICAgIHRoaXMucHJvZHVjdFRvdGFsID0gNTA7DQogICAgICB9KTsNCiAgICB9LA0KICAgIA0KICAgIC8vIOaQnOe0ouS6p+WTgQ0KICAgIHNlYXJjaFByb2R1Y3RzKCkgew0KICAgICAgdGhpcy5wcm9kdWN0UXVlcnkucGFnZU51bSA9IDE7DQogICAgICB0aGlzLmdldFByb2R1Y3RMaXN0KCk7DQogICAgfSwNCiAgICANCiAgICAvLyDph43nva7kuqflk4Hmn6Xor6LmnaHku7YNCiAgICByZXNldFByb2R1Y3RRdWVyeSgpIHsNCiAgICAgIHRoaXMucHJvZHVjdFF1ZXJ5ID0gew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgIGtleXdvcmQ6ICIiLA0KICAgICAgICB1bml0OiAiIiwNCiAgICAgICAgdHlwZTogIiIsDQogICAgICAgIHByb3BlcnR5OiAiIg0KICAgICAgfTsNCiAgICAgIHRoaXMuZ2V0UHJvZHVjdExpc3QoKTsNCiAgICB9LA0KICAgIA0KICAgIC8vIOWkhOeQhuS6p+WTgeihqOagvOmAieaLqeWPmOWMlg0KICAgIGhhbmRsZVByb2R1Y3RTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7DQogICAgICBpZiAoc2VsZWN0aW9uLmxlbmd0aCA+IDApIHsNCiAgICAgICAgdGhpcy5zZWxlY3RlZFByb2R1Y3QgPSBzZWxlY3Rpb25bMF07DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLnNlbGVjdGVkUHJvZHVjdCA9IG51bGw7DQogICAgICB9DQogICAgfSwNCiAgICANCiAgICAvLyDlpITnkIbkuqflk4HpobXnoIHlj5jljJYNCiAgICBoYW5kbGVQcm9kdWN0Q3VycmVudENoYW5nZShjdXJyZW50UGFnZSkgew0KICAgICAgdGhpcy5wcm9kdWN0UXVlcnkucGFnZU51bSA9IGN1cnJlbnRQYWdlOw0KICAgICAgdGhpcy5nZXRQcm9kdWN0TGlzdCgpOw0KICAgIH0sDQogICAgDQogICAgLy8g5aSE55CG5Lqn5ZOB5q+P6aG15p2h5pWw5Y+Y5YyWDQogICAgaGFuZGxlUHJvZHVjdFNpemVDaGFuZ2Uoc2l6ZSkgew0KICAgICAgdGhpcy5wcm9kdWN0UXVlcnkucGFnZVNpemUgPSBzaXplOw0KICAgICAgdGhpcy5wcm9kdWN0UXVlcnkucGFnZU51bSA9IDE7DQogICAgICB0aGlzLmdldFByb2R1Y3RMaXN0KCk7DQogICAgfSwNCiAgICANCiAgICAvLyDnoa7orqTkuqflk4HpgInmi6kNCiAgICBjb25maXJtUHJvZHVjdFNlbGVjdCgpIHsNCiAgICAgIGlmICh0aGlzLnNlbGVjdGVkUHJvZHVjdCkgew0KICAgICAgICB0aGlzLmZvcm0ucHJvZHVjdElkID0gdGhpcy5zZWxlY3RlZFByb2R1Y3QucHJvZHVjdF9pZDsNCiAgICAgICAgdGhpcy5mb3JtLnByb2R1Y3ROYW1lID0gdGhpcy5zZWxlY3RlZFByb2R1Y3QucHJvZHVjdF9uYW1lOw0KICAgICAgICB0aGlzLmZvcm0ucHJvZHVjdENvZGUgPSB0aGlzLnNlbGVjdGVkUHJvZHVjdC5wcm9kdWN0X2NvZGU7DQogICAgICAgIHRoaXMuZm9ybS5zcGVjaWZpY2F0aW9uID0gdGhpcy5zZWxlY3RlZFByb2R1Y3QucHJvZHVjdF9zZm47DQogICAgICAgIHRoaXMuZm9ybS5wcm9kdWN0VHlwZSA9IHRoaXMuZm9ybWF0UHJvZHVjdFR5cGUodGhpcy5zZWxlY3RlZFByb2R1Y3QpOw0KICAgICAgICB0aGlzLmZvcm0udW5pdCA9IHRoaXMuc2VsZWN0ZWRQcm9kdWN0LnByb2R1Y3RfdW5pdDsNCiAgICAgICAgdGhpcy5wcm9kdWN0RGlhbG9nVmlzaWJsZSA9IGZhbHNlOw0KICAgICAgICANCiAgICAgICAgLy8g5riF56m65bey6YCJQk9NDQogICAgICAgIHRoaXMuc2VsZWN0ZWRCb20gPSBudWxsOw0KICAgICAgICB0aGlzLnNlbGVjdGVkQm9tSWQgPSBudWxsOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7fpgInmi6nkuIDkuKrkuqflk4HvvIEnKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIA0KICAgIC8vIOagvOW8j+WMluS6p+WTgeexu+Weiw0KICAgIGZvcm1hdFByb2R1Y3RUeXBlKHJvdywgY29sdW1uKSB7DQogICAgICBjb25zdCB0eXBlID0gcm93LnByb2R1Y3RfdHlwZTsNCiAgICAgIGNvbnN0IG9wdGlvbiA9IHRoaXMuZGljdC50eXBlLnByb2R1Y3RfdHlwZS5maW5kKGl0ZW0gPT4gaXRlbS5kaWN0VmFsdWUgPT0gdHlwZSk7DQogICAgICByZXR1cm4gb3B0aW9uID8gb3B0aW9uLmRpY3RMYWJlbCA6IHR5cGU7DQogICAgfSwNCiAgICANCiAgICAvLyDpgInmi6lCT00NCiAgICBzZWxlY3RCb20oKSB7DQogICAgICBpZiAoIXRoaXMuZm9ybS5wcm9kdWN0SWQpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7flhYjpgInmi6nmiJDlk4HvvIEnKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgdGhpcy5ib21EaWFsb2dWaXNpYmxlID0gdHJ1ZTsNCiAgICAgIHRoaXMuZ2V0Qm9tTGlzdCgpOw0KICAgIH0sDQogICAgDQogICAgLy8g6I635Y+WQk9N5YiX6KGoDQogICAgZ2V0Qm9tTGlzdCgpIHsNCiAgICAgIHRoaXMuYm9tTG9hZGluZyA9IHRydWU7DQogICAgICBsaXN0Qm9tc0J5UHJvZHVjdElkKHRoaXMuZm9ybS5wcm9kdWN0SWQpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmJvbUxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIHRoaXMuYm9tTGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgICAgdGhpcy5ib21Ub3RhbCA9IHJlc3BvbnNlLnRvdGFsOw0KICAgICAgICAgIGlmICghdGhpcy5ib21MaXN0IHx8IHRoaXMuYm9tTGlzdC5sZW5ndGggPT09IDApIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuaW5mbygi5pyq5om+5Yiw6K+l5Lqn5ZOB55qEQk9N5L+h5oGvIik7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIC8vIOWmguaenOaciem7mOiupEJPTe+8jOWImeiHquWKqOmAieS4rQ0KICAgICAgICAgICAgY29uc3QgZGVmYXVsdEJvbSA9IHRoaXMuYm9tTGlzdC5maW5kKGIgPT4gYi5ib21fc3RhdHVzID09PSAnMScpOw0KICAgICAgICAgICAgaWYgKGRlZmF1bHRCb20pIHsNCiAgICAgICAgICAgICAgdGhpcy5oYW5kbGVCb21TZWxlY3QoZGVmYXVsdEJvbSk7DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuYm9tTGlzdCA9IFtdOw0KICAgICAgICAgIHRoaXMuYm9tVG90YWwgPSAwOw0KICAgICAgICB9DQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgIHRoaXMuYm9tTG9hZGluZyA9IGZhbHNlOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfojrflj5ZCT03liJfooajlpLHotKUnKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgDQogICAgLy8g5aSE55CGQk9N6KGM6YCJ5oupDQogICAgaGFuZGxlQm9tU2VsZWN0KHJvdykgew0KICAgICAgdGhpcy5zZWxlY3RlZEJvbSA9IHJvdzsNCiAgICAgIHRoaXMuc2VsZWN0ZWRCb21JZCA9IHJvdy5ib21faWQ7DQogICAgfSwNCiAgICANCiAgICAvLyDnoa7orqRCT03pgInmi6kNCiAgICBjb25maXJtQm9tU2VsZWN0KCkgew0KICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRCb20pIHsNCiAgICAgICAgdGhpcy5ib21EaWFsb2dWaXNpYmxlID0gZmFsc2U7DQogICAgICAgIC8vIOiOt+WPlkJPTeivpuaDhQ0KICAgICAgICB0aGlzLmdldEJvbURldGFpbCgpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7fpgInmi6nkuIDkuKpCT03vvIEnKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIA0KICAgIC8vIOiOt+WPlkJPTeivpuaDhQ0KICAgIGdldEJvbURldGFpbCgpIHsNCiAgICAgIGZpbmRCb21EZXRhaWxzKHRoaXMuc2VsZWN0ZWRCb20uYm9tX2lkKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgY29uc29sZS5sb2coIuaIkOWKn+iOt+WPlkJPTeivpuaDheWTjeW6lDoiLCByZXNwb25zZSk7DQogICAgICAgIGlmIChyZXNwb25zZSAmJiByZXNwb25zZS5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICB0aGlzLmJvbURldGFpbExpc3QgPSByZXNwb25zZS5yb3dzOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuYm9tRGV0YWlsTGlzdCA9IFtdOw0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuiOt+WPlkJPTeivpuaDheWksei0pTogIiArIChyZXNwb25zZSA/IHJlc3BvbnNlLm1zZyA6ICfml6Dlk43lupQnKSk7DQogICAgICAgIH0NCiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgY29uc29sZS5lcnJvcigi6I635Y+WQk9N6K+m5oOF5o6l5Y+j6LCD55So5aSx6LSlOiIsIGVycm9yKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi6I635Y+WQk9N6K+m5oOF5o6l5Y+j6LCD55So5aSx6LSlIik7DQogICAgICAgIHRoaXMuYm9tRGV0YWlsTGlzdCA9IFtdOw0KICAgICAgfSk7DQogICAgfSwNCiAgICANCiAgICAvLyDmuIXpmaTlt7LpgIlCT00NCiAgICBjbGVhclNlbGVjdGVkQm9tKCkgew0KICAgICAgdGhpcy5zZWxlY3RlZEJvbSA9IG51bGw7DQogICAgICB0aGlzLnNlbGVjdGVkQm9tSWQgPSBudWxsOw0KICAgICAgdGhpcy5ib21EZXRhaWxMaXN0ID0gW107DQogICAgfSwNCiAgICANCiAgICAvLyDkuIrkvKDliY3mo4Dmn6Xmlofku7bnsbvlnovlkozlpKflsI8NCiAgICBiZWZvcmVVcGxvYWQoZmlsZSkgew0KICAgICAgY29uc3QgaXNWYWxpZFR5cGUgPSAvXC4oZG9jfGRvY3h8eGxzfHhsc3h8cGRmfHJhcnx6aXB8cG5nfGpwZ3xqcGVnKSQvaS50ZXN0KGZpbGUubmFtZSk7DQogICAgICBjb25zdCBpc0x0MTBNID0gZmlsZS5zaXplIC8gMTAyNCAvIDEwMjQgPCAxMDsNCg0KICAgICAgaWYgKCFpc1ZhbGlkVHlwZSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfkuIrkvKDmlofku7bmoLzlvI/kuI3mlK/mjIEhJyk7DQogICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgIH0NCiAgICAgIGlmICghaXNMdDEwTSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfkuIrkvKDmlofku7blpKflsI/kuI3og73otoXov4cgMTBNQiEnKTsNCiAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgfQ0KICAgICAgcmV0dXJuIHRydWU7DQogICAgfSwNCiAgICANCiAgICAvLyDkuIrkvKDmlofku7blpITnkIYNCiAgICB1cGxvYWRGaWxlKG9wdGlvbnMpIHsNCiAgICAgIC8vIOi/memHjOW6lOivpeiwg+eUqOWunumZheeahOaWh+S7tuS4iuS8oEFQSQ0KICAgICAgY29uc29sZS5sb2coJ+aWh+S7tuS4iuS8oDonLCBvcHRpb25zLmZpbGUpOw0KICAgICAgLy8g5YGH6K6+5LiK5Lyg5oiQ5YqfDQogICAgICB0aGlzLmZpbGVMaXN0LnB1c2goew0KICAgICAgICBuYW1lOiBvcHRpb25zLmZpbGUubmFtZSwNCiAgICAgICAgdXJsOiBVUkwuY3JlYXRlT2JqZWN0VVJMKG9wdGlvbnMuZmlsZSkNCiAgICAgIH0pOw0KICAgICAgb3B0aW9ucy5vblN1Y2Nlc3MoKTsNCiAgICB9LA0KICAgIA0KICAgIC8vIOenu+mZpOaWh+S7tg0KICAgIGhhbmRsZVJlbW92ZShmaWxlKSB7DQogICAgICBjb25zdCBpbmRleCA9IHRoaXMuZmlsZUxpc3QuaW5kZXhPZihmaWxlKTsNCiAgICAgIGlmIChpbmRleCAhPT0gLTEpIHsNCiAgICAgICAgdGhpcy5maWxlTGlzdC5zcGxpY2UoaW5kZXgsIDEpOw0KICAgICAgfQ0KICAgIH0sDQogICAgDQogICAgLy8g6KGo5Y2V5o+Q5LqkDQogICAgc3VibWl0Rm9ybSgpIHsNCiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIC8vIOmineWklueahOaXpeacn+mAu+i+kemqjOivgQ0KICAgICAgICAgIGlmICghdGhpcy52YWxpZGF0ZURhdGVMb2dpYygpKSB7DQogICAgICAgICAgICByZXR1cm47DQogICAgICAgICAgfQ0KDQogICAgICAgICAgLy8g5qC55o2uaXNFZGl05qCH5b+X5Yaz5a6a6LCD55So5ZOq5LiqQVBJDQogICAgICAgICAgY29uc3QgYXBpQ2FsbCA9IHRoaXMuaXNFZGl0ID8gdXBkYXRlUHJvZHVjdGlvblBsYW4gOiBhZGRQcm9kdWN0aW9uUGxhbjsNCiAgICAgICAgICANCiAgICAgICAgICBhcGlDYWxsKHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3ModGhpcy5pc0VkaXQgPyAi5L+u5pS55oiQ5YqfIiA6ICLmlrDlop7miJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy5jYW5jZWwoKTsNCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKHJlc3BvbnNlLm1zZyB8fCAodGhpcy5pc0VkaXQgPyAi5L+u5pS55aSx6LSlIiA6ICLmlrDlop7lpLHotKUiKSk7DQogICAgICAgICAgICB9DQogICAgICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuaTjeS9nOWksei0pe+8jOivt+eojeWQjumHjeivlSIpOw0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIA0KICAgIC8vIOWPlua2iOaMiemSrg0KICAgIGNhbmNlbCgpIHsNCiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsgcGF0aDogIi9zYy9wbGFuIiB9KTsNCiAgICB9LA0KDQogICAgLy8g6aqM6K+B5byA5bel5pe26Ze0DQogICAgdmFsaWRhdGVTdGFydFRpbWUocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSB7DQogICAgICBpZiAoIXZhbHVlKSB7DQogICAgICAgIGNhbGxiYWNrKCk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgY29uc3QgdG9kYXkgPSBuZXcgRGF0ZSgpOw0KICAgICAgdG9kYXkuc2V0SG91cnMoMCwgMCwgMCwgMCk7DQogICAgICBjb25zdCBzdGFydERhdGUgPSBuZXcgRGF0ZSh2YWx1ZSk7DQoNCiAgICAgIC8vIOW8gOW3peaXpeacn+S4jeiDveaXqeS6juW9k+WJjeaXpeacnw0KICAgICAgaWYgKHN0YXJ0RGF0ZSA8IHRvZGF5KSB7DQogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcign5byA5bel5pel5pyf5LiN6IO95pep5LqO5b2T5YmN5pel5pyfJykpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIC8vIOWmguaenOWujOW3peaXtumXtOW3sumAieaLqe+8jOW8gOW3peaXtumXtOS4jeiDveaZmuS6juWujOW3peaXtumXtA0KICAgICAgaWYgKHRoaXMuZm9ybS5wbGFuRW5kVGltZSkgew0KICAgICAgICBjb25zdCBlbmREYXRlID0gbmV3IERhdGUodGhpcy5mb3JtLnBsYW5FbmRUaW1lKTsNCiAgICAgICAgaWYgKHN0YXJ0RGF0ZSA+IGVuZERhdGUpIHsNCiAgICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoJ+W8gOW3peaXpeacn+S4jeiDveaZmuS6juWujOW3peaXpeacnycpKTsNCiAgICAgICAgICByZXR1cm47DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgY2FsbGJhY2soKTsNCiAgICB9LA0KDQogICAgLy8g6aqM6K+B5a6M5bel5pe26Ze0DQogICAgdmFsaWRhdGVFbmRUaW1lKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgew0KICAgICAgaWYgKCF2YWx1ZSkgew0KICAgICAgICBjYWxsYmFjaygpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIGNvbnN0IGVuZERhdGUgPSBuZXcgRGF0ZSh2YWx1ZSk7DQoNCiAgICAgIC8vIOWmguaenOW8gOW3peaXtumXtOW3sumAieaLqe+8jOWujOW3peaXtumXtOS4jeiDveaXqeS6juW8gOW3peaXtumXtA0KICAgICAgaWYgKHRoaXMuZm9ybS5wbGFuU3RhcnRUaW1lKSB7DQogICAgICAgIGNvbnN0IHN0YXJ0RGF0ZSA9IG5ldyBEYXRlKHRoaXMuZm9ybS5wbGFuU3RhcnRUaW1lKTsNCiAgICAgICAgaWYgKGVuZERhdGUgPCBzdGFydERhdGUpIHsNCiAgICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoJ+WujOW3peaXpeacn+S4jeiDveaXqeS6juW8gOW3peaXpeacnycpKTsNCiAgICAgICAgICByZXR1cm47DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgY2FsbGJhY2soKTsNCiAgICB9LA0KDQogICAgLy8g6aqM6K+B6ZyA5rGC5pel5pyfDQogICAgdmFsaWRhdGVSZXF1aXJlZERhdGUocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSB7DQogICAgICBpZiAoIXZhbHVlKSB7DQogICAgICAgIGNhbGxiYWNrKCk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgY29uc3QgcmVxdWlyZWREYXRlID0gbmV3IERhdGUodmFsdWUpOw0KDQogICAgICAvLyDpnIDmsYLml6XmnJ/kuI3og73ml6nkuo7lvZPliY3ml6XmnJ8NCiAgICAgIGNvbnN0IHRvZGF5ID0gbmV3IERhdGUoKTsNCiAgICAgIHRvZGF5LnNldEhvdXJzKDAsIDAsIDAsIDApOw0KICAgICAgaWYgKHJlcXVpcmVkRGF0ZSA8IHRvZGF5KSB7DQogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcign6ZyA5rGC5pel5pyf5LiN6IO95pep5LqO5b2T5YmN5pel5pyfJykpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIC8vIOWmguaenOWujOW3peaXtumXtOW3sumAieaLqe+8jOmcgOaxguaXpeacn+S4jeiDveaXqeS6juWujOW3peaXtumXtA0KICAgICAgaWYgKHRoaXMuZm9ybS5wbGFuRW5kVGltZSkgew0KICAgICAgICBjb25zdCBlbmREYXRlID0gbmV3IERhdGUodGhpcy5mb3JtLnBsYW5FbmRUaW1lKTsNCiAgICAgICAgaWYgKHJlcXVpcmVkRGF0ZSA8IGVuZERhdGUpIHsNCiAgICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoJ+mcgOaxguaXpeacn+S4jeiDveaXqeS6juWujOW3peaXpeacnycpKTsNCiAgICAgICAgICByZXR1cm47DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgY2FsbGJhY2soKTsNCiAgICB9LA0KDQogICAgLy8g57u85ZCI5pel5pyf6YC76L6R6aqM6K+BDQogICAgdmFsaWRhdGVEYXRlTG9naWMoKSB7DQogICAgICBjb25zdCB0b2RheSA9IG5ldyBEYXRlKCk7DQogICAgICB0b2RheS5zZXRIb3VycygwLCAwLCAwLCAwKTsNCg0KICAgICAgLy8g5qOA5p+l5byA5bel5pel5pyfDQogICAgICBpZiAodGhpcy5mb3JtLnBsYW5TdGFydFRpbWUpIHsNCiAgICAgICAgY29uc3Qgc3RhcnREYXRlID0gbmV3IERhdGUodGhpcy5mb3JtLnBsYW5TdGFydFRpbWUpOw0KICAgICAgICBpZiAoc3RhcnREYXRlIDwgdG9kYXkpIHsNCiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi5byA5bel5pel5pyf5LiN6IO95pep5LqO5b2T5YmN5pel5pyfIik7DQogICAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIC8vIOajgOafpeW8gOW3peaXpeacn+WSjOWujOW3peaXpeacn+eahOWFs+ezuw0KICAgICAgaWYgKHRoaXMuZm9ybS5wbGFuU3RhcnRUaW1lICYmIHRoaXMuZm9ybS5wbGFuRW5kVGltZSkgew0KICAgICAgICBjb25zdCBzdGFydERhdGUgPSBuZXcgRGF0ZSh0aGlzLmZvcm0ucGxhblN0YXJ0VGltZSk7DQogICAgICAgIGNvbnN0IGVuZERhdGUgPSBuZXcgRGF0ZSh0aGlzLmZvcm0ucGxhbkVuZFRpbWUpOw0KICAgICAgICBpZiAoc3RhcnREYXRlID4gZW5kRGF0ZSkgew0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLlvIDlt6Xml6XmnJ/kuI3og73mmZrkuo7lrozlt6Xml6XmnJ8iKTsNCiAgICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgLy8g5qOA5p+l6ZyA5rGC5pel5pyfDQogICAgICBpZiAodGhpcy5mb3JtLnJlcXVpcmVkRGF0ZSkgew0KICAgICAgICBjb25zdCByZXF1aXJlZERhdGUgPSBuZXcgRGF0ZSh0aGlzLmZvcm0ucmVxdWlyZWREYXRlKTsNCiAgICAgICAgaWYgKHJlcXVpcmVkRGF0ZSA8IHRvZGF5KSB7DQogICAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIumcgOaxguaXpeacn+S4jeiDveaXqeS6juW9k+WJjeaXpeacnyIpOw0KICAgICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOmcgOaxguaXpeacn+S4jeiDveaXqeS6juWujOW3peaXpeacnw0KICAgICAgICBpZiAodGhpcy5mb3JtLnBsYW5FbmRUaW1lKSB7DQogICAgICAgICAgY29uc3QgZW5kRGF0ZSA9IG5ldyBEYXRlKHRoaXMuZm9ybS5wbGFuRW5kVGltZSk7DQogICAgICAgICAgaWYgKHJlcXVpcmVkRGF0ZSA8IGVuZERhdGUpIHsNCiAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLpnIDmsYLml6XmnJ/kuI3og73ml6nkuo7lrozlt6Xml6XmnJ8iKTsNCiAgICAgICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgcmV0dXJuIHRydWU7DQogICAgfQ0KICB9DQp9Ow0K"}, {"version": 3, "sources": ["edit_plan.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAi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file": "edit_plan.vue", "sourceRoot": "src/views/sc/plan", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"form-container\">\r\n      <!-- 基础信息区 -->\r\n      <el-tabs type=\"border-card\">\r\n        <el-tab-pane>\r\n          <span slot=\"label\"><i class=\"el-icon-date\"></i> 基础信息</span>\r\n          <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"8\">\r\n                <el-form-item label=\"计划编号\" prop=\"planCode\" required>\r\n                  <el-input v-model=\"form.planCode\" placeholder=\"请输入\" :disabled=\"isSystemCode\"></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"4\">\r\n                <el-switch\r\n                  v-model=\"isSystemCode\"\r\n                  active-text=\"系统编号\"\r\n                  inactive-text=\"\"\r\n                  style=\"margin-top: 13px;\"\r\n                  @change=\"handleSystemCodeChange\"\r\n                ></el-switch>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"计划名称\" prop=\"planName\" required>\r\n                  <el-input v-model=\"form.planName\" placeholder=\"请输入\"></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"来源类型\" prop=\"sourceType\" required>\r\n                  <el-select v-model=\"form.sourceType\" placeholder=\"销售订单\" style=\"width: 100%\">\r\n                    <el-option\r\n                      v-for=\"item in sourceTypeOptions\"\r\n                      :key=\"item.dictValue\"\r\n                      :label=\"item.dictLabel\"\r\n                      :value=\"item.dictValue\"\r\n                    ></el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"订单编号\" prop=\"orderCode\">\r\n                  <el-input v-model=\"form.orderCode\" placeholder=\"请输入\"></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"成品名称\" prop=\"productName\">\r\n                  <el-input\r\n                    placeholder=\"请选择成品\"\r\n                    v-model=\"form.productName\"\r\n                    class=\"input-with-select\"\r\n                  >\r\n                    <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"openProductSelection\"></el-button>\r\n                  </el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"成品编号\" prop=\"productCode\">\r\n                  <el-input v-model=\"form.productCode\" placeholder=\"请输入\" disabled></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"规格型号\" prop=\"specification\">\r\n                  <el-input v-model=\"form.specification\" placeholder=\"请输入\" disabled></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"成品类型\" prop=\"productType\">\r\n                  <dict-tag :options=\"dict.type.product_type\" :value=\"form.productType\" />\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"单位\" prop=\"unit\">\r\n                  <el-input v-model=\"form.unit\" placeholder=\"请输入\" disabled></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"计划数量\" prop=\"plannedQty\" required>\r\n                  <el-input-number v-model=\"form.plannedQty\" :min=\"1\" controls-position=\"right\" style=\"width: 100%\"></el-input-number>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"开工时间\" prop=\"planStartTime\">\r\n                  <el-date-picker\r\n                    v-model=\"form.planStartTime\"\r\n                    type=\"date\"\r\n                    placeholder=\"请选择日期\"\r\n                    style=\"width: 100%\"\r\n                    value-format=\"yyyy-MM-dd\"\r\n                  ></el-date-picker>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"完工时间\" prop=\"planEndTime\">\r\n                  <el-date-picker\r\n                    v-model=\"form.planEndTime\"\r\n                    type=\"date\"\r\n                    placeholder=\"请选择日期\"\r\n                    style=\"width: 100%\"\r\n                    value-format=\"yyyy-MM-dd\"\r\n                  ></el-date-picker>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"需求日期\" prop=\"requiredDate\">\r\n                  <el-date-picker\r\n                    v-model=\"form.requiredDate\"\r\n                    type=\"date\"\r\n                    placeholder=\"请选择日期\"\r\n                    style=\"width: 100%\"\r\n                    value-format=\"yyyy-MM-dd\"\r\n                  ></el-date-picker>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"备注\" prop=\"remark\">\r\n                  <el-input\r\n                    type=\"textarea\"\r\n                    v-model=\"form.remark\"\r\n                    placeholder=\"请输入\"\r\n                    :rows=\"4\"\r\n                  ></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"附件\" prop=\"attachment\">\r\n                  <div class=\"upload-container\" @click=\"triggerUpload\">\r\n                    <el-upload\r\n                      ref=\"upload\"\r\n                      class=\"upload-hidden\"\r\n                      action=\"#\"\r\n                      :http-request=\"uploadFile\"\r\n                      :file-list=\"fileList\"\r\n                      :before-upload=\"beforeUpload\"\r\n                      :on-remove=\"handleRemove\"\r\n                      multiple\r\n                      drag\r\n                    >\r\n                      <div class=\"upload-area\">\r\n                        <i class=\"el-icon-upload\"></i>\r\n                        <div class=\"upload-text\">点击或者拖动文件到虚线框内上传</div>\r\n                        <div class=\"upload-hint\">支持 docx, xls, PDF, rar, zip, PNG, JPG 等类型的文件</div>\r\n                      </div>\r\n                    </el-upload>\r\n                  </div>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-divider>\r\n              <span class=\"bom-title\">BOM组成</span>\r\n            </el-divider>\r\n            \r\n            <el-row>\r\n              <el-col :span=\"24\">\r\n                <div class=\"bom-container\">\r\n                  <div class=\"folder-icon\" v-if=\"!selectedBom\">\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"60\" height=\"60\" viewBox=\"0 0 24 24\" fill=\"#DCDFE6\">\r\n                      <path d=\"M10 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2h-8l-2-2z\"/>\r\n                    </svg>\r\n                  </div>\r\n                  <div class=\"bom-text\" v-if=\"!selectedBom\">暂无数据</div>\r\n                  <div class=\"bom-info\" v-else>\r\n                    <div class=\"bom-header\">\r\n                      <div class=\"bom-title-info\">\r\n                        <span>BOM编号：{{ selectedBom.bom_code }}</span>\r\n                        <span>版本号：{{ selectedBom.bom_version }}</span>\r\n                      </div>\r\n                      <el-button type=\"text\" icon=\"el-icon-delete\" @click=\"clearSelectedBom\">清除</el-button>\r\n                    </div>\r\n                    <el-table\r\n                      :data=\"bomDetailList\"\r\n                      border\r\n                      size=\"small\"\r\n                      style=\"width: 100%\"\r\n                      class=\"bom-detail-table\"\r\n                    >\r\n                      <el-table-column label=\"序号\" type=\"index\" width=\"50\" align=\"center\"></el-table-column>\r\n                      <el-table-column prop=\"material_code\" label=\"物料编码\" min-width=\"120\" align=\"center\"></el-table-column>\r\n                      <el-table-column prop=\"material_name\" label=\"物料名称\" min-width=\"150\" align=\"center\"></el-table-column>\r\n                      <el-table-column prop=\"specification\" label=\"规格型号\" min-width=\"100\" align=\"center\"></el-table-column>\r\n                      <el-table-column prop=\"unit\" label=\"单位\" width=\"60\" align=\"center\"></el-table-column>\r\n                      <el-table-column prop=\"quantity\" label=\"用量\" width=\"80\" align=\"center\"></el-table-column>\r\n                      <el-table-column prop=\"remark\" label=\"备注\" min-width=\"120\" align=\"center\"></el-table-column>\r\n                    </el-table>\r\n                  </div>\r\n                  <div class=\"bom-action\">\r\n                    <el-tooltip :disabled=\"form.productId\" content=\"请先选择成品!\" placement=\"right\" effect=\"light\">\r\n                      <el-button type=\"primary\" class=\"select-bom-button\" @click=\"selectBom\">选择BOM</el-button>\r\n                    </el-tooltip>\r\n                  </div>\r\n                </div>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row>\r\n              <el-col :span=\"24\" style=\"text-align: center; margin-top: 20px;\">\r\n                <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n                <el-button @click=\"cancel\">取 消</el-button>\r\n              </el-col>\r\n            </el-row>\r\n          </el-form>\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n    </div>\r\n    \r\n    <!-- 产品选择对话框 -->\r\n    <el-dialog title=\"选择成品\" :visible.sync=\"productDialogVisible\" width=\"40%\" append-to-body>\r\n      <el-form :model=\"productQuery\" ref=\"productQueryForm\" :inline=\"true\" class=\"demo-form-inline\" size=\"small\">\r\n        <el-form-item>\r\n          <el-input v-model=\"productQuery.keyword\" placeholder=\"请输入产品编号/名称\" clearable style=\"width: 180px;\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-select v-model=\"productQuery.unit\" placeholder=\"请选择单位\" clearable style=\"width: 120px;\">\r\n            <el-option label=\"个\" value=\"个\"></el-option>\r\n            <el-option label=\"件\" value=\"件\"></el-option>\r\n            <el-option label=\"台\" value=\"台\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-select v-model=\"productQuery.type\" placeholder=\"请选择类型\" clearable style=\"width: 120px;\">\r\n            <el-option label=\"成品\" value=\"成品\"></el-option>\r\n            <el-option label=\"半成品\" value=\"半成品\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-select v-model=\"productQuery.property\" placeholder=\"请选择产品属性\" clearable style=\"width: 120px;\">\r\n            <el-option label=\"自制\" value=\"自制\"></el-option>\r\n            <el-option label=\"外购\" value=\"外购\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"small\" @click=\"searchProducts\">查询</el-button>\r\n          <el-button icon=\"el-icon-refresh\" size=\"small\" @click=\"resetProductQuery\">重置</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n      \r\n      <el-table\r\n        v-loading=\"productLoading\"\r\n        :data=\"productList\"\r\n        border\r\n        size=\"small\"\r\n        style=\"width: 100%\"\r\n        @selection-change=\"handleProductSelectionChange\"\r\n        height=\"300\"\r\n      >\r\n        <el-table-column type=\"selection\" width=\"40\" align=\"center\"></el-table-column>\r\n        <el-table-column label=\"序号\" type=\"index\" width=\"40\" align=\"center\"></el-table-column>\r\n        <el-table-column prop=\"product_code\" label=\"产品编号\" width=\"90\" align=\"center\"></el-table-column>\r\n        <el-table-column prop=\"product_name\" label=\"产品名称\" min-width=\"120\" align=\"center\"></el-table-column>\r\n        <el-table-column prop=\"product_sfn\" label=\"规格型号\" min-width=\"90\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <span :style=\"{color: '#1890ff'}\">{{ scope.row.product_sfn }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"product_unit\" label=\"单位\" width=\"60\" align=\"center\"></el-table-column>\r\n        <el-table-column prop=\"product_type\" label=\"产品类型\" width=\"70\" align=\"center\" :formatter=\"formatProductType\"></el-table-column>\r\n      </el-table>\r\n      \r\n      <div class=\"pagination-container\">\r\n        <span class=\"total-text\">共 {{ productTotal }} 条</span>\r\n        <div class=\"pagination-wrapper\">\r\n          <span class=\"page-size\">\r\n            <el-select v-model=\"productQuery.pageSize\" size=\"mini\" @change=\"handleProductSizeChange\" style=\"width: 80px;\">\r\n              <el-option\r\n                v-for=\"item in [10, 20, 30, 50]\"\r\n                :key=\"item\"\r\n                :label=\"`${item}条/页`\"\r\n                :value=\"item\">\r\n              </el-option>\r\n            </el-select>\r\n          </span>\r\n          <el-pagination\r\n            small\r\n            background\r\n            @current-change=\"handleProductCurrentChange\"\r\n            :current-page=\"productQuery.pageNum\"\r\n            :page-size=\"productQuery.pageSize\"\r\n            layout=\"prev, pager, next, jumper\"\r\n            :pager-count=\"5\"\r\n            :total=\"productTotal\">\r\n          </el-pagination>\r\n        </div>\r\n      </div>\r\n      \r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button size=\"small\" @click=\"productDialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" size=\"small\" @click=\"confirmProductSelect\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    \r\n    <!-- BOM选择对话框 -->\r\n    <el-dialog title=\"选择BOM\" :visible.sync=\"bomDialogVisible\" width=\"40%\" append-to-body>\r\n      <div class=\"bom-dialog-header\">\r\n        <div class=\"product-info\">\r\n          <span>产品名称：{{ form.productName }}</span>\r\n          <span>产品编号：{{ form.productCode }}</span>\r\n          <span>规格型号：{{ form.specification }}</span>\r\n          <span>单位：{{ form.unit }}</span>\r\n        </div>\r\n      </div>\r\n      \r\n      <el-table\r\n        v-loading=\"bomLoading\"\r\n        :data=\"bomList\"\r\n        border\r\n        style=\"width: 100%\"\r\n        @row-click=\"handleBomSelect\"\r\n        highlight-current-row\r\n        size=\"small\"\r\n        height=\"300\"\r\n      >\r\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-radio v-model=\"selectedBomId\" :label=\"scope.row.bom_id\" @change=\"handleBomSelect(scope.row)\">&nbsp;</el-radio>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"序号\" type=\"index\" width=\"55\" align=\"center\"></el-table-column>\r\n        <el-table-column prop=\"bom_code\" label=\"BOM编号\" min-width=\"150\" align=\"center\"></el-table-column>\r\n        <el-table-column prop=\"bom_version\" label=\"版本号\" width=\"100\" align=\"center\"></el-table-column>\r\n        <el-table-column prop=\"bom_status\" label=\"默认BOM\" width=\"100\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ scope.row.bom_status === '1' ? '是' : '否' }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"bom_output\" label=\"日产量\" width=\"100\" align=\"center\"></el-table-column>\r\n      </el-table>\r\n      \r\n      <pagination\r\n        v-show=\"bomTotal > 0\"\r\n        :total=\"bomTotal\"\r\n        :page.sync=\"bomQuery.pageNum\"\r\n        :limit.sync=\"bomQuery.pageSize\"\r\n        @pagination=\"getBomList\"\r\n      />\r\n      \r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"bomDialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmBomSelect\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getProductionPlan, updateProductionPlan, addProductionPlan } from \"@/api/sc/productionPlan\";\r\nimport { listProducts, listBomsByProductId, findBomDetails } from \"@/api/basic/product\";\r\nimport { getAutoNumbers } from \"@/api/basic/numbers\";\r\nimport Pagination from \"@/components/Pagination\";\r\nimport DictTag from \"@/components/DictTag\";\r\n\r\nexport default {\r\n  name: \"EditPlan\",\r\n  dicts: ['product_type'],\r\n  components: {\r\n    Pagination,\r\n    DictTag\r\n  },\r\n  data() {\r\n    return {\r\n      // 页面标题\r\n      title: \"修改生产计划\",\r\n      // 是否为修改模式\r\n      isEdit: true,\r\n      // 表单数据\r\n      form: {\r\n        planCode: \"\",\r\n        planName: \"\",\r\n        sourceType: \"销售订单\",\r\n        orderCode: \"\",\r\n        productId: undefined,\r\n        productName: \"\",\r\n        productCode: \"\",\r\n        specification: \"\",\r\n        productType: \"\",\r\n        unit: \"\",\r\n        plannedQty: 1,\r\n        planStartTime: \"\",\r\n        planEndTime: \"\",\r\n        requiredDate: \"\",\r\n        remark: \"\"\r\n      },\r\n      // 是否使用系统编号\r\n      isSystemCode: true,\r\n      // 表单验证规则\r\n      rules: {\r\n        planName: [\r\n          { required: true, message: \"计划名称不能为空\", trigger: \"blur\" },\r\n          { max: 50, message: \"长度不能超过50个字符\", trigger: \"blur\" }\r\n        ],\r\n        sourceType: [\r\n          { required: true, message: \"来源类型不能为空\", trigger: \"change\" }\r\n        ],\r\n        productName: [\r\n          { required: true, message: \"请选择产品\", trigger: \"change\" }\r\n        ],\r\n        plannedQty: [\r\n          { required: true, message: \"计划数量不能为空\", trigger: \"blur\" }\r\n        ],\r\n        planStartTime: [\r\n          { validator: this.validateStartTime, trigger: \"change\" }\r\n        ],\r\n        planEndTime: [\r\n          { validator: this.validateEndTime, trigger: \"change\" }\r\n        ],\r\n        requiredDate: [\r\n          { validator: this.validateRequiredDate, trigger: \"change\" }\r\n        ]\r\n      },\r\n      // 产品下拉选项\r\n      productOptions: [],\r\n      // 产品加载状态\r\n      productLoading: false,\r\n      // 来源类型选项\r\n      sourceTypeOptions: [\r\n        { dictLabel: \"销售订单\", dictValue: \"销售订单\" },\r\n        { dictLabel: \"库存备货\", dictValue: \"库存备货\" }\r\n      ],\r\n      // 上传文件列表\r\n      fileList: [],\r\n      \r\n      // 产品选择对话框\r\n      productDialogVisible: false,\r\n      productQuery: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        keyword: \"\",\r\n        unit: \"\",\r\n        type: \"\",\r\n        property: \"\"\r\n      },\r\n      productList: [],\r\n      productTotal: 0,\r\n      selectedProduct: null,\r\n      \r\n      // BOM选择对话框\r\n      bomDialogVisible: false,\r\n      bomQuery: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        productId: undefined\r\n      },\r\n      bomList: [],\r\n      bomTotal: 0,\r\n      bomLoading: false,\r\n      selectedBom: null,\r\n      selectedBomId: null,\r\n      bomDetailList: [],\r\n    };\r\n  },\r\n  created() {\r\n    const planCode = this.$route.query.planCode;\r\n    if (planCode) {\r\n      this.isEdit = true;\r\n      this.title = \"修改生产计划\";\r\n      this.getPlanData(planCode);\r\n    } else {\r\n      this.isEdit = false;\r\n      this.title = \"新增生产计划\";\r\n      this.generatePlanCode();\r\n    }\r\n  },\r\n  methods: {\r\n    // 获取计划数据\r\n    getPlanData(planCode) {\r\n      getProductionPlan(planCode).then(response => {\r\n        this.form = response.data;\r\n        // 如果有关联的产品，则自动加载其BOM信息\r\n        if (this.form.productId) {\r\n          this.loadAssociatedBom();\r\n        }\r\n      });\r\n    },\r\n\r\n    // 加载关联的BOM\r\n    loadAssociatedBom() {\r\n      listBomsByProductId(this.form.productId).then(response => {\r\n        if (response.code === 200 && response.rows && response.rows.length > 0) {\r\n          // 查找默认的BOM (status '1')\r\n          const activeBom = response.rows.find(b => b.bom_status === '1');\r\n          if (activeBom) {\r\n            this.selectedBom = activeBom;\r\n            this.getBomDetail(); // 加载BOM详情\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    // 生成计划编号\r\n    generatePlanCode() {\r\n      getAutoNumbers(6).then(response => {\r\n        if (response.code === 200) {\r\n          this.form.planCode = response.msg;\r\n        } else {\r\n          this.$message.error('获取计划编号失败');\r\n        }\r\n      }).catch(() => {\r\n        this.$message.error('获取计划编号失败');\r\n      });\r\n    },\r\n    \r\n    // 处理系统编号开关变化\r\n    handleSystemCodeChange(val) {\r\n      if (val) {\r\n        // 如果开启系统编号，则生成编号\r\n        this.generatePlanCode();\r\n      } else {\r\n        // 如果关闭系统编号，则清空编号\r\n        this.form.planCode = '';\r\n      }\r\n    },\r\n    \r\n    // 触发上传\r\n    triggerUpload() {\r\n      this.$refs.upload.$el.click();\r\n    },\r\n    \r\n    // 打开产品选择弹窗\r\n    openProductSelection() {\r\n      // 修改模式下不允许更换产品\r\n      if (this.isEdit) {\r\n        this.$message.warning(\"修改模式下不允许更换产品。\");\r\n        return;\r\n      }\r\n      this.productDialogVisible = true;\r\n      this.getProductList();\r\n    },\r\n    \r\n    // 获取产品列表\r\n    getProductList() {\r\n      this.productLoading = true;\r\n      listProducts({\r\n        pageNum: this.productQuery.pageNum,\r\n        pageSize: this.productQuery.pageSize,\r\n        keyword: this.productQuery.keyword,\r\n        productUnit: this.productQuery.unit,\r\n        productType: this.productQuery.type,\r\n        productProperty: this.productQuery.property\r\n      }).then(response => {\r\n        this.productLoading = false;\r\n        if (response.code === 200) {\r\n          this.productList = response.rows;\r\n          this.productTotal = response.total;\r\n        }\r\n      }).catch(() => {\r\n        this.productLoading = false;\r\n        // 模拟数据\r\n        this.productList = [\r\n          { product_id: 1, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' },\r\n          { product_id: 2, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' },\r\n          { product_id: 3, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' },\r\n          { product_id: 4, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' },\r\n          { product_id: 5, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' }\r\n        ];\r\n        this.productTotal = 50;\r\n      });\r\n    },\r\n    \r\n    // 搜索产品\r\n    searchProducts() {\r\n      this.productQuery.pageNum = 1;\r\n      this.getProductList();\r\n    },\r\n    \r\n    // 重置产品查询条件\r\n    resetProductQuery() {\r\n      this.productQuery = {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        keyword: \"\",\r\n        unit: \"\",\r\n        type: \"\",\r\n        property: \"\"\r\n      };\r\n      this.getProductList();\r\n    },\r\n    \r\n    // 处理产品表格选择变化\r\n    handleProductSelectionChange(selection) {\r\n      if (selection.length > 0) {\r\n        this.selectedProduct = selection[0];\r\n      } else {\r\n        this.selectedProduct = null;\r\n      }\r\n    },\r\n    \r\n    // 处理产品页码变化\r\n    handleProductCurrentChange(currentPage) {\r\n      this.productQuery.pageNum = currentPage;\r\n      this.getProductList();\r\n    },\r\n    \r\n    // 处理产品每页条数变化\r\n    handleProductSizeChange(size) {\r\n      this.productQuery.pageSize = size;\r\n      this.productQuery.pageNum = 1;\r\n      this.getProductList();\r\n    },\r\n    \r\n    // 确认产品选择\r\n    confirmProductSelect() {\r\n      if (this.selectedProduct) {\r\n        this.form.productId = this.selectedProduct.product_id;\r\n        this.form.productName = this.selectedProduct.product_name;\r\n        this.form.productCode = this.selectedProduct.product_code;\r\n        this.form.specification = this.selectedProduct.product_sfn;\r\n        this.form.productType = this.formatProductType(this.selectedProduct);\r\n        this.form.unit = this.selectedProduct.product_unit;\r\n        this.productDialogVisible = false;\r\n        \r\n        // 清空已选BOM\r\n        this.selectedBom = null;\r\n        this.selectedBomId = null;\r\n      } else {\r\n        this.$message.warning('请选择一个产品！');\r\n      }\r\n    },\r\n    \r\n    // 格式化产品类型\r\n    formatProductType(row, column) {\r\n      const type = row.product_type;\r\n      const option = this.dict.type.product_type.find(item => item.dictValue == type);\r\n      return option ? option.dictLabel : type;\r\n    },\r\n    \r\n    // 选择BOM\r\n    selectBom() {\r\n      if (!this.form.productId) {\r\n        this.$message.warning('请先选择成品！');\r\n        return;\r\n      }\r\n      this.bomDialogVisible = true;\r\n      this.getBomList();\r\n    },\r\n    \r\n    // 获取BOM列表\r\n    getBomList() {\r\n      this.bomLoading = true;\r\n      listBomsByProductId(this.form.productId).then(response => {\r\n        this.bomLoading = false;\r\n        if (response.code === 200) {\r\n          this.bomList = response.rows;\r\n          this.bomTotal = response.total;\r\n          if (!this.bomList || this.bomList.length === 0) {\r\n            this.$message.info(\"未找到该产品的BOM信息\");\r\n          } else {\r\n            // 如果有默认BOM，则自动选中\r\n            const defaultBom = this.bomList.find(b => b.bom_status === '1');\r\n            if (defaultBom) {\r\n              this.handleBomSelect(defaultBom);\r\n            }\r\n          }\r\n        } else {\r\n          this.bomList = [];\r\n          this.bomTotal = 0;\r\n        }\r\n      }).catch(() => {\r\n        this.bomLoading = false;\r\n        this.$message.error('获取BOM列表失败');\r\n      });\r\n    },\r\n    \r\n    // 处理BOM行选择\r\n    handleBomSelect(row) {\r\n      this.selectedBom = row;\r\n      this.selectedBomId = row.bom_id;\r\n    },\r\n    \r\n    // 确认BOM选择\r\n    confirmBomSelect() {\r\n      if (this.selectedBom) {\r\n        this.bomDialogVisible = false;\r\n        // 获取BOM详情\r\n        this.getBomDetail();\r\n      } else {\r\n        this.$message.warning('请选择一个BOM！');\r\n      }\r\n    },\r\n    \r\n    // 获取BOM详情\r\n    getBomDetail() {\r\n      findBomDetails(this.selectedBom.bom_id).then(response => {\r\n        console.log(\"成功获取BOM详情响应:\", response);\r\n        if (response && response.code === 200) {\r\n          this.bomDetailList = response.rows;\r\n        } else {\r\n          this.bomDetailList = [];\r\n          this.$message.error(\"获取BOM详情失败: \" + (response ? response.msg : '无响应'));\r\n        }\r\n      }).catch(error => {\r\n        console.error(\"获取BOM详情接口调用失败:\", error);\r\n        this.$message.error(\"获取BOM详情接口调用失败\");\r\n        this.bomDetailList = [];\r\n      });\r\n    },\r\n    \r\n    // 清除已选BOM\r\n    clearSelectedBom() {\r\n      this.selectedBom = null;\r\n      this.selectedBomId = null;\r\n      this.bomDetailList = [];\r\n    },\r\n    \r\n    // 上传前检查文件类型和大小\r\n    beforeUpload(file) {\r\n      const isValidType = /\\.(doc|docx|xls|xlsx|pdf|rar|zip|png|jpg|jpeg)$/i.test(file.name);\r\n      const isLt10M = file.size / 1024 / 1024 < 10;\r\n\r\n      if (!isValidType) {\r\n        this.$message.error('上传文件格式不支持!');\r\n        return false;\r\n      }\r\n      if (!isLt10M) {\r\n        this.$message.error('上传文件大小不能超过 10MB!');\r\n        return false;\r\n      }\r\n      return true;\r\n    },\r\n    \r\n    // 上传文件处理\r\n    uploadFile(options) {\r\n      // 这里应该调用实际的文件上传API\r\n      console.log('文件上传:', options.file);\r\n      // 假设上传成功\r\n      this.fileList.push({\r\n        name: options.file.name,\r\n        url: URL.createObjectURL(options.file)\r\n      });\r\n      options.onSuccess();\r\n    },\r\n    \r\n    // 移除文件\r\n    handleRemove(file) {\r\n      const index = this.fileList.indexOf(file);\r\n      if (index !== -1) {\r\n        this.fileList.splice(index, 1);\r\n      }\r\n    },\r\n    \r\n    // 表单提交\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          // 额外的日期逻辑验证\r\n          if (!this.validateDateLogic()) {\r\n            return;\r\n          }\r\n\r\n          // 根据isEdit标志决定调用哪个API\r\n          const apiCall = this.isEdit ? updateProductionPlan : addProductionPlan;\r\n          \r\n          apiCall(this.form).then(response => {\r\n            if (response.code === 200) {\r\n              this.$modal.msgSuccess(this.isEdit ? \"修改成功\" : \"新增成功\");\r\n              this.cancel();\r\n            } else {\r\n              this.$modal.msgError(response.msg || (this.isEdit ? \"修改失败\" : \"新增失败\"));\r\n            }\r\n          }).catch(() => {\r\n            this.$modal.msgError(\"操作失败，请稍后重试\");\r\n          });\r\n        }\r\n      });\r\n    },\r\n    \r\n    // 取消按钮\r\n    cancel() {\r\n      this.$router.push({ path: \"/sc/plan\" });\r\n    },\r\n\r\n    // 验证开工时间\r\n    validateStartTime(rule, value, callback) {\r\n      if (!value) {\r\n        callback();\r\n        return;\r\n      }\r\n\r\n      const today = new Date();\r\n      today.setHours(0, 0, 0, 0);\r\n      const startDate = new Date(value);\r\n\r\n      // 开工日期不能早于当前日期\r\n      if (startDate < today) {\r\n        callback(new Error('开工日期不能早于当前日期'));\r\n        return;\r\n      }\r\n\r\n      // 如果完工时间已选择，开工时间不能晚于完工时间\r\n      if (this.form.planEndTime) {\r\n        const endDate = new Date(this.form.planEndTime);\r\n        if (startDate > endDate) {\r\n          callback(new Error('开工日期不能晚于完工日期'));\r\n          return;\r\n        }\r\n      }\r\n\r\n      callback();\r\n    },\r\n\r\n    // 验证完工时间\r\n    validateEndTime(rule, value, callback) {\r\n      if (!value) {\r\n        callback();\r\n        return;\r\n      }\r\n\r\n      const endDate = new Date(value);\r\n\r\n      // 如果开工时间已选择，完工时间不能早于开工时间\r\n      if (this.form.planStartTime) {\r\n        const startDate = new Date(this.form.planStartTime);\r\n        if (endDate < startDate) {\r\n          callback(new Error('完工日期不能早于开工日期'));\r\n          return;\r\n        }\r\n      }\r\n\r\n      callback();\r\n    },\r\n\r\n    // 验证需求日期\r\n    validateRequiredDate(rule, value, callback) {\r\n      if (!value) {\r\n        callback();\r\n        return;\r\n      }\r\n\r\n      const requiredDate = new Date(value);\r\n\r\n      // 需求日期不能早于当前日期\r\n      const today = new Date();\r\n      today.setHours(0, 0, 0, 0);\r\n      if (requiredDate < today) {\r\n        callback(new Error('需求日期不能早于当前日期'));\r\n        return;\r\n      }\r\n\r\n      // 如果完工时间已选择，需求日期不能早于完工时间\r\n      if (this.form.planEndTime) {\r\n        const endDate = new Date(this.form.planEndTime);\r\n        if (requiredDate < endDate) {\r\n          callback(new Error('需求日期不能早于完工日期'));\r\n          return;\r\n        }\r\n      }\r\n\r\n      callback();\r\n    },\r\n\r\n    // 综合日期逻辑验证\r\n    validateDateLogic() {\r\n      const today = new Date();\r\n      today.setHours(0, 0, 0, 0);\r\n\r\n      // 检查开工日期\r\n      if (this.form.planStartTime) {\r\n        const startDate = new Date(this.form.planStartTime);\r\n        if (startDate < today) {\r\n          this.$modal.msgError(\"开工日期不能早于当前日期\");\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 检查开工日期和完工日期的关系\r\n      if (this.form.planStartTime && this.form.planEndTime) {\r\n        const startDate = new Date(this.form.planStartTime);\r\n        const endDate = new Date(this.form.planEndTime);\r\n        if (startDate > endDate) {\r\n          this.$modal.msgError(\"开工日期不能晚于完工日期\");\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 检查需求日期\r\n      if (this.form.requiredDate) {\r\n        const requiredDate = new Date(this.form.requiredDate);\r\n        if (requiredDate < today) {\r\n          this.$modal.msgError(\"需求日期不能早于当前日期\");\r\n          return false;\r\n        }\r\n\r\n        // 需求日期不能早于完工日期\r\n        if (this.form.planEndTime) {\r\n          const endDate = new Date(this.form.planEndTime);\r\n          if (requiredDate < endDate) {\r\n            this.$modal.msgError(\"需求日期不能早于完工日期\");\r\n            return false;\r\n          }\r\n        }\r\n      }\r\n\r\n      return true;\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.form-container {\r\n  background-color: #fff;\r\n  padding: 10px;\r\n}\r\n\r\n.el-tabs--border-card {\r\n  box-shadow: none;\r\n}\r\n\r\n.upload-container {\r\n  width: 100%;\r\n  border: 1px dashed #d9d9d9;\r\n  border-radius: 6px;\r\n  text-align: center;\r\n  padding: 20px 0;\r\n  cursor: pointer;\r\n}\r\n\r\n.upload-container:hover {\r\n  border-color: #409EFF;\r\n}\r\n\r\n.upload-area {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.upload-area .el-icon-upload {\r\n  font-size: 48px;\r\n  color: #c0c4cc;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.upload-text {\r\n  font-size: 14px;\r\n  color: #606266;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.upload-hint {\r\n  font-size: 12px;\r\n  color: #909399;\r\n}\r\n\r\n.input-with-select .el-input-group__append {\r\n  background-color: #fff;\r\n}\r\n\r\n.bom-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.bom-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin: 20px 0;\r\n  padding: 30px 0;\r\n}\r\n\r\n.folder-icon {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.bom-text {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.warning-text {\r\n  color: #E6A23C;\r\n  font-size: 12px;\r\n  margin-left: 10px;\r\n}\r\n\r\n.warning-text i {\r\n  margin-right: 5px;\r\n}\r\n\r\n.upload-hidden {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.upload-hidden >>> .el-upload {\r\n  width: 100%;\r\n}\r\n\r\n.upload-hidden >>> .el-upload-dragger {\r\n  width: 100%;\r\n  height: 100%;\r\n  border: none;\r\n  padding: 0;\r\n  margin: 0;\r\n}\r\n\r\n.bom-dialog-header {\r\n  margin-bottom: 15px;\r\n  padding: 10px;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n}\r\n\r\n.product-info {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.product-info span {\r\n  margin-right: 20px;\r\n  line-height: 30px;\r\n}\r\n\r\n.el-radio {\r\n  margin-right: 0;\r\n}\r\n\r\n.pagination-container {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 10px 0;\r\n  font-size: 12px;\r\n}\r\n\r\n.pagination-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.page-size {\r\n  margin-right: 10px;\r\n}\r\n\r\n.total-text {\r\n  color: #606266;\r\n  font-size: 12px;\r\n}\r\n\r\n.bom-info {\r\n  width: 100%;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.bom-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n  padding: 8px;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n}\r\n\r\n.bom-title-info {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.bom-title-info span {\r\n  margin-right: 20px;\r\n  font-weight: bold;\r\n}\r\n\r\n.bom-detail-table {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.bom-action {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.select-bom-button {\r\n  margin-right: 10px;\r\n}\r\n</style>\r\n"]}]}