package com.ruoyi.system.api;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.system.api.domain.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(contextId = "detectService", value = "ldzl-qc", fallbackFactory = com.ruoyi.system.api.factory.DetectServiceFallbackFactory.class)
public interface DetectService {

    /**
     * 查询缺陷记录
     * @param qc_id 质量检测单ID
     * @return 分页数据响应对象 TableDataInfo
     */
    @PostMapping("/qc/defectRecord/selectQcDefectRecord/{qc_id}")
    public TableDataInfo selectQcDefectRecord(@PathVariable("qc_id") Long qc_id);

    /**
     * 查询过程检测单行记录
     * @param ipqcId 过程质量检测单行ID
     * @return 分页数据响应对象 TableDataInfo
     */
    @PostMapping("/qc/defectRecord/selectQcIpqcLineId/{ipqcId}")
    public TableDataInfo selectQcIpqcLineId(@PathVariable("ipqcId") Long ipqcId);

    /**
     * 查询过程检测单记录
     * @param qc_id 质量检测单ID
     * @return 过程质量检测数据传输对象 QcIpqcDto
     */
    @PostMapping("/qc/defectRecord/selectQcIpqcId/{qc_id}")
    public QcIpqcDto selectQcIpqcId(@PathVariable("qc_id") Long qc_id);

    /**
     * 查询来料检验单信息
     * @param source_doc_id 来源文档ID
     * @return 来料检验数据传输对象 QcIqcDto
     */
    @PostMapping("/qc/defectRecord/selectQcIqcId/{source_doc_id}")
    public QcIqcDto selectQcIqcId(@PathVariable("source_doc_id") Long source_doc_id);

    /**
     * 获取出货检验单详情
     * @param source_doc_code
     * @return
     */
    @PostMapping("/ipqc/selectQcOqcListSourceDocId/{source_doc_code}")
    public QcOqcDto selectQcOqcListSourceDocId(@PathVariable("source_doc_code") Long source_doc_code);
    /**
     * 获取退料检验单
     * @param source_doc_code
     * @return
     */
    @PostMapping("/qc/rqc/selectQcRqcListSourceDocId/{source_doc_code}")
    public QcRqcDto selectQcRqcListSourceDocId(@PathVariable("source_doc_code") Long source_doc_code);

    /**
     * 新增过程检验单
     * @param qcIpqcDto
     * @return
     */
    @PostMapping("/ipqc/addIpqc")
    public AjaxResult add(@RequestBody QcIpqcDto qcIpqcDto);

    /**
     * 新增退料检验单
     */
    @PostMapping("/qc/rqc/addQcRqc")
    public AjaxResult add(@RequestBody QcRqcDto qcRqcDto);

    /**
     * 根据物料编码查询检测方案
     * template_id 检测方案id
     * quantity_check_num 最低检测数
     * quantity_unqualified_num 最大不合格数
     * cr_rate 最大致命缺陷率
     * maj_rate 最大严重缺陷率
     * min_rate 最大轻微缺陷率
     * 是否合格的判断标准
     */
    @PostMapping("/qc/qcTemplate/selectQcTemplateProductMaterialCode")
    QcTemplateProductDto selectQcTemplateProductMaterialCode(@RequestParam("material_code") String material_code);

    /**
     * 根据产品ID查询检测方案
     * template_id 检测方案id
     * quantity_check_num 最低检测数
     * quantity_unqualified_num 最大不合格数
     * cr_rate 最大致命缺陷率
     * maj_rate 最大严重缺陷率
     * min_rate 最大轻微缺陷率
     * 是否合格的判断标准
     */
    @PostMapping("/qc/qcTemplate/selectQcTemplateProductProductCode")
    QcTemplateProductDto selectQcTemplateProductProductCode(@RequestParam("product_id") Long product_id);


}
