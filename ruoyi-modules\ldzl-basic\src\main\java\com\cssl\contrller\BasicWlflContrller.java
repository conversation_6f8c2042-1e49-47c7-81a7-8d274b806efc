package com.cssl.contrller;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.conditions.query.QueryChainWrapper;
import com.cssl.mapper.BasicWlflMapper;
import com.cssl.mapper.BasicWlflzMapper;
import com.cssl.pojo.BasicNumbers;
import com.cssl.pojo.BasicWlfl;
import com.cssl.pojo.BasicWlflz;
import com.cssl.service.BasicNumbersService;
import com.cssl.service.BasicWlflService;
import com.cssl.service.BasicWlflzService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/wlfl")
public class BasicWlflContrller extends BaseController {
    @Resource
    private BasicWlflService basicWlflService;

    @Resource
    private BasicWlflzService bwzs; // 子分类
    @Resource
    private BasicWlflMapper basicWlflMapper;

    @Resource
    private BasicWlflzMapper basicWlflzMapper;

    //添加父分类
    @Log(title = "添加父分类", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasicWlfl basicWlfl)
    {
        return toAjax(basicWlflService.insertBasicWlfl(basicWlfl));
    }

    //修改父类
    @Log(title = "修改父类", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasicWlfl basicWlfl)
    {
        return toAjax(basicWlflService.updateBasicWlflz(basicWlfl));
    }

    //删除父类
    @Log(title = "删除父类", businessType = BusinessType.UPDATE)
    @PutMapping("/dp/{material_classification_id}")
    public AjaxResult edit1(@PathVariable Long material_classification_id)
    {
        return toAjax(basicWlflService.delByBasicWlflzInt(material_classification_id));
    }

    //查询所有父类分类信息
    @PostMapping ("/find")
    public TableDataInfo findall()
    {
        List<BasicWlfl> list= basicWlflService.findBasicWlflz();
        return getDataTable(list);
    }

    @GetMapping("/list")
    public TableDataInfo listWlfl(){

        QueryChainWrapper<BasicWlfl> queryChain = new QueryChainWrapper<>(basicWlflMapper);
        queryChain.eq("is_delete", "0");
        List<BasicWlfl> list = basicWlflService.list(queryChain.getWrapper());

// 同理，对于子查询也需要修改
        for (BasicWlfl basicWlfl : list) {
            QueryChainWrapper<BasicWlflz> subQueryChain = new QueryChainWrapper<>(basicWlflzMapper);
            subQueryChain.eq("is_delete", "0")
                    .eq("material_classification_id", basicWlfl.getMaterial_classification_id());
            List<BasicWlflz> list1 = bwzs.list(subQueryChain.getWrapper());
            basicWlfl.setBasicWlflz(list1);
        }
        return getDataTable(list);
    }


    /**
     * 查询所有分类 一对多
     * @return
     */
    @PostMapping("/selectBasicWlflWithChildren")
    public List<BasicWlfl> selectBasicWlflWithChildren() {
        return basicWlflService.selectBasicWlflWithChildren();
    }

}
