package com.cssl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cssl.pojo.BasicTeam;
import com.cssl.pojo.BasicTeamdetails;
import com.cssl.pojo.vo.BasicTeamVo;

import java.util.List;

public interface BasicTeamdetailsMapper extends BaseMapper<BasicTeamdetails> {
    //根据班组id查看班组详情
    public List<BasicTeamVo> selectTeamdetailsById(Long team_id);

    //根据班组id查看班组详情
    public List<BasicTeamdetails> selectTeamdetailsById1(Long team_id);

    //批量删除
    public int delBatchTeamdetails(List<Long> teamdetails_ids);

    //删除班组信息
    public int delTeamdetailsById(Long team_id);

    //根据班组id批量删除
    public int delBatchTeamdetails1(List<Long> team_ids);
}
