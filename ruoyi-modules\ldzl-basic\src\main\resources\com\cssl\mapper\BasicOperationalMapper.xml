<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cssl.mapper.BasicOperationalMapper">

    <resultMap type="com.cssl.pojo.BasicOperational" id="BasicOperationalResult">
        <result property="operational_id"    column="operational_id"    />
        <result property="operational_code"    column="operational_code"    />
        <result property="operational_name"    column="operational_name"    />
        <result property="operational_status"    column="operational_status"    />
        <result property="operational_description"    column="operational_description"    />
        <result property="remark"    column="remarks"    />
        <result property="processId"    column="process_id"    />
        <result property="productId"    column="product_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="is_delete"    column="is_delete"    />
        <collection property="operatonaldetails" ofType="com.cssl.pojo.BasicOperatonaldetails">
            <id column="operationaldetails_id" property="operationaldetails_id"/>
        </collection>
    </resultMap>

    <sql id="selectBasicOperationalVo">
        select
            bo.operational_id,
            bo.operational_code,
            bo.operational_name,
            bo.operational_status,
            bo.operational_description,
            bo.remarks,
            bod.process_id,
            bod.product_id,
            bo.create_by,
            bo.create_time,
            bo.update_by,
            bo.update_time,
            bo.is_delete
        from
            basic_operational bo
        inner join
            basic_operatonaldetails bod on bo.operational_id = bod.operational_id
    </sql>
    <insert id="insertBasicOperational" parameterType="com.cssl.pojo.BasicOperational" useGeneratedKeys="true" keyProperty="operational_id">
        INSERT into basic_operational
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="operational_code!= null and operational_code!=''">operational_code,</if>
            <if test="operational_name != null and operational_name != ''">operational_name,</if>
            <if test="operational_status != null and operational_status != ''" >operational_status,</if>
            <if test="operational_description != null  and operational_description !='' ">operational_description,</if>
            <if test="remarks != null and remarks !=''">remarks,</if>
            <if test="is_delete != null ">is_delete,</if>
            <if test="create_by != null and create_by !='' ">create_by,</if>
            <if test="create_time!= null ">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="operational_code!= null and operational_code!=''">#{operational_code},</if>
            <if test="operational_name != null and operational_name != ''">#{operational_name},</if>
            <if test="operational_status != null and operational_status != ''" >#{operational_status},</if>
            <if test="operational_description != null  and operational_description !='' ">#{operational_description},</if>
            <if test="remarks != null and remarks !=''">#{remarks},</if>
            <if test="is_delete != null ">#{is_delete},</if>
            <if test="create_by != null and create_by !='' ">#{create_by},</if>
            <if test="create_time!= null ">#{create_time},</if>
        </trim>
    </insert>
    <update id="deleteBasicOperationalById">
        update basic_operational
        <trim prefix="SET" suffixOverrides=",">
            <if test="is_delete != null ">is_delete =1,</if>
        </trim>
        where operational_id = #{operational_id}
    </update>
    <update id="deleteBatchBasicOperational">
        update basic_operational set is_delete = '1' where operational_id in
        <foreach item="operationalIds" collection="list" open="(" separator="," close=")">
            #{operationalIds}
        </foreach>
    </update>
    <update id="updateBasicOperational">
        update basic_operational
        <trim prefix="SET" suffixOverrides=",">
            <if test="operational_name != null and operational_name != ''">operational_name=#{operational_name},</if>
            <if test="operational_status != null and operational_status !='' ">operational_status=#{operational_status},</if>
            <if test="operational_description != null and operational_description != ''" >operational_description=#{operational_description},</if>
            <if test="remarks != null and remarks !=''">remarks=#{remarks},</if>
            <if test="update_by != null and update_by !='' ">update_by=#{update_by},</if>
            <if test="update_time!= null ">update_time=#{update_time},</if>
        </trim>
        where operational_id = #{operational_id}
    </update>

    <select id="selectBasicOperationalList" parameterType="com.cssl.pojo.BasicOperational" resultMap="BasicOperationalResult">
        <include refid="selectBasicOperationalVo"/>
        <where>
            bo.is_delete = '0' and bod.is_delete = '0'
            <if test="operationalCode != null  and operationalCode != ''"> and bo.operational_code = #{operationalCode}</if>
            <if test="operationalName != null  and operationalName != ''"> and bo.operational_name like concat('%', #{operationalName}, '%')</if>
            <if test="operationalStatus != null  and operationalStatus != ''"> and bo.operational_status = #{operationalStatus}</if>
            <if test="productId != null"> and bod.product_id = #{productId}</if>
        </where>
    </select>

    <select id="selectBasicOperationalByProductId" resultMap="BasicOperationalResult">
        <include refid="selectBasicOperationalVo"/>
        where bod.product_id = #{productId} and bo.is_delete = '0' and bod.is_delete = '0'
    </select>

    <select id="listBasicOperational" resultType="com.cssl.pojo.BasicOperational">
        select * from basic_operational where is_delete=0
        <if test="operational_name !=null and operational_name != ''">and operational_name like CONCAT('%',#{operational_name},'%')</if>
        <if test="operational_status !=null and operational_status !=''">and operational_status=#{operational_status}</if>
        <if test="operational_id !=null and operational_id !=''">and operational_id=#{operational_id}</if>
    </select>
</mapper>



