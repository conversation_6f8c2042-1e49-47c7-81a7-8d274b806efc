package com.cssl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cssl.pojo.BasicProductionline;

import java.util.List;

public interface BasicProductionlineService extends IService<BasicProductionline> {
    //查询生产线信息
    public List<BasicProductionline> listBasicProductionline(BasicProductionline basicProductionline);

    //添加生产线信息
    public int addBasicProductionline(BasicProductionline basicProductionline);

    //修改生产线信息
    public int updateBasicProductionline(BasicProductionline basicProductionline);

    //删除生产线信息
    public int delBasicProductionline(Long production_line_id);

}
