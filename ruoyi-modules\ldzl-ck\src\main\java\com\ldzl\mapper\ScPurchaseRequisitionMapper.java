package com.ldzl.mapper;

import com.ldzl.pojo.ScPurchaseRequisition;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【sc_purchase_requisition(采购申请表)】的数据库操作Mapper
* @createDate 2025-07-14 19:50:13
* @Entity com.ldzl.pojo.ScPurchaseRequisition
*/
public interface ScPurchaseRequisitionMapper extends BaseMapper<ScPurchaseRequisition> {

    /**
     * 查询生产采购申请
     * @param po
     * @return
     */
    List<ScPurchaseRequisition> selectPurchase(ScPurchaseRequisition po);
}




