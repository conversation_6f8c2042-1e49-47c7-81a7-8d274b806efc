package com.cssl.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;
import java.util.List;

@Data
@TableName("basic_bom")
public class BasicBom extends BaseEntity {
    @TableId(value ="bom_id",type = IdType.AUTO)
    private Long bom_id;
    private String bom_code;
    private String bom_status;
    private Long product_id;
    private String remarks;
    private String is_delete;
    private String create_by;
    private Date create_time;
    private String update_by;
    private Date update_time;
    @TableField(exist = false)
    private BasicProduct basicProduct;
    @TableField(exist = false)
    private String product_code;
    @TableField(exist = false)
    private String product_name;
    @TableField(exist = false)
    private String product_sfn;
    @TableField(exist = false)
    private String product_unit;
    private String bom_version;
    private BigDecimal bom_output;
    @TableField(exist = false)
    private List<BasicBomdetails> basicBomdetails;

}
