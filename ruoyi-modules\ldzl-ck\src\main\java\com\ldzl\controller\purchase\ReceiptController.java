package com.ldzl.controller.purchase;

import com.ldzl.dto.AddItemReceiptDTO;
import com.ldzl.dto.ArrivalNoticeDTO;
import com.ldzl.dto.ItemReceiptDTO;
import com.ldzl.pojo.CkBatch;
import com.ldzl.pojo.CkItemRecpt;
import com.ldzl.pojo.CkPurchaseOrder;
import com.ldzl.service.*;
import com.ruoyi.common.core.domain.UR;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.TableDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 采购入库控制器
 */
@RestController
@RequestMapping("/receipt")
public class ReceiptController extends BaseController {

    @Autowired
    private CkItemRecptService irs; // 采购入库服务

    @Autowired
    private CkItemRecptLineService irls; // 采购入库行服务

    @Autowired
    private CkPurchaseOrderService pos; // 采购订单服务

    @Autowired
    private CkPurchaseOrderLineService pols; // 采购订单行服务]

    @Autowired
    private CkBatchService cbs; // 批次服务

    /**
     * 查询采购入库
     * @param itemRecpt
     * @return
     */
    @PostMapping("/findAll_receipt")
    public TableDataInfo findAll_receipt(CkItemRecpt itemRecpt){
        startPage();
        return getDataTable(irs.selectRecpt(itemRecpt));
    }

    /**
     * 查询指定采购入库单下的商品
     * @param recpt_id
     * @return
     */
    @PostMapping("/findReceipt_id/{recpt_id}")
    public TableDataInfo findReceipt_id(@PathVariable Long recpt_id){
        startPage();
        return getDataTable(irls.selectRecpt_id(recpt_id));
    }

    /**
     * 添加采购入库
     * @param itemRecptDTO
     * @return
     */
    @PostMapping("/addReceipt")
    public UR addReceipt(@RequestBody AddItemReceiptDTO itemRecptDTO){
        System.out.println("测试获取的采购入库信息："+ itemRecptDTO);
        //return irs.saveOrUpdate(itemRecptDTO) ? UR.ok("添加成功") : UR.fail("添加失败");
        return UR.ok("添加成功");
    }

    /**
     * 查询采购单 到货和采购中的采购单
     * @param po
     * @return
     */
    @PostMapping("/findOrder_arrived")
    public TableDataInfo findOrder_arrived(CkPurchaseOrder po) {
        startPage();
        return getDataTable(pos.findOrder_arrived(po));
    }

    /**
     * 查询采购单详情 可入库
     * @param arr
     * @return
     */
    @PostMapping("/findAll_po_line")
    public TableDataInfo findAll_po_line(ArrivalNoticeDTO arr) {
        startPage();
        return getDataTable(pols.selectOrderStore(arr));
    }

    /**
     * 添加采购入库单
     * @param itemReceiptDTO
     * @return
     */
    @PostMapping("/addItemReceipt")
    public UR addItemReceipt(@RequestBody ItemReceiptDTO itemReceiptDTO) {
        System.out.println("测试获取的入库单："+itemReceiptDTO.getItemRecpt());

        System.out.println("测试获取的入库单详情："+itemReceiptDTO.getListItemDetail());

        irs.addRecpt(itemReceiptDTO);
        return UR.ok( "添加成功");
    }


    /**
     * 新增批次
     * @param batch
     * @return
     */
    @PostMapping("/addBatch")
    public UR addBatch(CkBatch batch){
        CkBatch bat = cbs.insertBatch(batch);
        Map<String, CkBatch> map = new HashMap<>();
        map.put("批次", bat);
        if (bat != null)
            return UR.ok("添加成功",(Map)map);
        else
            return UR.fail("添加失败");
    }


    /**
     * 查询采购入库单行 修改订单时回显入库商品
     * @param recpt_id
     * @return
     */
    @GetMapping("/selectRecptLine_feedback/{recpt_id}")
    public TableDataInfo selectRecptLine_feedback( @PathVariable("recpt_id") Long recpt_id){
        return getDataTable(irls.selectRecptLine_feedback(recpt_id));
    }

    /**
     * 批量提交采购入库单
     * @param recpt_id
     * @return
     */
    @GetMapping("/submitReceipt/{recpt_id}")
    public UR submitReceipt( @PathVariable("recpt_id") Long recpt_id){
        if(irs.submitReceipt(recpt_id))
            return UR.ok("提交成功");
        else
            return UR.fail("提交失败");
    }

    /**
     * 入库
     * @param itemRecpt
     * @return
     */
    @PostMapping("/recptStore")
    public UR recptStore(@RequestBody CkItemRecpt itemRecpt){
        if(irs.goodsWarehousing(itemRecpt))
            return UR.ok("入库成功");
        else
            return UR.fail("入库失败");
    }

    /**
     * 查询待质检的入库单
     * @param recpt_coed
     * @param recpt_name
     * @return
     */
    @GetMapping("/selectCkProductRecptDetectSingle/{recpt_code}/{recpt_name}")
    public TableDataInfo selectCkProductRecptDetectSingle(@PathVariable("recpt_code") String recpt_coed,
                                               @PathVariable("recpt_name") String recpt_name){
        startPage();
        return getDataTable(irs.selectCkProductRecptDetectSingle(recpt_coed,recpt_name));
    }


    /**
     * 修改入库单状态 合格或不合格
     * @param recpt_id
     * @param status
     * @return
     */
    @GetMapping("/updateCkProductRecptStatusStatus/{recpt_id}/{status}")
    public UR updateCkProductRecptStatusStatus(@PathVariable("recpt_id") Long recpt_id,
                                               @PathVariable("status") Long status){
        if(irs.updateCkProductRecptStatusStatus(recpt_id,status))
            return UR.ok("修改成功");
        else
            return UR.fail("修改失败");
    }


















    /**
     * 根据批次ID查询批次信息
     * @param batchId
     * @return
     */
    @GetMapping("/getBatch/{batchId}")
    public UR getBatchById(@PathVariable Long batchId){
        CkBatch batch = cbs.selectBatchById(batchId);
        if (batch != null) {
            Map<String, Object> data = new HashMap<>();
            data.put("batch", batch);
            return UR.ok("查询成功", data);
        } else {
            return UR.fail("批次不存在");
        }
    }

    /**
     * 根据批次编号查询批次信息
     * @param batchCode
     * @return
     */
    @GetMapping("/getBatchByCode/{batchCode}")
    public UR getBatchByCode(@PathVariable String batchCode){
        CkBatch batch = cbs.selectBatchByCode(batchCode);
        if (batch != null) {
            Map<String, Object> data = new HashMap<>();
            data.put("batch", batch);
            return UR.ok("查询成功", data);
        } else {
            return UR.fail("批次不存在");
        }
    }

    /**
     * 根据工单ID查询相关批次列表
     * @param workOrderId
     * @return
     */
    @GetMapping("/getBatchByWorkOrder/{workOrderId}")
    public UR getBatchByWorkOrderId(@PathVariable Long workOrderId){
        List<CkBatch> batchList = cbs.selectBatchByWorkOrderId(workOrderId);
        List<Object> dataList = new ArrayList<>(batchList);
        return UR.ok("查询成功", dataList);
    }

    /**
     * 根据工单编号查询相关批次列表
     * @param workOrderCode
     * @return
     */
    @GetMapping("/getBatchByWorkOrderCode/{workOrderCode}")
    public UR getBatchByWorkOrderCode(@PathVariable String workOrderCode){
        List<CkBatch> batchList = cbs.selectBatchByWorkOrderCode(workOrderCode);
        List<Object> dataList = new ArrayList<>(batchList);
        return UR.ok("查询成功", dataList);
    }

    /**
     * 分页查询批次列表
     * @param batch
     * @return
     */
    @PostMapping("/findBatchList")
    public TableDataInfo findBatchList(CkBatch batch){
        startPage();
        return getDataTable(cbs.selectBatchList(batch));
    }

}
