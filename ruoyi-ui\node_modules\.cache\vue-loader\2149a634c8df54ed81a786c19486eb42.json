{"remainingRequest": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\src\\views\\sc\\plan\\edit_plan.vue?vue&type=style&index=0&id=b097a766&scoped=true&lang=css", "dependencies": [{"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\src\\views\\sc\\plan\\edit_plan.vue", "mtime": 1753786042780}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751945357942}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751945367667}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751945361393}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751945356000}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751945364156}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouZm9ybS1jb250YWluZXIgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOw0KICBwYWRkaW5nOiAxMHB4Ow0KfQ0KDQouZWwtdGFicy0tYm9yZGVyLWNhcmQgew0KICBib3gtc2hhZG93OiBub25lOw0KfQ0KDQoudXBsb2FkLWNvbnRhaW5lciB7DQogIHdpZHRoOiAxMDAlOw0KICBib3JkZXI6IDFweCBkYXNoZWQgI2Q5ZDlkOTsNCiAgYm9yZGVyLXJhZGl1czogNnB4Ow0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIHBhZGRpbmc6IDIwcHggMDsNCiAgY3Vyc29yOiBwb2ludGVyOw0KfQ0KDQoudXBsb2FkLWNvbnRhaW5lcjpob3ZlciB7DQogIGJvcmRlci1jb2xvcjogIzQwOUVGRjsNCn0NCg0KLnVwbG9hZC1hcmVhIHsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQp9DQoNCi51cGxvYWQtYXJlYSAuZWwtaWNvbi11cGxvYWQgew0KICBmb250LXNpemU6IDQ4cHg7DQogIGNvbG9yOiAjYzBjNGNjOw0KICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KfQ0KDQoudXBsb2FkLXRleHQgew0KICBmb250LXNpemU6IDE0cHg7DQogIGNvbG9yOiAjNjA2MjY2Ow0KICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KfQ0KDQoudXBsb2FkLWhpbnQgew0KICBmb250LXNpemU6IDEycHg7DQogIGNvbG9yOiAjOTA5Mzk5Ow0KfQ0KDQouaW5wdXQtd2l0aC1zZWxlY3QgLmVsLWlucHV0LWdyb3VwX19hcHBlbmQgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOw0KfQ0KDQouYm9tLXRpdGxlIHsNCiAgZm9udC1zaXplOiAxNnB4Ow0KICBmb250LXdlaWdodDogYm9sZDsNCiAgY29sb3I6ICMzMDMxMzM7DQp9DQoNCi5ib20tY29udGFpbmVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIG1hcmdpbjogMjBweCAwOw0KICBwYWRkaW5nOiAzMHB4IDA7DQp9DQoNCi5mb2xkZXItaWNvbiB7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQp9DQoNCi5ib20tdGV4dCB7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgY29sb3I6ICM5MDkzOTk7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQp9DQoNCi53YXJuaW5nLXRleHQgew0KICBjb2xvcjogI0U2QTIzQzsNCiAgZm9udC1zaXplOiAxMnB4Ow0KICBtYXJnaW4tbGVmdDogMTBweDsNCn0NCg0KLndhcm5pbmctdGV4dCBpIHsNCiAgbWFyZ2luLXJpZ2h0OiA1cHg7DQp9DQoNCi51cGxvYWQtaGlkZGVuIHsNCiAgd2lkdGg6IDEwMCU7DQogIGhlaWdodDogMTAwJTsNCn0NCg0KLnVwbG9hZC1oaWRkZW4gPj4+IC5lbC11cGxvYWQgew0KICB3aWR0aDogMTAwJTsNCn0NCg0KLnVwbG9hZC1oaWRkZW4gPj4+IC5lbC11cGxvYWQtZHJhZ2dlciB7DQogIHdpZHRoOiAxMDAlOw0KICBoZWlnaHQ6IDEwMCU7DQogIGJvcmRlcjogbm9uZTsNCiAgcGFkZGluZzogMDsNCiAgbWFyZ2luOiAwOw0KfQ0KDQouYm9tLWRpYWxvZy1oZWFkZXIgew0KICBtYXJnaW4tYm90dG9tOiAxNXB4Ow0KICBwYWRkaW5nOiAxMHB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmN2ZhOw0KICBib3JkZXItcmFkaXVzOiA0cHg7DQp9DQoNCi5wcm9kdWN0LWluZm8gew0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LXdyYXA6IHdyYXA7DQp9DQoNCi5wcm9kdWN0LWluZm8gc3BhbiB7DQogIG1hcmdpbi1yaWdodDogMjBweDsNCiAgbGluZS1oZWlnaHQ6IDMwcHg7DQp9DQoNCi5lbC1yYWRpbyB7DQogIG1hcmdpbi1yaWdodDogMDsNCn0NCg0KLnBhZ2luYXRpb24tY29udGFpbmVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBwYWRkaW5nOiAxMHB4IDA7DQogIGZvbnQtc2l6ZTogMTJweDsNCn0NCg0KLnBhZ2luYXRpb24td3JhcHBlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQp9DQoNCi5wYWdlLXNpemUgew0KICBtYXJnaW4tcmlnaHQ6IDEwcHg7DQp9DQoNCi50b3RhbC10ZXh0IHsNCiAgY29sb3I6ICM2MDYyNjY7DQogIGZvbnQtc2l6ZTogMTJweDsNCn0NCg0KLmJvbS1pbmZvIHsNCiAgd2lkdGg6IDEwMCU7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQp9DQoNCi5ib20taGVhZGVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KICBwYWRkaW5nOiA4cHg7DQogIGJhY2tncm91bmQtY29sb3I6ICNmNWY3ZmE7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCn0NCg0KLmJvbS10aXRsZS1pbmZvIHsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC13cmFwOiB3cmFwOw0KfQ0KDQouYm9tLXRpdGxlLWluZm8gc3BhbiB7DQogIG1hcmdpbi1yaWdodDogMjBweDsNCiAgZm9udC13ZWlnaHQ6IGJvbGQ7DQp9DQoNCi5ib20tZGV0YWlsLXRhYmxlIHsNCiAgbWFyZ2luLWJvdHRvbTogMTVweDsNCn0NCg0KLmJvbS1hY3Rpb24gew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KfQ0KDQouc2VsZWN0LWJvbS1idXR0b24gew0KICBtYXJnaW4tcmlnaHQ6IDEwcHg7DQp9DQo="}, {"version": 3, "sources": ["edit_plan.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA05BA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "edit_plan.vue", "sourceRoot": "src/views/sc/plan", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"form-container\">\r\n      <!-- 基础信息区 -->\r\n      <el-tabs type=\"border-card\">\r\n        <el-tab-pane>\r\n          <span slot=\"label\"><i class=\"el-icon-date\"></i> 基础信息</span>\r\n          <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"8\">\r\n                <el-form-item label=\"计划编号\" prop=\"planCode\" required>\r\n                  <el-input v-model=\"form.planCode\" placeholder=\"请输入\" :disabled=\"isSystemCode\"></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"4\">\r\n                <el-switch\r\n                  v-model=\"isSystemCode\"\r\n                  active-text=\"系统编号\"\r\n                  inactive-text=\"\"\r\n                  style=\"margin-top: 13px;\"\r\n                  @change=\"handleSystemCodeChange\"\r\n                ></el-switch>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"计划名称\" prop=\"planName\" required>\r\n                  <el-input v-model=\"form.planName\" placeholder=\"请输入\"></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"来源类型\" prop=\"sourceType\" required>\r\n                  <el-select v-model=\"form.sourceType\" placeholder=\"销售订单\" style=\"width: 100%\">\r\n                    <el-option\r\n                      v-for=\"item in sourceTypeOptions\"\r\n                      :key=\"item.dictValue\"\r\n                      :label=\"item.dictLabel\"\r\n                      :value=\"item.dictValue\"\r\n                    ></el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"订单编号\" prop=\"orderCode\">\r\n                  <el-input v-model=\"form.orderCode\" placeholder=\"请输入\"></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"成品名称\" prop=\"productName\">\r\n                  <el-input\r\n                    placeholder=\"请选择成品\"\r\n                    v-model=\"form.productName\"\r\n                    class=\"input-with-select\"\r\n                  >\r\n                    <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"openProductSelection\"></el-button>\r\n                  </el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"成品编号\" prop=\"productCode\">\r\n                  <el-input v-model=\"form.productCode\" placeholder=\"请输入\" disabled></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"规格型号\" prop=\"specification\">\r\n                  <el-input v-model=\"form.specification\" placeholder=\"请输入\" disabled></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"成品类型\" prop=\"productType\">\r\n                  <dict-tag :options=\"dict.type.product_type\" :value=\"form.productType\" />\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"单位\" prop=\"unit\">\r\n                  <el-input v-model=\"form.unit\" placeholder=\"请输入\" disabled></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"计划数量\" prop=\"plannedQty\" required>\r\n                  <el-input-number v-model=\"form.plannedQty\" :min=\"1\" controls-position=\"right\" style=\"width: 100%\"></el-input-number>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"开工时间\" prop=\"planStartTime\">\r\n                  <el-date-picker\r\n                    v-model=\"form.planStartTime\"\r\n                    type=\"date\"\r\n                    placeholder=\"请选择日期\"\r\n                    style=\"width: 100%\"\r\n                    value-format=\"yyyy-MM-dd\"\r\n                  ></el-date-picker>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"完工时间\" prop=\"planEndTime\">\r\n                  <el-date-picker\r\n                    v-model=\"form.planEndTime\"\r\n                    type=\"date\"\r\n                    placeholder=\"请选择日期\"\r\n                    style=\"width: 100%\"\r\n                    value-format=\"yyyy-MM-dd\"\r\n                  ></el-date-picker>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"需求日期\" prop=\"requiredDate\">\r\n                  <el-date-picker\r\n                    v-model=\"form.requiredDate\"\r\n                    type=\"date\"\r\n                    placeholder=\"请选择日期\"\r\n                    style=\"width: 100%\"\r\n                    value-format=\"yyyy-MM-dd\"\r\n                  ></el-date-picker>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"备注\" prop=\"remark\">\r\n                  <el-input\r\n                    type=\"textarea\"\r\n                    v-model=\"form.remark\"\r\n                    placeholder=\"请输入\"\r\n                    :rows=\"4\"\r\n                  ></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"附件\" prop=\"attachment\">\r\n                  <div class=\"upload-container\" @click=\"triggerUpload\">\r\n                    <el-upload\r\n                      ref=\"upload\"\r\n                      class=\"upload-hidden\"\r\n                      action=\"#\"\r\n                      :http-request=\"uploadFile\"\r\n                      :file-list=\"fileList\"\r\n                      :before-upload=\"beforeUpload\"\r\n                      :on-remove=\"handleRemove\"\r\n                      multiple\r\n                      drag\r\n                    >\r\n                      <div class=\"upload-area\">\r\n                        <i class=\"el-icon-upload\"></i>\r\n                        <div class=\"upload-text\">点击或者拖动文件到虚线框内上传</div>\r\n                        <div class=\"upload-hint\">支持 docx, xls, PDF, rar, zip, PNG, JPG 等类型的文件</div>\r\n                      </div>\r\n                    </el-upload>\r\n                  </div>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-divider>\r\n              <span class=\"bom-title\">BOM组成</span>\r\n            </el-divider>\r\n            \r\n            <el-row>\r\n              <el-col :span=\"24\">\r\n                <div class=\"bom-container\">\r\n                  <div class=\"folder-icon\" v-if=\"!selectedBom\">\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"60\" height=\"60\" viewBox=\"0 0 24 24\" fill=\"#DCDFE6\">\r\n                      <path d=\"M10 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2h-8l-2-2z\"/>\r\n                    </svg>\r\n                  </div>\r\n                  <div class=\"bom-text\" v-if=\"!selectedBom\">暂无数据</div>\r\n                  <div class=\"bom-info\" v-else>\r\n                    <div class=\"bom-header\">\r\n                      <div class=\"bom-title-info\">\r\n                        <span>BOM编号：{{ selectedBom.bom_code }}</span>\r\n                        <span>版本号：{{ selectedBom.bom_version }}</span>\r\n                      </div>\r\n                      <el-button type=\"text\" icon=\"el-icon-delete\" @click=\"clearSelectedBom\">清除</el-button>\r\n                    </div>\r\n                    <el-table\r\n                      :data=\"bomDetailList\"\r\n                      border\r\n                      size=\"small\"\r\n                      style=\"width: 100%\"\r\n                      class=\"bom-detail-table\"\r\n                    >\r\n                      <el-table-column label=\"序号\" type=\"index\" width=\"50\" align=\"center\"></el-table-column>\r\n                      <el-table-column prop=\"material_code\" label=\"物料编码\" min-width=\"120\" align=\"center\"></el-table-column>\r\n                      <el-table-column prop=\"material_name\" label=\"物料名称\" min-width=\"150\" align=\"center\"></el-table-column>\r\n                      <el-table-column prop=\"specification\" label=\"规格型号\" min-width=\"100\" align=\"center\"></el-table-column>\r\n                      <el-table-column prop=\"unit\" label=\"单位\" width=\"60\" align=\"center\"></el-table-column>\r\n                      <el-table-column prop=\"quantity\" label=\"用量\" width=\"80\" align=\"center\"></el-table-column>\r\n                      <el-table-column prop=\"remark\" label=\"备注\" min-width=\"120\" align=\"center\"></el-table-column>\r\n                    </el-table>\r\n                  </div>\r\n                  <div class=\"bom-action\">\r\n                    <el-tooltip :disabled=\"form.productId\" content=\"请先选择成品!\" placement=\"right\" effect=\"light\">\r\n                      <el-button type=\"primary\" class=\"select-bom-button\" @click=\"selectBom\">选择BOM</el-button>\r\n                    </el-tooltip>\r\n                  </div>\r\n                </div>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row>\r\n              <el-col :span=\"24\" style=\"text-align: center; margin-top: 20px;\">\r\n                <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n                <el-button @click=\"cancel\">取 消</el-button>\r\n              </el-col>\r\n            </el-row>\r\n          </el-form>\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n    </div>\r\n    \r\n    <!-- 产品选择对话框 -->\r\n    <el-dialog title=\"选择成品\" :visible.sync=\"productDialogVisible\" width=\"40%\" append-to-body>\r\n      <el-form :model=\"productQuery\" ref=\"productQueryForm\" :inline=\"true\" class=\"demo-form-inline\" size=\"small\">\r\n        <el-form-item>\r\n          <el-input v-model=\"productQuery.keyword\" placeholder=\"请输入产品编号/名称\" clearable style=\"width: 180px;\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-select v-model=\"productQuery.unit\" placeholder=\"请选择单位\" clearable style=\"width: 120px;\">\r\n            <el-option label=\"个\" value=\"个\"></el-option>\r\n            <el-option label=\"件\" value=\"件\"></el-option>\r\n            <el-option label=\"台\" value=\"台\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-select v-model=\"productQuery.type\" placeholder=\"请选择类型\" clearable style=\"width: 120px;\">\r\n            <el-option label=\"成品\" value=\"成品\"></el-option>\r\n            <el-option label=\"半成品\" value=\"半成品\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-select v-model=\"productQuery.property\" placeholder=\"请选择产品属性\" clearable style=\"width: 120px;\">\r\n            <el-option label=\"自制\" value=\"自制\"></el-option>\r\n            <el-option label=\"外购\" value=\"外购\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"small\" @click=\"searchProducts\">查询</el-button>\r\n          <el-button icon=\"el-icon-refresh\" size=\"small\" @click=\"resetProductQuery\">重置</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n      \r\n      <el-table\r\n        v-loading=\"productLoading\"\r\n        :data=\"productList\"\r\n        border\r\n        size=\"small\"\r\n        style=\"width: 100%\"\r\n        @selection-change=\"handleProductSelectionChange\"\r\n        height=\"300\"\r\n      >\r\n        <el-table-column type=\"selection\" width=\"40\" align=\"center\"></el-table-column>\r\n        <el-table-column label=\"序号\" type=\"index\" width=\"40\" align=\"center\"></el-table-column>\r\n        <el-table-column prop=\"product_code\" label=\"产品编号\" width=\"90\" align=\"center\"></el-table-column>\r\n        <el-table-column prop=\"product_name\" label=\"产品名称\" min-width=\"120\" align=\"center\"></el-table-column>\r\n        <el-table-column prop=\"product_sfn\" label=\"规格型号\" min-width=\"90\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <span :style=\"{color: '#1890ff'}\">{{ scope.row.product_sfn }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"product_unit\" label=\"单位\" width=\"60\" align=\"center\"></el-table-column>\r\n        <el-table-column prop=\"product_type\" label=\"产品类型\" width=\"70\" align=\"center\" :formatter=\"formatProductType\"></el-table-column>\r\n      </el-table>\r\n      \r\n      <div class=\"pagination-container\">\r\n        <span class=\"total-text\">共 {{ productTotal }} 条</span>\r\n        <div class=\"pagination-wrapper\">\r\n          <span class=\"page-size\">\r\n            <el-select v-model=\"productQuery.pageSize\" size=\"mini\" @change=\"handleProductSizeChange\" style=\"width: 80px;\">\r\n              <el-option\r\n                v-for=\"item in [10, 20, 30, 50]\"\r\n                :key=\"item\"\r\n                :label=\"`${item}条/页`\"\r\n                :value=\"item\">\r\n              </el-option>\r\n            </el-select>\r\n          </span>\r\n          <el-pagination\r\n            small\r\n            background\r\n            @current-change=\"handleProductCurrentChange\"\r\n            :current-page=\"productQuery.pageNum\"\r\n            :page-size=\"productQuery.pageSize\"\r\n            layout=\"prev, pager, next, jumper\"\r\n            :pager-count=\"5\"\r\n            :total=\"productTotal\">\r\n          </el-pagination>\r\n        </div>\r\n      </div>\r\n      \r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button size=\"small\" @click=\"productDialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" size=\"small\" @click=\"confirmProductSelect\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    \r\n    <!-- BOM选择对话框 -->\r\n    <el-dialog title=\"选择BOM\" :visible.sync=\"bomDialogVisible\" width=\"40%\" append-to-body>\r\n      <div class=\"bom-dialog-header\">\r\n        <div class=\"product-info\">\r\n          <span>产品名称：{{ form.productName }}</span>\r\n          <span>产品编号：{{ form.productCode }}</span>\r\n          <span>规格型号：{{ form.specification }}</span>\r\n          <span>单位：{{ form.unit }}</span>\r\n        </div>\r\n      </div>\r\n      \r\n      <el-table\r\n        v-loading=\"bomLoading\"\r\n        :data=\"bomList\"\r\n        border\r\n        style=\"width: 100%\"\r\n        @row-click=\"handleBomSelect\"\r\n        highlight-current-row\r\n        size=\"small\"\r\n        height=\"300\"\r\n      >\r\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-radio v-model=\"selectedBomId\" :label=\"scope.row.bom_id\" @change=\"handleBomSelect(scope.row)\">&nbsp;</el-radio>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"序号\" type=\"index\" width=\"55\" align=\"center\"></el-table-column>\r\n        <el-table-column prop=\"bom_code\" label=\"BOM编号\" min-width=\"150\" align=\"center\"></el-table-column>\r\n        <el-table-column prop=\"bom_version\" label=\"版本号\" width=\"100\" align=\"center\"></el-table-column>\r\n        <el-table-column prop=\"bom_status\" label=\"默认BOM\" width=\"100\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ scope.row.bom_status === '1' ? '是' : '否' }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"bom_output\" label=\"日产量\" width=\"100\" align=\"center\"></el-table-column>\r\n      </el-table>\r\n      \r\n      <pagination\r\n        v-show=\"bomTotal > 0\"\r\n        :total=\"bomTotal\"\r\n        :page.sync=\"bomQuery.pageNum\"\r\n        :limit.sync=\"bomQuery.pageSize\"\r\n        @pagination=\"getBomList\"\r\n      />\r\n      \r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"bomDialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmBomSelect\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getProductionPlan, updateProductionPlan, addProductionPlan } from \"@/api/sc/productionPlan\";\r\nimport { listProducts, listBomsByProductId, findBomDetails } from \"@/api/basic/product\";\r\nimport { getAutoNumbers } from \"@/api/basic/numbers\";\r\nimport Pagination from \"@/components/Pagination\";\r\nimport DictTag from \"@/components/DictTag\";\r\n\r\nexport default {\r\n  name: \"EditPlan\",\r\n  dicts: ['product_type'],\r\n  components: {\r\n    Pagination,\r\n    DictTag\r\n  },\r\n  data() {\r\n    return {\r\n      // 页面标题\r\n      title: \"修改生产计划\",\r\n      // 是否为修改模式\r\n      isEdit: true,\r\n      // 表单数据\r\n      form: {\r\n        planCode: \"\",\r\n        planName: \"\",\r\n        sourceType: \"销售订单\",\r\n        orderCode: \"\",\r\n        productId: undefined,\r\n        productName: \"\",\r\n        productCode: \"\",\r\n        specification: \"\",\r\n        productType: \"\",\r\n        unit: \"\",\r\n        plannedQty: 1,\r\n        planStartTime: \"\",\r\n        planEndTime: \"\",\r\n        requiredDate: \"\",\r\n        remark: \"\"\r\n      },\r\n      // 是否使用系统编号\r\n      isSystemCode: true,\r\n      // 表单验证规则\r\n      rules: {\r\n        planName: [\r\n          { required: true, message: \"计划名称不能为空\", trigger: \"blur\" },\r\n          { max: 50, message: \"长度不能超过50个字符\", trigger: \"blur\" }\r\n        ],\r\n        sourceType: [\r\n          { required: true, message: \"来源类型不能为空\", trigger: \"change\" }\r\n        ],\r\n        productName: [\r\n          { required: true, message: \"请选择产品\", trigger: \"change\" }\r\n        ],\r\n        plannedQty: [\r\n          { required: true, message: \"计划数量不能为空\", trigger: \"blur\" }\r\n        ],\r\n        planStartTime: [\r\n          { validator: this.validateStartTime, trigger: \"change\" }\r\n        ],\r\n        planEndTime: [\r\n          { validator: this.validateEndTime, trigger: \"change\" }\r\n        ],\r\n        requiredDate: [\r\n          { validator: this.validateRequiredDate, trigger: \"change\" }\r\n        ]\r\n      },\r\n      // 产品下拉选项\r\n      productOptions: [],\r\n      // 产品加载状态\r\n      productLoading: false,\r\n      // 来源类型选项\r\n      sourceTypeOptions: [\r\n        { dictLabel: \"销售订单\", dictValue: \"销售订单\" },\r\n        { dictLabel: \"库存备货\", dictValue: \"库存备货\" }\r\n      ],\r\n      // 上传文件列表\r\n      fileList: [],\r\n      \r\n      // 产品选择对话框\r\n      productDialogVisible: false,\r\n      productQuery: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        keyword: \"\",\r\n        unit: \"\",\r\n        type: \"\",\r\n        property: \"\"\r\n      },\r\n      productList: [],\r\n      productTotal: 0,\r\n      selectedProduct: null,\r\n      \r\n      // BOM选择对话框\r\n      bomDialogVisible: false,\r\n      bomQuery: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        productId: undefined\r\n      },\r\n      bomList: [],\r\n      bomTotal: 0,\r\n      bomLoading: false,\r\n      selectedBom: null,\r\n      selectedBomId: null,\r\n      bomDetailList: [],\r\n    };\r\n  },\r\n  created() {\r\n    const planCode = this.$route.query.planCode;\r\n    if (planCode) {\r\n      this.isEdit = true;\r\n      this.title = \"修改生产计划\";\r\n      this.getPlanData(planCode);\r\n    } else {\r\n      this.isEdit = false;\r\n      this.title = \"新增生产计划\";\r\n      this.generatePlanCode();\r\n    }\r\n  },\r\n  methods: {\r\n    // 获取计划数据\r\n    getPlanData(planCode) {\r\n      getProductionPlan(planCode).then(response => {\r\n        this.form = response.data;\r\n        // 如果有关联的产品，则自动加载其BOM信息\r\n        if (this.form.productId) {\r\n          this.loadAssociatedBom();\r\n        }\r\n      });\r\n    },\r\n\r\n    // 加载关联的BOM\r\n    loadAssociatedBom() {\r\n      listBomsByProductId(this.form.productId).then(response => {\r\n        if (response.code === 200 && response.rows && response.rows.length > 0) {\r\n          // 查找默认的BOM (status '1')\r\n          const activeBom = response.rows.find(b => b.bom_status === '1');\r\n          if (activeBom) {\r\n            this.selectedBom = activeBom;\r\n            this.getBomDetail(); // 加载BOM详情\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    // 生成计划编号\r\n    generatePlanCode() {\r\n      getAutoNumbers(6).then(response => {\r\n        if (response.code === 200) {\r\n          this.form.planCode = response.msg;\r\n        } else {\r\n          this.$message.error('获取计划编号失败');\r\n        }\r\n      }).catch(() => {\r\n        this.$message.error('获取计划编号失败');\r\n      });\r\n    },\r\n    \r\n    // 处理系统编号开关变化\r\n    handleSystemCodeChange(val) {\r\n      if (val) {\r\n        // 如果开启系统编号，则生成编号\r\n        this.generatePlanCode();\r\n      } else {\r\n        // 如果关闭系统编号，则清空编号\r\n        this.form.planCode = '';\r\n      }\r\n    },\r\n    \r\n    // 触发上传\r\n    triggerUpload() {\r\n      this.$refs.upload.$el.click();\r\n    },\r\n    \r\n    // 打开产品选择弹窗\r\n    openProductSelection() {\r\n      // 修改模式下不允许更换产品\r\n      if (this.isEdit) {\r\n        this.$message.warning(\"修改模式下不允许更换产品。\");\r\n        return;\r\n      }\r\n      this.productDialogVisible = true;\r\n      this.getProductList();\r\n    },\r\n    \r\n    // 获取产品列表\r\n    getProductList() {\r\n      this.productLoading = true;\r\n      listProducts({\r\n        pageNum: this.productQuery.pageNum,\r\n        pageSize: this.productQuery.pageSize,\r\n        keyword: this.productQuery.keyword,\r\n        productUnit: this.productQuery.unit,\r\n        productType: this.productQuery.type,\r\n        productProperty: this.productQuery.property\r\n      }).then(response => {\r\n        this.productLoading = false;\r\n        if (response.code === 200) {\r\n          this.productList = response.rows;\r\n          this.productTotal = response.total;\r\n        }\r\n      }).catch(() => {\r\n        this.productLoading = false;\r\n        // 模拟数据\r\n        this.productList = [\r\n          { product_id: 1, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' },\r\n          { product_id: 2, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' },\r\n          { product_id: 3, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' },\r\n          { product_id: 4, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' },\r\n          { product_id: 5, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' }\r\n        ];\r\n        this.productTotal = 50;\r\n      });\r\n    },\r\n    \r\n    // 搜索产品\r\n    searchProducts() {\r\n      this.productQuery.pageNum = 1;\r\n      this.getProductList();\r\n    },\r\n    \r\n    // 重置产品查询条件\r\n    resetProductQuery() {\r\n      this.productQuery = {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        keyword: \"\",\r\n        unit: \"\",\r\n        type: \"\",\r\n        property: \"\"\r\n      };\r\n      this.getProductList();\r\n    },\r\n    \r\n    // 处理产品表格选择变化\r\n    handleProductSelectionChange(selection) {\r\n      if (selection.length > 0) {\r\n        this.selectedProduct = selection[0];\r\n      } else {\r\n        this.selectedProduct = null;\r\n      }\r\n    },\r\n    \r\n    // 处理产品页码变化\r\n    handleProductCurrentChange(currentPage) {\r\n      this.productQuery.pageNum = currentPage;\r\n      this.getProductList();\r\n    },\r\n    \r\n    // 处理产品每页条数变化\r\n    handleProductSizeChange(size) {\r\n      this.productQuery.pageSize = size;\r\n      this.productQuery.pageNum = 1;\r\n      this.getProductList();\r\n    },\r\n    \r\n    // 确认产品选择\r\n    confirmProductSelect() {\r\n      if (this.selectedProduct) {\r\n        this.form.productId = this.selectedProduct.product_id;\r\n        this.form.productName = this.selectedProduct.product_name;\r\n        this.form.productCode = this.selectedProduct.product_code;\r\n        this.form.specification = this.selectedProduct.product_sfn;\r\n        this.form.productType = this.formatProductType(this.selectedProduct);\r\n        this.form.unit = this.selectedProduct.product_unit;\r\n        this.productDialogVisible = false;\r\n        \r\n        // 清空已选BOM\r\n        this.selectedBom = null;\r\n        this.selectedBomId = null;\r\n      } else {\r\n        this.$message.warning('请选择一个产品！');\r\n      }\r\n    },\r\n    \r\n    // 格式化产品类型\r\n    formatProductType(row, column) {\r\n      const type = row.product_type;\r\n      const option = this.productTypeOptions.find(item => item.dictValue == type);\r\n      return option ? option.dictLabel : type;\r\n    },\r\n    \r\n    // 选择BOM\r\n    selectBom() {\r\n      if (!this.form.productId) {\r\n        this.$message.warning('请先选择成品！');\r\n        return;\r\n      }\r\n      this.bomDialogVisible = true;\r\n      this.getBomList();\r\n    },\r\n    \r\n    // 获取BOM列表\r\n    getBomList() {\r\n      this.bomLoading = true;\r\n      listBomsByProductId(this.form.productId).then(response => {\r\n        this.bomLoading = false;\r\n        if (response.code === 200) {\r\n          this.bomList = response.rows;\r\n          this.bomTotal = response.total;\r\n          if (!this.bomList || this.bomList.length === 0) {\r\n            this.$message.info(\"未找到该产品的BOM信息\");\r\n          } else {\r\n            // 如果有默认BOM，则自动选中\r\n            const defaultBom = this.bomList.find(b => b.bom_status === '1');\r\n            if (defaultBom) {\r\n              this.handleBomSelect(defaultBom);\r\n            }\r\n          }\r\n        } else {\r\n          this.bomList = [];\r\n          this.bomTotal = 0;\r\n        }\r\n      }).catch(() => {\r\n        this.bomLoading = false;\r\n        this.$message.error('获取BOM列表失败');\r\n      });\r\n    },\r\n    \r\n    // 处理BOM行选择\r\n    handleBomSelect(row) {\r\n      this.selectedBom = row;\r\n      this.selectedBomId = row.bom_id;\r\n    },\r\n    \r\n    // 确认BOM选择\r\n    confirmBomSelect() {\r\n      if (this.selectedBom) {\r\n        this.bomDialogVisible = false;\r\n        // 获取BOM详情\r\n        this.getBomDetail();\r\n      } else {\r\n        this.$message.warning('请选择一个BOM！');\r\n      }\r\n    },\r\n    \r\n    // 获取BOM详情\r\n    getBomDetail() {\r\n      findBomDetails(this.selectedBom.bom_id).then(response => {\r\n        console.log(\"成功获取BOM详情响应:\", response);\r\n        if (response && response.code === 200) {\r\n          this.bomDetailList = response.rows;\r\n        } else {\r\n          this.bomDetailList = [];\r\n          this.$message.error(\"获取BOM详情失败: \" + (response ? response.msg : '无响应'));\r\n        }\r\n      }).catch(error => {\r\n        console.error(\"获取BOM详情接口调用失败:\", error);\r\n        this.$message.error(\"获取BOM详情接口调用失败\");\r\n        this.bomDetailList = [];\r\n      });\r\n    },\r\n    \r\n    // 清除已选BOM\r\n    clearSelectedBom() {\r\n      this.selectedBom = null;\r\n      this.selectedBomId = null;\r\n      this.bomDetailList = [];\r\n    },\r\n    \r\n    // 上传前检查文件类型和大小\r\n    beforeUpload(file) {\r\n      const isValidType = /\\.(doc|docx|xls|xlsx|pdf|rar|zip|png|jpg|jpeg)$/i.test(file.name);\r\n      const isLt10M = file.size / 1024 / 1024 < 10;\r\n\r\n      if (!isValidType) {\r\n        this.$message.error('上传文件格式不支持!');\r\n        return false;\r\n      }\r\n      if (!isLt10M) {\r\n        this.$message.error('上传文件大小不能超过 10MB!');\r\n        return false;\r\n      }\r\n      return true;\r\n    },\r\n    \r\n    // 上传文件处理\r\n    uploadFile(options) {\r\n      // 这里应该调用实际的文件上传API\r\n      console.log('文件上传:', options.file);\r\n      // 假设上传成功\r\n      this.fileList.push({\r\n        name: options.file.name,\r\n        url: URL.createObjectURL(options.file)\r\n      });\r\n      options.onSuccess();\r\n    },\r\n    \r\n    // 移除文件\r\n    handleRemove(file) {\r\n      const index = this.fileList.indexOf(file);\r\n      if (index !== -1) {\r\n        this.fileList.splice(index, 1);\r\n      }\r\n    },\r\n    \r\n    // 表单提交\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          // 额外的日期逻辑验证\r\n          if (!this.validateDateLogic()) {\r\n            return;\r\n          }\r\n\r\n          // 根据isEdit标志决定调用哪个API\r\n          const apiCall = this.isEdit ? updateProductionPlan : addProductionPlan;\r\n          \r\n          apiCall(this.form).then(response => {\r\n            if (response.code === 200) {\r\n              this.$modal.msgSuccess(this.isEdit ? \"修改成功\" : \"新增成功\");\r\n              this.cancel();\r\n            } else {\r\n              this.$modal.msgError(response.msg || (this.isEdit ? \"修改失败\" : \"新增失败\"));\r\n            }\r\n          }).catch(() => {\r\n            this.$modal.msgError(\"操作失败，请稍后重试\");\r\n          });\r\n        }\r\n      });\r\n    },\r\n    \r\n    // 取消按钮\r\n    cancel() {\r\n      this.$router.push({ path: \"/sc/plan\" });\r\n    },\r\n\r\n    // 验证开工时间\r\n    validateStartTime(rule, value, callback) {\r\n      if (!value) {\r\n        callback();\r\n        return;\r\n      }\r\n\r\n      const today = new Date();\r\n      today.setHours(0, 0, 0, 0);\r\n      const startDate = new Date(value);\r\n\r\n      // 开工日期不能早于当前日期\r\n      if (startDate < today) {\r\n        callback(new Error('开工日期不能早于当前日期'));\r\n        return;\r\n      }\r\n\r\n      // 如果完工时间已选择，开工时间不能晚于完工时间\r\n      if (this.form.planEndTime) {\r\n        const endDate = new Date(this.form.planEndTime);\r\n        if (startDate > endDate) {\r\n          callback(new Error('开工日期不能晚于完工日期'));\r\n          return;\r\n        }\r\n      }\r\n\r\n      callback();\r\n    },\r\n\r\n    // 验证完工时间\r\n    validateEndTime(rule, value, callback) {\r\n      if (!value) {\r\n        callback();\r\n        return;\r\n      }\r\n\r\n      const endDate = new Date(value);\r\n\r\n      // 如果开工时间已选择，完工时间不能早于开工时间\r\n      if (this.form.planStartTime) {\r\n        const startDate = new Date(this.form.planStartTime);\r\n        if (endDate < startDate) {\r\n          callback(new Error('完工日期不能早于开工日期'));\r\n          return;\r\n        }\r\n      }\r\n\r\n      callback();\r\n    },\r\n\r\n    // 验证需求日期\r\n    validateRequiredDate(rule, value, callback) {\r\n      if (!value) {\r\n        callback();\r\n        return;\r\n      }\r\n\r\n      const requiredDate = new Date(value);\r\n\r\n      // 需求日期不能早于当前日期\r\n      const today = new Date();\r\n      today.setHours(0, 0, 0, 0);\r\n      if (requiredDate < today) {\r\n        callback(new Error('需求日期不能早于当前日期'));\r\n        return;\r\n      }\r\n\r\n      // 如果完工时间已选择，需求日期不能早于完工时间\r\n      if (this.form.planEndTime) {\r\n        const endDate = new Date(this.form.planEndTime);\r\n        if (requiredDate < endDate) {\r\n          callback(new Error('需求日期不能早于完工日期'));\r\n          return;\r\n        }\r\n      }\r\n\r\n      callback();\r\n    },\r\n\r\n    // 综合日期逻辑验证\r\n    validateDateLogic() {\r\n      const today = new Date();\r\n      today.setHours(0, 0, 0, 0);\r\n\r\n      // 检查开工日期\r\n      if (this.form.planStartTime) {\r\n        const startDate = new Date(this.form.planStartTime);\r\n        if (startDate < today) {\r\n          this.$modal.msgError(\"开工日期不能早于当前日期\");\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 检查开工日期和完工日期的关系\r\n      if (this.form.planStartTime && this.form.planEndTime) {\r\n        const startDate = new Date(this.form.planStartTime);\r\n        const endDate = new Date(this.form.planEndTime);\r\n        if (startDate > endDate) {\r\n          this.$modal.msgError(\"开工日期不能晚于完工日期\");\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 检查需求日期\r\n      if (this.form.requiredDate) {\r\n        const requiredDate = new Date(this.form.requiredDate);\r\n        if (requiredDate < today) {\r\n          this.$modal.msgError(\"需求日期不能早于当前日期\");\r\n          return false;\r\n        }\r\n\r\n        // 需求日期不能早于完工日期\r\n        if (this.form.planEndTime) {\r\n          const endDate = new Date(this.form.planEndTime);\r\n          if (requiredDate < endDate) {\r\n            this.$modal.msgError(\"需求日期不能早于完工日期\");\r\n            return false;\r\n          }\r\n        }\r\n      }\r\n\r\n      return true;\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.form-container {\r\n  background-color: #fff;\r\n  padding: 10px;\r\n}\r\n\r\n.el-tabs--border-card {\r\n  box-shadow: none;\r\n}\r\n\r\n.upload-container {\r\n  width: 100%;\r\n  border: 1px dashed #d9d9d9;\r\n  border-radius: 6px;\r\n  text-align: center;\r\n  padding: 20px 0;\r\n  cursor: pointer;\r\n}\r\n\r\n.upload-container:hover {\r\n  border-color: #409EFF;\r\n}\r\n\r\n.upload-area {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.upload-area .el-icon-upload {\r\n  font-size: 48px;\r\n  color: #c0c4cc;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.upload-text {\r\n  font-size: 14px;\r\n  color: #606266;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.upload-hint {\r\n  font-size: 12px;\r\n  color: #909399;\r\n}\r\n\r\n.input-with-select .el-input-group__append {\r\n  background-color: #fff;\r\n}\r\n\r\n.bom-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.bom-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin: 20px 0;\r\n  padding: 30px 0;\r\n}\r\n\r\n.folder-icon {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.bom-text {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.warning-text {\r\n  color: #E6A23C;\r\n  font-size: 12px;\r\n  margin-left: 10px;\r\n}\r\n\r\n.warning-text i {\r\n  margin-right: 5px;\r\n}\r\n\r\n.upload-hidden {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.upload-hidden >>> .el-upload {\r\n  width: 100%;\r\n}\r\n\r\n.upload-hidden >>> .el-upload-dragger {\r\n  width: 100%;\r\n  height: 100%;\r\n  border: none;\r\n  padding: 0;\r\n  margin: 0;\r\n}\r\n\r\n.bom-dialog-header {\r\n  margin-bottom: 15px;\r\n  padding: 10px;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n}\r\n\r\n.product-info {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.product-info span {\r\n  margin-right: 20px;\r\n  line-height: 30px;\r\n}\r\n\r\n.el-radio {\r\n  margin-right: 0;\r\n}\r\n\r\n.pagination-container {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 10px 0;\r\n  font-size: 12px;\r\n}\r\n\r\n.pagination-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.page-size {\r\n  margin-right: 10px;\r\n}\r\n\r\n.total-text {\r\n  color: #606266;\r\n  font-size: 12px;\r\n}\r\n\r\n.bom-info {\r\n  width: 100%;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.bom-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n  padding: 8px;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n}\r\n\r\n.bom-title-info {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.bom-title-info span {\r\n  margin-right: 20px;\r\n  font-weight: bold;\r\n}\r\n\r\n.bom-detail-table {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.bom-action {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.select-bom-button {\r\n  margin-right: 10px;\r\n}\r\n</style>\r\n"]}]}