package com.ldzl.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 采购到货通知单
 */
@Data
public class ArrivalNoticeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单详情ID
     */
    private Long line_id;

    /**
     * 采购订单id
     */
    private Long po_id;

    /**
     * 采购订单编号
     */
    private String po_code;         // 采购订单编号

    /**
     * 采购订单名称
     */
    private String po_name;         // 采购订单名称


    /**
     * 货物编号
     */
    private String goods_code;      // 货物编号

    /**
     * 货物名称
     */
    private String goods_name;      // 货物名称

    /**
     * 规格型号
     */
    private String stock_sfn;   // 规格型号

    /**
     * 单位名称
     */
    private String unit_name;       // 单位名称

    /**
     * 采购数量
     */
    private BigDecimal quantity_num;    // 采购数量

    /**
     * 待到货数量
     */
    private BigDecimal wait_num;        // 待到货数量

    /**
     * 到货数量
     */
    private BigDecimal arrived_num;     // 到货数量

    /**
     * 待上架数量
     */
    private BigDecimal treat_list_num;     // 待上架数量

    /**
     * 已上架数量
     */
    private BigDecimal list_num;     // 以上架数量

    /**
     * 父分类id
     */
    private Long material_classification_id;

    /**
     * 子分类id
     */
    private Long material_subcategory_id;

    /**
     * 仓库编号
     */
    private String warehouse_name;      // 仓库名称

    /**
     * 单价
     */
    private BigDecimal unit_price;      // 单价

    /**
     * 总价
     */
    private BigDecimal total_price;     // 总价

    /**
     * 批次编号
     */
    private String batch_code;          // 批次编号

    /**
     * 订单详情状态，char(1)
     */
    private String status;              // 订单详情状态，char(1)

}
