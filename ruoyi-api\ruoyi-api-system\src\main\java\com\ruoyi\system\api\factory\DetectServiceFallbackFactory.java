package com.ruoyi.system.api.factory;

import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.system.api.BasicService;
import com.ruoyi.system.api.DetectService;
import com.ruoyi.system.api.domain.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;

@Component
public class DetectServiceFallbackFactory implements FallbackFactory<DetectService> {

    private static final Logger log = LoggerFactory.getLogger(DetectServiceFallbackFactory.class);
    @Override
    public DetectService create(Throwable throwable)
    {
        log.error("基础服务调用失败:{}", throwable.getMessage());
        return new DetectService()
        {
            @Override
            public TableDataInfo selectQcDefectRecord(Long qc_id) {
                log.error("获取查询缺陷记录信息失败:{}", throwable.getMessage());
                return new TableDataInfo();
            }

            @Override
            public TableDataInfo selectQcIpqcLineId(Long ipqcId)
            {
                log.error("获取过程检测单行记录信息失败:{}", throwable.getMessage());
                return new TableDataInfo();
            }

            @Override
            public QcIpqcDto selectQcIpqcId(Long qc_id)
            {
                log.error("获取过程检测单信息失败:{}", throwable.getMessage());
                return new QcIpqcDto();
            }

            @Override
            public QcIqcDto selectQcIqcId(Long source_doc_id) {
                return new QcIqcDto();
            }

            @Override
            public QcOqcDto selectQcOqcListSourceDocId(Long source_doc_code) {
                return new QcOqcDto();
            }

            @Override
            public QcRqcDto selectQcRqcListSourceDocId(Long source_doc_code) {
                return new QcRqcDto();
            }

            @Override
            public AjaxResult add(QcIpqcDto qcIpqcDto) {
                 return new AjaxResult();
            }

            @Override
            public AjaxResult add(QcRqcDto qcRqcDto) {
                return new AjaxResult();
            }

            @Override
            public QcTemplateProductDto selectQcTemplateProductMaterialCode(String material_code) {
                return new QcTemplateProductDto();
            }

            @Override
            public QcTemplateProductDto selectQcTemplateProductProductCode(Long product_id) {
                return new QcTemplateProductDto();
            }
        };
    }
}
