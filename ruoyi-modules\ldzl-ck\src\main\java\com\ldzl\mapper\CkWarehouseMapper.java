package com.ldzl.mapper;

import com.ldzl.dto.CapacityDTO;
import com.ldzl.pojo.CkWarehouse;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ck_warehouse(仓库表)】的数据库操作Mapper
* @createDate 2025-07-10 16:37:29
* @Entity com.ldzl.pojo.CkWarehouse
*/
public interface CkWarehouseMapper extends BaseMapper<CkWarehouse> {

    /**
     * 查询仓库容量
     * @return
     */
    List<CapacityDTO> findCapacity_w();

    /**
     * 分页查询仓库
     * @return
     */
    List<CkWarehouse> selectWarehouse(CkWarehouse  warehouse);

    /**
     * 查询所有仓库/库区/库位
     * @return
     */
    List<CkWarehouse> selectWarehouse_w_l_a();
}




