package com.cssl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cssl.pojo.BasicStation;

import java.util.List;

public interface BasicStationMapper extends BaseMapper<BasicStation> {
    //查询工位信息
    public List<BasicStation> listBasicStation(BasicStation basicStation);

    //添加工位信息
    public int addBasicStation(BasicStation basicStation);

    //修改工位信息
    public int updateBasicStation(BasicStation basicStation);

    //删除工位信息
    public int delBasicStation(Long station_id);

    List<BasicStation> selectBasicStationByProcessIds(List<Long> processIds);
}
