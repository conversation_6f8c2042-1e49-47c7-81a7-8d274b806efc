package com.cssl.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cssl.mapper.BasicCustomersMapper;
import com.cssl.mapper.BasicFactoryMapper;
import com.cssl.pojo.BasicCustomers;
import com.cssl.pojo.BasicFactory;
import com.cssl.service.BasicCustomersService;
import com.cssl.service.BasicFactoryService;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class BasicFactoryServiceImpl extends ServiceImpl<BasicFactoryMapper, BasicFactory> implements BasicFactoryService {
    @Resource
    private BasicFactoryMapper basicFactoryMapper;

    @Override
    public List<BasicFactory> listBasicFactory(BasicFactory basicFactory) {

        return basicFactoryMapper.listBasicFactory(basicFactory);
    }

    @Override
    public int addBasicFactory(BasicFactory basicFactory) {
        basicFactory.setCreate_by(SecurityUtils.getUsername());
        basicFactory.setCreate_time(new Date());
        basicFactory.setIs_delete("0");
        return basicFactoryMapper.addBasicFactory(basicFactory);
    }

    @Override
    public int updateFactory(BasicFactory basicFactory) {
        basicFactory.setUpdate_by(SecurityUtils.getUsername());
        basicFactory.setUpdate_time(new Date());
        return basicFactoryMapper.updateFactory(basicFactory);
    }

    @Override
    public int delFactory(Long factory_id) {
        return basicFactoryMapper.delFactory(factory_id);
    }
}
