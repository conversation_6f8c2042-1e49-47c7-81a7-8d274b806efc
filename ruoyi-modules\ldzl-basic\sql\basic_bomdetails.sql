/*
 Navicat Premium Data Transfer

 Source Server         : yunSQL
 Source Server Type    : MySQL
 Source Server Version : 50744
 Source Host           : ***************:3322
 Source Schema         : ldzlmes

 Target Server Type    : MySQL
 Target Server Version : 50744
 File Encoding         : 65001

 Date: 02/07/2025 16:08:38
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for basic_bomdetails
-- ----------------------------
DROP TABLE IF EXISTS `basic_bomdetails`;
CREATE TABLE `basic_bomdetails`  (
  `bomdetails_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'BOM明细id',
  `bom_id` bigint(20) NULL DEFAULT NULL COMMENT 'bom表id',
  `material_id` bigint(20) NULL DEFAULT NULL COMMENT '物料id',
  `operational_id` bigint(20) NULL DEFAULT NULL COMMENT '工艺路线id',
  `position` int(11) NULL DEFAULT NULL COMMENT '装配顺序',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `is_detele` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '逻辑删除',
  `material_usage` decimal(10, 2) NULL DEFAULT NULL COMMENT '使用量',
  `material_Ingredient_ratio` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用料比例',
  PRIMARY KEY (`bomdetails_id`) USING BTREE,
  INDEX `bom_id`(`bom_id`) USING BTREE,
  INDEX `material_id`(`material_id`) USING BTREE,
  CONSTRAINT `basic_bomdetails_ibfk_1` FOREIGN KEY (`bom_id`) REFERENCES `basic_bom` (`bom_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `basic_bomdetails_ibfk_2` FOREIGN KEY (`material_id`) REFERENCES `basic_wl` (`material_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
