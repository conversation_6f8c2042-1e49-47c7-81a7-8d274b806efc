/*
 Navicat Premium Data Transfer

 Source Server         : yunSQL
 Source Server Type    : MySQL
 Source Server Version : 50744
 Source Host           : ***************:3322
 Source Schema         : ldzlmes

 Target Server Type    : MySQL
 Target Server Version : 50744
 File Encoding         : 65001

 Date: 02/07/2025 15:47:17
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for basic_units
-- ----------------------------
DROP TABLE IF EXISTS `basic_units`;
CREATE TABLE `basic_units`  (
  `unit_id` bigint(11) NOT NULL AUTO_INCREMENT COMMENT '计量单位id',
  `unit_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '单位编码',
  `unit_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '单位名称',
  `isPrimary_unit` int(1) NOT NULL COMMENT '是否为主单位（0：不是主单位；1：是主单位）',
  `conversionrate` decimal(10, 2) NULL DEFAULT NULL COMMENT '与主单位的换算比例',
  `unit_status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '是否启用 (1：启用；0：不启用）',
  `remarks` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `is_delete` int(11) NULL DEFAULT 1 COMMENT '逻辑删除',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `main_unit` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所属主单位名称',
  PRIMARY KEY (`unit_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of basic_units
-- ----------------------------
INSERT INTO `basic_units` VALUES (1, 'dn', '吨', 1, NULL, '1', NULL, 0, NULL, '2025-06-24 10:12:32', NULL, NULL, NULL);
INSERT INTO `basic_units` VALUES (3, 'kg', '千克', 1, NULL, '1', NULL, 0, NULL, '2025-06-24 10:17:27', NULL, NULL, NULL);
INSERT INTO `basic_units` VALUES (4, 'g', '克', 0, 1000.00, '1', NULL, 0, NULL, '2025-06-24 10:42:27', NULL, NULL, '千克');
INSERT INTO `basic_units` VALUES (5, 'kk', 'sjef00', 0, NULL, '1', NULL, 0, NULL, '2025-06-24 17:29:55', NULL, NULL, NULL);

SET FOREIGN_KEY_CHECKS = 1;
