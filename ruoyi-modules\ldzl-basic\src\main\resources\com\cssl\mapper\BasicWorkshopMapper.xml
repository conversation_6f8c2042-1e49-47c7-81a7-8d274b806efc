<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cssl.mapper.BasicWorkshopMapper">
    <resultMap id="selectBasicWorkshop" type="com.cssl.pojo.BasicWorkshop">
        <id column="workshop_id" property="workshop_id"/>
        <association property="basicFactory" javaType="com.cssl.pojo.BasicFactory">
            <id column="factory_id" property="factory_id"/>
        </association>
    </resultMap>

    <insert id="addBasicWorkshop">
        insert into basic_workshop
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="factory_name !=null and factory_name !=''">factory_name,</if>
            <if test="factory_id !=null ">factory_id,</if>
            <if test="workshop_code !=null and workshop_code !=''">workshop_code,</if>
            <if test="workshop_name !=null and workshop_name !=''">workshop_name,</if>
            <if test="remarks !=null and remarks !=''">remarks,</if>
            <if test="is_delete!=null ">is_delete,</if>
            <if test="create_by!=null and create_by !='' ">create_by,</if>
            <if test="create_time!=null ">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="factory_name !=null and factory_name !=''">#{factory_name},</if>
            <if test="factory_id !=null ">#{factory_id},</if>
            <if test="workshop_code !=null and workshop_code !=''">#{workshop_code},</if>
            <if test="workshop_name !=null and workshop_name !=''">#{workshop_name},</if>
            <if test="remarks !=null and remarks !=''">#{remarks},</if>
            <if test="is_delete!=null ">#{is_delete},</if>
            <if test="create_by!=null and create_by !='' ">#{create_by},</if>
            <if test="create_time!=null ">#{create_time},</if>
        </trim>
    </insert>

    <update id="updateBasicWorkshop">
        update basic_workshop
        <trim prefix="SET" suffixOverrides=",">
            <if test="factory_name !=null and factory_name !=''">factory_name=#{factory_name},</if>
            <if test="factory_id !=null ">factory_id=#{factory_id},</if>
            <if test="workshop_name !=null and workshop_name !=''">workshop_name=#{workshop_name},</if>
            <if test="remarks !=null and remarks !=''">remarks=#{remarks},</if>
            <if test="update_by!=null and update_by !='' ">update_by=#{update_by},</if>
            <if test="update_time!=null ">update_time=#{update_time},</if>
        </trim>
        where workshop_id=#{workshop_id}
    </update>
    <update id="delBasicWorkshop">
        update basic_workshop
        <trim prefix="SET" suffixOverrides=",">
            <if test="is_delete !=null">is_delete=1</if>
        </trim>
        where workshop_id = #{workshop_id}
    </update>

    <select id="listBasicWorkshop" resultType="com.cssl.pojo.BasicWorkshop">
        SELECT * from basic_workshop bw inner join basic_factory bf on bw.factory_id=bf.factory_id
        WHERE bf.is_delete=0 and bw.is_delete=0
        <if test="basicFactory !=null and basicFactory.factory_id !=null">and bf.factory_id=#{basicFactory.factory_id}</if>
        <if test="workshop_id !=null and workshop_id !=''">and bw.workshop_id=#{workshop_id}</if>
        <if test="workshop_name !=null and workshop_name !=''">and bw.workshop_name=#{workshop_name} </if>
    </select>
</mapper>