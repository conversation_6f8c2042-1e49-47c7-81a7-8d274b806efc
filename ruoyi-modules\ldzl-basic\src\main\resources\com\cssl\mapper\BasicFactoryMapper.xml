<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cssl.mapper.BasicFactoryMapper">

    <insert id="addBasicFactory">
        INSERT into basic_factory
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="factory_code!= null and factory_code!=''">factory_code,</if>
            <if test="factory_name != null and factory_name != ''">factory_name,</if>
            <if test="factory_address != null and factory_address !='' ">factory_address,</if>
            <if test="remarks != null and remarks != ''" >remarks,</if>
            <if test="is_delete != null ">is_delete,</if>
            <if test="create_by != null and create_by !='' ">create_by,</if>
            <if test="create_time!= null ">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="factory_code!= null and factory_code!=''">#{factory_code},</if>
            <if test="factory_name != null and factory_name != ''">#{factory_name},</if>
            <if test="factory_address != null and factory_address !='' ">#{factory_address},</if>
            <if test="remarks != null and remarks != ''" >#{remarks},</if>
            <if test="is_delete != null ">#{is_delete},</if>
            <if test="create_by != null and create_by !='' ">#{create_by},</if>
            <if test="create_time!= null ">#{create_time},</if>
        </trim>
    </insert>

    <update id="updateFactory">
        update basic_factory
        <trim prefix="SET" suffixOverrides=",">
            <if test="factory_name != null and factory_name != ''">factory_name=#{factory_name},</if>
            <if test="factory_address != null and factory_address !='' ">factory_address=#{factory_address},</if>
            <if test="remarks != null and remarks != ''" >remarks=#{remarks},</if>
            <if test="is_delete != null ">is_delete=#{is_delete},</if>
            <if test="update_by != null and update_by !='' ">update_by=#{update_by},</if>
            <if test="update_time!= null ">update_time=#{update_time},</if>
        </trim>
        where factory_id = #{factory_id}
    </update>


    <update id="delFactory">
        update basic_factory
        <trim prefix="SET" suffixOverrides=",">
            <if test="is_delete != null ">is_delete =1,</if>
        </trim>
        where factory_id = #{factory_id}
    </update>


    <select id="listBasicFactory" resultType="com.cssl.pojo.BasicFactory">
        SELECT * from basic_factory WHERE is_delete=0
        <if test="factory_name !=null and factory_name !=''">and factory_name like CONCAT('%',#{factory_name},'%')</if>
        <if test="factory_id !=null and factory_id !=null"> and factory_id=#{factory_id}</if>
    </select>
</mapper>