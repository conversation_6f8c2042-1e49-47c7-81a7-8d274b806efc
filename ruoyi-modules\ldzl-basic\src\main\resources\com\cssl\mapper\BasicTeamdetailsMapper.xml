<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cssl.mapper.BasicTeamdetailsMapper">
    <update id="delBatchTeamdetails">
        update basic_teamdetails set is_delete = '1' where teamdetails_id in
        <foreach item="teamdetails_ids" collection="list" open="(" separator="," close=")">
            #{teamdetails_ids}
        </foreach>
    </update>
    <update id="delTeamdetailsById">
        update basic_teamdetails
        <trim prefix="SET" suffixOverrides=",">
            <if test="is_delete !=null">is_delete=1</if>
        </trim>
        where team_id = #{team_id}
    </update>
    <update id="delBatchTeamdetails1">
        update basic_teamdetails set is_delete = '1' where team_id in
        <foreach item="team_ids" collection="list" open="(" separator="," close=")">
            #{team_ids}
        </foreach>
    </update>

    <select id="selectTeamdetailsById" resultType="com.cssl.pojo.vo.BasicTeamVo">
        SELECT a.*,b.* ,c.*,d.*from ldzlmes.basic_teamdetails a inner join ldzlmes.basic_team b on a.team_id=b.team_id
                                                                inner join `ry-cloud`.sys_user c on a.user_id=c.user_id inner join `ry-cloud`.sys_dept d on d.dept_id=c.dept_id WHERE a.is_delete=0 and b.is_delete=0 and c.login_ip=0 and d.del_flag=0
        <if test="team_id !=null and team_id!=''">
            and a.team_id=#{team_id}
        </if>
    </select>
    <select id="selectTeamdetailsById1" resultType="com.cssl.pojo.BasicTeamdetails">
        SELECT a.*,b.* ,c.*,d.*from ldzlmes.basic_teamdetails a inner join ldzlmes.basic_team b on a.team_id=b.team_id
        inner join `ry-cloud`.sys_user c on a.user_id=c.user_id inner join `ry-cloud`.sys_dept d on d.dept_id=c.dept_id WHERE a.is_delete=0 and b.is_delete=0 and c.login_ip=0 and d.del_flag=0
        <if test="team_id !=null and team_id!=''">
            and a.team_id=#{team_id}
        </if>
    </select>
</mapper>