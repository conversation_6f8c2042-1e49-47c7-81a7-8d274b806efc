<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI 聊天助手</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #f0f2f5;
        }

        .chat-container {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 600px;
            height: 80vh;
            display: flex;
            flex-direction: column;
        }

        .chat-header {
            background-color: #4CAF50;
            color: white;
            padding: 16px;
            border-top-left-radius: 12px;
            border-top-right-radius: 12px;
            text-align: center;
            font-size: 1.2rem;
            font-weight: bold;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 16px;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .message {
            max-width: 80%;
            padding: 12px;
            border-radius: 8px;
            word-wrap: break-word;
        }

        .user-message {
            background-color: #e3f2fd;
            align-self: flex-end;
        }

        .ai-message {
            background-color: #f1f8e9;
            align-self: flex-start;
        }

        .chat-input {
            display: flex;
            padding: 16px;
            border-top: 1px solid #eee;
        }

        #user-input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-right: 10px;
            font-size: 1rem;
        }

        #send-button {
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 10px 20px;
            cursor: pointer;
            font-size: 1rem;
            transition: background-color 0.3s;
        }

        #send-button:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
<div class="chat-container">
    <div class="chat-header">Flynn Chat</div>
    <div class="chat-messages" id="chat-messages"></div>
    <div class="chat-input">
        <input type="text" id="user-input" placeholder="输入你的问题...">
        <button id="send-button">发送</button>
    </div>
</div>

<script>
    const chatMessages = document.getElementById('chat-messages');
    const userInput = document.getElementById('user-input');
    const sendButton = document.getElementById('send-button');

    // 发送消息
    function sendMessage() {
        const query = userInput.value.trim();
        if (query) {
            appendMessage(query, 'user');
            userInput.value = '';

            // 调用后端 AI 接口
            fetch('/helloworld/simple/chat?query=' + encodeURIComponent(query))
                .then(response => response.text())
                .then(aiResponse => {
                    appendMessage(aiResponse, 'ai');
                })
                .catch(error => {
                    console.error('Error fetching AI response:', error);
                    appendMessage('抱歉，获取回答时出错，请稍后再试。', 'ai');
                });
        }
    }

    // 添加消息到聊天区域
    function appendMessage(text, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;
        messageDiv.textContent = text;
        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    // 绑定发送按钮点击事件
    sendButton.addEventListener('click', sendMessage);

    // 绑定回车键事件
    userInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            sendMessage();
        }
    });
</script>
</body>
</html>