package com.cssl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cssl.mapper.BasicWlflMapper;
import com.cssl.pojo.BasicNumbers;
import com.cssl.pojo.BasicWlfl;

import java.util.List;

public interface BasicWlflService extends IService<BasicWlfl> {

    //新增父分类
    public int insertBasicWlfl(BasicWlfl basicWlfl);

    //修改父分类
    public int updateBasicWlflz(BasicWlfl basicWlfl);

    //删除父分类
    public int delByBasicWlflzInt(Long material_classification_id);

    //查询父分类
    public List<BasicWlfl> findBasicWlflz();


    /**
     * 查询所有分类 一对多
     * @return
     */
    public List<BasicWlfl> selectBasicWlflWithChildren();
}
