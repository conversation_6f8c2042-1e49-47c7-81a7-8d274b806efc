package com.cssl.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("basic_bomdetails")
public class BasicBomdetails{
    @TableId(value ="bomdetails_id",type = IdType.AUTO)
    private Long bomdetails_id;
    private Integer position;
    private String is_detele;
    private String create_by;
    private Date create_time;
    private String update_by;
    private Date update_time;
    @TableField(exist = false)
    private BasicBom basicBom;
    @TableField(exist = false)
    private BasicWlgl basicWlgl;
    @TableField(exist = false)
    private BasicOperational basicOperational;
    @TableField(exist = false)
    private BasicProduct basicProduct;
    private Long bom_id;
    private Long material_id;
    private Long operational_id;
    private Long product_id;
    private BigDecimal material_usage;
    private String material_Ingredient_ratio;

    @TableField(exist = false)
    private String material_code;

    @TableField(exist = false)
    private String material_name;

    @TableField(exist = false)
    private String specification;

    @TableField(exist = false)
    private String unit;

    @TableField(exist = false)
    private BigDecimal quantity;

    @TableField(exist = false)
    private String remark;

    @TableField(exist = false)
    private String operational_code;

    @TableField(exist = false)
    private String operational_name;

    @TableField(exist = false)
    private String operational_status;

    @TableField(exist = false)
    private String operational_description;

}
