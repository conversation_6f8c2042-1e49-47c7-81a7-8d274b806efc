{"remainingRequest": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\src\\views\\sc\\plan\\add_plan.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\src\\views\\sc\\plan\\add_plan.vue", "mtime": 1753785710311}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\babel.config.js", "mtime": 1749629472348}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751945356000}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751945361444}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751945356000}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751945364156}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_productionPlan", "require", "_product", "_numbers", "_Pagination", "_interopRequireDefault", "name", "components", "Pagination", "data", "form", "planCode", "planName", "sourceType", "orderCode", "productId", "undefined", "productName", "productCode", "specification", "unit", "plannedQty", "planStartTime", "planEndTime", "requiredDate", "remark", "isSystemCode", "rules", "required", "message", "trigger", "max", "validator", "validateStartTime", "validateEndTime", "validateRequiredDate", "productOptions", "productLoading", "sourceTypeOptions", "dict<PERSON><PERSON>l", "dict<PERSON><PERSON>ue", "fileList", "productDialogVisible", "productQuery", "pageNum", "pageSize", "keyword", "type", "property", "productList", "productTotal", "productionOrderDialogVisible", "productionOrderList", "selectedProductionOrder", "selectedProduct", "bomDialogVisible", "b<PERSON><PERSON><PERSON><PERSON>", "bomList", "bomTotal", "bomLoading", "selectedBom", "selectedBomId", "bomDetailList", "created", "generatePlanCode", "methods", "_this", "getAutoNumbers", "then", "response", "code", "msg", "$message", "error", "catch", "handleSystemCodeChange", "val", "handleSourceTypeChange", "openProductionOrderDialog", "getProductionOrderList", "_this2", "Promise", "resolve", "_interopRequireWildcard2", "default", "api", "listProductionOrder", "rows", "handleProductionOrderSelectionChange", "selection", "length", "confirmProductionOrderSelect", "_this3", "warning", "prodOrderCode", "createPlanFromOrder", "productionOrderId", "planData", "getProductDetails", "success", "_this4", "listProducts", "product", "product_name", "product_code", "product_sfn", "product_unit", "console", "triggerUpload", "$refs", "upload", "$el", "click", "openProductSelection", "getProductsByOrder", "getProductList", "_this5", "productUnit", "productType", "productProperty", "total", "product_id", "product_type", "product_property", "_this6", "getProductionOrderDetails", "products", "map", "detail", "productSfn", "order_qty", "qtyNum", "delivery_date", "deliveryDate", "searchProducts", "resetProduct<PERSON>uery", "handleProductSelectionChange", "handleProductCurrentChange", "currentPage", "handleProductSizeChange", "size", "confirmProductSelect", "selectBom", "getBomList", "_this7", "listBomsByProductId", "info", "defaultBom", "find", "b", "bom_status", "handleBomSelect", "row", "bom_id", "confirmBomSelect", "getBomDetail", "_this8", "findBomDetails", "log", "clearSelectedBom", "beforeUpload", "file", "isValidType", "test", "isLt10M", "uploadFile", "options", "push", "url", "URL", "createObjectURL", "onSuccess", "handleRemove", "index", "indexOf", "splice", "submitForm", "_this9", "validate", "valid", "validateDateLogic", "addProductionPlan", "$modal", "msgSuccess", "cancel", "msgError", "$router", "path", "rule", "value", "callback", "today", "Date", "setHours", "startDate", "Error", "endDate"], "sources": ["src/views/sc/plan/add_plan.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <div class=\"form-container\">\n      <!-- 基础信息区 -->\n      <el-tabs type=\"border-card\">\n        <el-tab-pane>\n          <span slot=\"label\"><i class=\"el-icon-date\"></i> 基础信息</span>\n          <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\n            <el-row :gutter=\"20\">\n              <el-col :span=\"8\">\n                <el-form-item label=\"计划编号\" prop=\"planCode\" required>\n                  <el-input v-model=\"form.planCode\" placeholder=\"请输入\" :disabled=\"isSystemCode\"></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"4\">\n                <el-switch\n                  v-model=\"isSystemCode\"\n                  active-text=\"系统编号\"\n                  inactive-text=\"\"\n                  style=\"margin-top: 13px;\"\n                  @change=\"handleSystemCodeChange\"\n                ></el-switch>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"计划名称\" prop=\"planName\" required>\n                  <el-input v-model=\"form.planName\" placeholder=\"请输入\"></el-input>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-row :gutter=\"20\">\n              <el-col :span=\"12\">\n                <el-form-item label=\"来源类型\" prop=\"sourceType\" required>\n                  <el-select v-model=\"form.sourceType\" placeholder=\"销售订单\" style=\"width: 100%\" @change=\"handleSourceTypeChange\">\n                    <el-option\n                      v-for=\"item in sourceTypeOptions\"\n                      :key=\"item.dictValue\"\n                      :label=\"item.dictLabel\"\n                      :value=\"item.dictValue\"\n                    ></el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"订单编号\" prop=\"orderCode\">\n                  <el-input v-model=\"form.orderCode\" placeholder=\"请输入\">\n                    <el-button\n                      v-if=\"form.sourceType === 'PRODUCTION_ORDER'\"\n                      slot=\"append\"\n                      icon=\"el-icon-search\"\n                      @click=\"openProductionOrderDialog\">\n                      选择\n                    </el-button>\n                  </el-input>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-row :gutter=\"20\">\n              <el-col :span=\"12\">\n                <el-form-item label=\"成品名称\" prop=\"productName\">\n                  <el-input\n                    placeholder=\"请选择成品\"\n                    v-model=\"form.productName\"\n                    class=\"input-with-select\"\n                  >\n                    <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"openProductSelection\"></el-button>\n                  </el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"成品编号\" prop=\"productCode\">\n                  <el-input v-model=\"form.productCode\" placeholder=\"请输入\" disabled></el-input>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-row :gutter=\"20\">\n              <el-col :span=\"12\">\n                <el-form-item label=\"规格型号\" prop=\"specification\">\n                  <el-input v-model=\"form.specification\" placeholder=\"请输入\" disabled></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"单位\" prop=\"unit\">\n                  <el-input v-model=\"form.unit\" placeholder=\"请输入\" disabled></el-input>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-row :gutter=\"20\">\n              <el-col :span=\"12\">\n                <el-form-item label=\"计划数量\" prop=\"plannedQty\" required>\n                  <el-input-number v-model=\"form.plannedQty\" :min=\"1\" controls-position=\"right\" style=\"width: 100%\"></el-input-number>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-row :gutter=\"20\">\n              <el-col :span=\"12\">\n                <el-form-item label=\"开工时间\" prop=\"planStartTime\">\n                  <el-date-picker\n                    v-model=\"form.planStartTime\"\n                    type=\"date\"\n                    placeholder=\"请选择日期\"\n                    style=\"width: 100%\"\n                    value-format=\"yyyy-MM-dd\"\n                  ></el-date-picker>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"完工时间\" prop=\"planEndTime\">\n                  <el-date-picker\n                    v-model=\"form.planEndTime\"\n                    type=\"date\"\n                    placeholder=\"请选择日期\"\n                    style=\"width: 100%\"\n                    value-format=\"yyyy-MM-dd\"\n                  ></el-date-picker>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-row :gutter=\"20\">\n              <el-col :span=\"12\">\n                <el-form-item label=\"需求日期\" prop=\"requiredDate\">\n                  <el-date-picker\n                    v-model=\"form.requiredDate\"\n                    type=\"date\"\n                    placeholder=\"请选择日期\"\n                    style=\"width: 100%\"\n                    value-format=\"yyyy-MM-dd\"\n                  ></el-date-picker>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-row :gutter=\"20\">\n              <el-col :span=\"24\">\n                <el-form-item label=\"备注\" prop=\"remark\">\n                  <el-input\n                    type=\"textarea\"\n                    v-model=\"form.remark\"\n                    placeholder=\"请输入\"\n                    :rows=\"4\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-row :gutter=\"20\">\n              <el-col :span=\"24\">\n                <el-form-item label=\"附件\" prop=\"attachment\">\n                  <div class=\"upload-container\" @click=\"triggerUpload\">\n                    <el-upload\n                      ref=\"upload\"\n                      class=\"upload-hidden\"\n                      action=\"#\"\n                      :http-request=\"uploadFile\"\n                      :file-list=\"fileList\"\n                      :before-upload=\"beforeUpload\"\n                      :on-remove=\"handleRemove\"\n                      multiple\n                      drag\n                    >\n                      <div class=\"upload-area\">\n                        <i class=\"el-icon-upload\"></i>\n                        <div class=\"upload-text\">点击或者拖动文件到虚线框内上传</div>\n                        <div class=\"upload-hint\">支持 docx, xls, PDF, rar, zip, PNG, JPG 等类型的文件</div>\n                      </div>\n                    </el-upload>\n                  </div>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-divider>\n              <span class=\"bom-title\">BOM组成</span>\n            </el-divider>\n            \n            <el-row>\n              <el-col :span=\"24\">\n                <div class=\"bom-container\">\n                  <div class=\"folder-icon\" v-if=\"!selectedBom\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"60\" height=\"60\" viewBox=\"0 0 24 24\" fill=\"#DCDFE6\">\n                      <path d=\"M10 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2h-8l-2-2z\"/>\n                    </svg>\n                  </div>\n                  <div class=\"bom-text\" v-if=\"!selectedBom\">暂无数据</div>\n                  <div class=\"bom-info\" v-else>\n                    <div class=\"bom-header\">\n                      <div class=\"bom-title-info\">\n                        <span>BOM编号：{{ selectedBom.bom_code }}</span>\n                        <span>版本号：{{ selectedBom.bom_version }}</span>\n                      </div>\n                      <el-button type=\"text\" icon=\"el-icon-delete\" @click=\"clearSelectedBom\">清除</el-button>\n                    </div>\n                    <el-table\n                      :data=\"bomDetailList\"\n                      border\n                      size=\"small\"\n                      style=\"width: 100%\"\n                      class=\"bom-detail-table\"\n                    >\n                      <el-table-column label=\"序号\" type=\"index\" width=\"50\" align=\"center\"></el-table-column>\n                      <el-table-column prop=\"material_code\" label=\"物料编码\" min-width=\"120\" align=\"center\"></el-table-column>\n                      <el-table-column prop=\"material_name\" label=\"物料名称\" min-width=\"150\" align=\"center\"></el-table-column>\n                      <el-table-column prop=\"specification\" label=\"规格型号\" min-width=\"100\" align=\"center\"></el-table-column>\n                      <el-table-column prop=\"unit\" label=\"单位\" width=\"60\" align=\"center\"></el-table-column>\n                      <el-table-column prop=\"quantity\" label=\"用量\" width=\"80\" align=\"center\"></el-table-column>\n                      <el-table-column prop=\"remark\" label=\"备注\" min-width=\"120\" align=\"center\"></el-table-column>\n                    </el-table>\n                  </div>\n                  <div class=\"bom-action\">\n                    <el-tooltip :disabled=\"form.productId\" content=\"请先选择成品!\" placement=\"right\" effect=\"light\">\n                      <el-button type=\"primary\" class=\"select-bom-button\" @click=\"selectBom\">选择BOM</el-button>\n                    </el-tooltip>\n                  </div>\n                </div>\n              </el-col>\n            </el-row>\n            \n            <el-row>\n              <el-col :span=\"24\" style=\"text-align: center; margin-top: 20px;\">\n                <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n                <el-button @click=\"cancel\">取 消</el-button>\n              </el-col>\n            </el-row>\n          </el-form>\n        </el-tab-pane>\n      </el-tabs>\n    </div>\n    \n    <!-- 产品选择对话框 -->\n    <el-dialog title=\"选择成品\" :visible.sync=\"productDialogVisible\" width=\"40%\" append-to-body>\n      <el-form :model=\"productQuery\" ref=\"productQueryForm\" :inline=\"true\" class=\"demo-form-inline\" size=\"small\">\n        <el-form-item>\n          <el-input v-model=\"productQuery.keyword\" placeholder=\"请输入产品编号/名称\" clearable style=\"width: 180px;\"></el-input>\n        </el-form-item>\n        <el-form-item>\n          <el-select v-model=\"productQuery.unit\" placeholder=\"请选择单位\" clearable style=\"width: 120px;\">\n            <el-option label=\"个\" value=\"个\"></el-option>\n            <el-option label=\"件\" value=\"件\"></el-option>\n            <el-option label=\"台\" value=\"台\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item>\n          <el-select v-model=\"productQuery.type\" placeholder=\"请选择类型\" clearable style=\"width: 120px;\">\n            <el-option label=\"成品\" value=\"成品\"></el-option>\n            <el-option label=\"半成品\" value=\"半成品\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item>\n          <el-select v-model=\"productQuery.property\" placeholder=\"请选择产品属性\" clearable style=\"width: 120px;\">\n            <el-option label=\"自制\" value=\"自制\"></el-option>\n            <el-option label=\"外购\" value=\"外购\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"small\" @click=\"searchProducts\">查询</el-button>\n          <el-button icon=\"el-icon-refresh\" size=\"small\" @click=\"resetProductQuery\">重置</el-button>\n        </el-form-item>\n      </el-form>\n      \n      <el-table\n        v-loading=\"productLoading\"\n        :data=\"productList\"\n        border\n        size=\"small\"\n        style=\"width: 100%\"\n        @selection-change=\"handleProductSelectionChange\"\n        height=\"300\"\n      >\n        <el-table-column type=\"selection\" width=\"40\" align=\"center\"></el-table-column>\n        <el-table-column label=\"序号\" type=\"index\" width=\"40\" align=\"center\"></el-table-column>\n        <el-table-column prop=\"product_code\" label=\"产品编号\" width=\"90\" align=\"center\"></el-table-column>\n        <el-table-column prop=\"product_name\" label=\"产品名称\" min-width=\"120\" align=\"center\"></el-table-column>\n        <el-table-column prop=\"product_sfn\" label=\"规格型号\" min-width=\"90\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <span :style=\"{color: '#1890ff'}\">{{ scope.row.product_sfn }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"product_unit\" label=\"单位\" width=\"60\" align=\"center\"></el-table-column>\n        <!-- 生产订单产品特有的列 -->\n        <el-table-column v-if=\"form.sourceType === 'PRODUCTION_ORDER'\" prop=\"order_qty\" label=\"订单数量\" width=\"80\" align=\"center\"></el-table-column>\n        <el-table-column v-if=\"form.sourceType === 'PRODUCTION_ORDER'\" prop=\"delivery_date\" label=\"交付日期\" width=\"100\" align=\"center\">\n          <template slot-scope=\"scope\">\n            {{ scope.row.delivery_date ? scope.row.delivery_date.substring(0, 10) : '' }}\n          </template>\n        </el-table-column>\n      </el-table>\n      \n      <div class=\"pagination-container\">\n        <span class=\"total-text\">共 {{ productTotal }} 条</span>\n        <div class=\"pagination-wrapper\">\n          <span class=\"page-size\">\n            <el-select v-model=\"productQuery.pageSize\" size=\"mini\" @change=\"handleProductSizeChange\" style=\"width: 80px;\">\n              <el-option\n                v-for=\"item in [10, 20, 30, 50]\"\n                :key=\"item\"\n                :label=\"`${item}条/页`\"\n                :value=\"item\">\n              </el-option>\n            </el-select>\n          </span>\n          <el-pagination\n            small\n            background\n            @current-change=\"handleProductCurrentChange\"\n            :current-page=\"productQuery.pageNum\"\n            :page-size=\"productQuery.pageSize\"\n            layout=\"prev, pager, next, jumper\"\n            :pager-count=\"5\"\n            :total=\"productTotal\">\n          </el-pagination>\n        </div>\n      </div>\n      \n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button size=\"small\" @click=\"productDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" size=\"small\" @click=\"confirmProductSelect\">确定</el-button>\n      </div>\n    </el-dialog>\n    \n    <!-- BOM选择对话框 -->\n    <el-dialog title=\"选择BOM\" :visible.sync=\"bomDialogVisible\" width=\"40%\" append-to-body>\n      <div class=\"bom-dialog-header\">\n        <div class=\"product-info\">\n          <span>产品名称：{{ form.productName }}</span>\n          <span>产品编号：{{ form.productCode }}</span>\n          <span>规格型号：{{ form.specification }}</span>\n          <span>单位：{{ form.unit }}</span>\n        </div>\n      </div>\n      \n      <el-table\n        v-loading=\"bomLoading\"\n        :data=\"bomList\"\n        border\n        style=\"width: 100%\"\n        @row-click=\"handleBomSelect\"\n        highlight-current-row\n        size=\"small\"\n        height=\"300\"\n      >\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <el-radio v-model=\"selectedBomId\" :label=\"scope.row.bom_id\" @change=\"handleBomSelect(scope.row)\">&nbsp;</el-radio>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"序号\" type=\"index\" width=\"55\" align=\"center\"></el-table-column>\n        <el-table-column prop=\"bom_code\" label=\"BOM编号\" min-width=\"150\" align=\"center\"></el-table-column>\n        <el-table-column prop=\"bom_version\" label=\"版本号\" width=\"100\" align=\"center\"></el-table-column>\n        <el-table-column prop=\"bom_status\" label=\"默认BOM\" width=\"100\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <span>{{ scope.row.bom_status === '1' ? '是' : '否' }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"bom_output\" label=\"日产量\" width=\"100\" align=\"center\"></el-table-column>\n      </el-table>\n      \n      <pagination\n        v-show=\"bomTotal > 0\"\n        :total=\"bomTotal\"\n        :page.sync=\"bomQuery.pageNum\"\n        :limit.sync=\"bomQuery.pageSize\"\n        @pagination=\"getBomList\"\n      />\n      \n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"bomDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"confirmBomSelect\">确定</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 生产订单选择对话框 -->\n    <el-dialog title=\"选择生产订单\" :visible.sync=\"productionOrderDialogVisible\" width=\"800px\" append-to-body>\n      <el-table\n        :data=\"productionOrderList\"\n        @selection-change=\"handleProductionOrderSelectionChange\"\n        style=\"width: 100%\">\n        <el-table-column type=\"selection\" width=\"55\"></el-table-column>\n        <el-table-column prop=\"prodOrderCode\" label=\"订单编号\" width=\"150\"></el-table-column>\n        <el-table-column prop=\"customerName\" label=\"客户名称\" width=\"150\"></el-table-column>\n        <el-table-column prop=\"deliveryDate\" label=\"交付日期\" width=\"120\">\n          <template slot-scope=\"scope\">\n            {{ scope.row.deliveryDate ? scope.row.deliveryDate.substring(0, 10) : '' }}\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"status\" label=\"状态\" width=\"80\">\n          <template slot-scope=\"scope\">\n            <el-tag v-if=\"scope.row.status === '1'\" type=\"warning\">待计划</el-tag>\n            <el-tag v-else-if=\"scope.row.status === '2'\" type=\"primary\">已计划</el-tag>\n            <el-tag v-else-if=\"scope.row.status === '3'\" type=\"success\">生产中</el-tag>\n            <el-tag v-else-if=\"scope.row.status === '4'\" type=\"info\">已完成</el-tag>\n            <el-tag v-else type=\"info\">{{ scope.row.status }}</el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"remark\" label=\"备注\" show-overflow-tooltip></el-table-column>\n      </el-table>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"productionOrderDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"confirmProductionOrderSelect\">确定</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { addProductionPlan } from \"@/api/sc/productionPlan\";\nimport { listProducts, listBomsByProductId, findBomDetails } from \"@/api/basic/product\";\nimport { getAutoNumbers } from \"@/api/basic/numbers\";\nimport Pagination from \"@/components/Pagination\";\n\nexport default {\n  name: \"AddPlan\",\n  components: {\n    Pagination\n  },\n  data() {\n    return {\n      // 表单数据\n      form: {\n        planCode: \"\",\n        planName: \"\",\n        sourceType: \"销售订单\",\n        orderCode: \"\",\n        productId: undefined,\n        productName: \"\",\n        productCode: \"\",\n        specification: \"\",\n        unit: \"\",\n        plannedQty: 1,\n        planStartTime: \"\",\n        planEndTime: \"\",\n        requiredDate: \"\",\n        remark: \"\"\n      },\n      // 是否使用系统编号\n      isSystemCode: true,\n      // 表单验证规则\n      rules: {\n        planName: [\n          { required: true, message: \"计划名称不能为空\", trigger: \"blur\" },\n          { max: 50, message: \"长度不能超过50个字符\", trigger: \"blur\" }\n        ],\n        sourceType: [\n          { required: true, message: \"来源类型不能为空\", trigger: \"change\" }\n        ],\n        productName: [\n          { required: true, message: \"请选择产品\", trigger: \"change\" }\n        ],\n        plannedQty: [\n          { required: true, message: \"计划数量不能为空\", trigger: \"blur\" }\n        ],\n        planStartTime: [\n          { validator: this.validateStartTime, trigger: \"change\" }\n        ],\n        planEndTime: [\n          { validator: this.validateEndTime, trigger: \"change\" }\n        ],\n        requiredDate: [\n          { validator: this.validateRequiredDate, trigger: \"change\" }\n        ]\n      },\n      // 产品下拉选项\n      productOptions: [],\n      // 产品加载状态\n      productLoading: false,\n      // 来源类型选项\n      sourceTypeOptions: [\n        { dictLabel: \"销售订单\", dictValue: \"销售订单\" },\n        { dictLabel: \"库存备货\", dictValue: \"库存备货\" },\n        { dictLabel: \"生产订单\", dictValue: \"PRODUCTION_ORDER\" }\n      ],\n      // 上传文件列表\n      fileList: [],\n      \n      // 产品选择对话框\n      productDialogVisible: false,\n      productQuery: {\n        pageNum: 1,\n        pageSize: 10,\n        keyword: \"\",\n        unit: \"\",\n        type: \"\",\n        property: \"\"\n      },\n      productList: [],\n      productTotal: 0,\n\n      // 生产订单选择对话框\n      productionOrderDialogVisible: false,\n      productionOrderList: [],\n      selectedProductionOrder: null,\n      selectedProduct: null,\n      \n      // BOM选择对话框\n      bomDialogVisible: false,\n      bomQuery: {\n        pageNum: 1,\n        pageSize: 10,\n        productId: undefined\n      },\n      bomList: [],\n      bomTotal: 0,\n      bomLoading: false,\n      selectedBom: null,\n      selectedBomId: null,\n      bomDetailList: [],\n    };\n  },\n  created() {\n    // 初始化时如果是系统编号，则生成计划编号\n    if (this.isSystemCode) {\n      this.generatePlanCode();\n    }\n  },\n  methods: {\n    // 生成计划编号\n    generatePlanCode() {\n      getAutoNumbers(6).then(response => {\n        if (response.code === 200) {\n          this.form.planCode = response.msg;\n        } else {\n          this.$message.error('获取计划编号失败');\n        }\n      }).catch(() => {\n        this.$message.error('获取计划编号失败');\n      });\n    },\n    \n    // 处理系统编号开关变化\n    handleSystemCodeChange(val) {\n      if (val) {\n        // 如果开启系统编号，则生成编号\n        this.generatePlanCode();\n      } else {\n        // 如果关闭系统编号，则清空编号\n        this.form.planCode = '';\n      }\n    },\n\n    // 处理来源类型变化\n    handleSourceTypeChange(val) {\n      // 清空订单编号\n      this.form.orderCode = \"\";\n      // 如果选择生产订单，清空产品相关信息\n      if (val === 'PRODUCTION_ORDER') {\n        this.form.productId = undefined;\n        this.form.productName = \"\";\n        this.form.productCode = \"\";\n        this.form.specification = \"\";\n        this.form.unit = \"\";\n      }\n    },\n\n    // 打开生产订单选择对话框\n    openProductionOrderDialog() {\n      this.productionOrderDialogVisible = true;\n      this.getProductionOrderList();\n    },\n\n    // 获取生产订单列表\n    getProductionOrderList() {\n      // 这里调用生产订单列表API\n      import(\"@/api/sc/productionOrder\").then(api => {\n        api.listProductionOrder({}).then(response => {\n          this.productionOrderList = response.rows || [];\n        }).catch(() => {\n          this.$message.error('获取生产订单列表失败');\n        });\n      });\n    },\n\n    // 处理生产订单选择变化\n    handleProductionOrderSelectionChange(selection) {\n      this.selectedProductionOrder = selection.length > 0 ? selection[0] : null;\n    },\n\n    // 确认选择生产订单\n    confirmProductionOrderSelect() {\n      if (!this.selectedProductionOrder) {\n        this.$message.warning('请选择一个生产订单');\n        return;\n      }\n\n      // 设置订单编号\n      this.form.orderCode = this.selectedProductionOrder.prodOrderCode;\n\n      // 调用API根据生产订单创建计划模板\n      import(\"@/api/sc/productionPlan\").then(api => {\n        api.createPlanFromOrder(this.selectedProductionOrder.productionOrderId).then(response => {\n          if (response.code === 200) {\n            const planData = response.data;\n            // 填充表单数据\n            this.form.planName = planData.planName;\n            this.form.productId = planData.productId;\n            this.form.plannedQty = planData.plannedQty;\n            this.form.planStartTime = planData.planStartTime;\n            this.form.planEndTime = planData.planEndTime;\n            this.form.requiredDate = planData.requiredDate;\n            this.form.remark = planData.remark;\n\n            // 如果有产品信息，需要获取产品详情\n            if (planData.productId) {\n              this.getProductDetails(planData.productId);\n            }\n\n            this.$message.success('已关联生产订单，请完善其他信息');\n          }\n        }).catch(() => {\n          this.$message.error('关联生产订单失败');\n        });\n      });\n\n      this.productionOrderDialogVisible = false;\n    },\n\n    // 获取产品详情\n    getProductDetails(productId) {\n      listProducts({ productId: productId }).then(response => {\n        if (response.rows && response.rows.length > 0) {\n          const product = response.rows[0];\n          this.form.productName = product.product_name;\n          this.form.productCode = product.product_code;\n          this.form.specification = product.product_sfn;\n          this.form.unit = product.product_unit;\n        }\n      }).catch(() => {\n        console.error('获取产品详情失败');\n      });\n    },\n    \n    // 触发上传\n    triggerUpload() {\n      this.$refs.upload.$el.click();\n    },\n    \n    // 打开产品选择弹窗\n    openProductSelection() {\n      if (this.form.sourceType === 'PRODUCTION_ORDER') {\n        // 如果选择了生产订单，但没有选择具体订单\n        if (!this.form.orderCode) {\n          this.$message.warning('请先选择生产订单');\n          return;\n        }\n        // 显示该订单的产品列表\n        this.getProductsByOrder();\n      } else {\n        // 其他情况显示全部产品\n        this.getProductList();\n      }\n      this.productDialogVisible = true;\n    },\n    \n    // 获取产品列表\n    getProductList() {\n      this.productLoading = true;\n      listProducts({\n        pageNum: this.productQuery.pageNum,\n        pageSize: this.productQuery.pageSize,\n        keyword: this.productQuery.keyword,\n        productUnit: this.productQuery.unit,\n        productType: this.productQuery.type,\n        productProperty: this.productQuery.property\n      }).then(response => {\n        this.productLoading = false;\n        if (response.code === 200) {\n          this.productList = response.rows;\n          this.productTotal = response.total;\n        }\n      }).catch(() => {\n        this.productLoading = false;\n        // 模拟数据\n        this.productList = [\n          { product_id: 1, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' },\n          { product_id: 2, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' },\n          { product_id: 3, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' },\n          { product_id: 4, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' },\n          { product_id: 5, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' }\n        ];\n        this.productTotal = 50;\n      });\n    },\n\n    // 根据生产订单获取产品列表\n    getProductsByOrder() {\n      if (!this.selectedProductionOrder) {\n        this.$message.error('未找到选中的生产订单信息');\n        return;\n      }\n\n      this.productLoading = true;\n      // 调用API获取订单产品明细\n      import(\"@/api/sc/productionOrder\").then(api => {\n        api.getProductionOrderDetails(this.selectedProductionOrder.productionOrderId).then(response => {\n          if (response.code === 200 && response.data && response.data.products) {\n            // 将订单明细转换为产品列表格式\n            this.productList = response.data.products.map(detail => ({\n              product_id: detail.productId,\n              product_name: detail.productName,\n              product_code: detail.productCode,\n              product_sfn: detail.productSfn,\n              product_unit: detail.productUnit,\n              // 添加订单相关信息\n              order_qty: detail.qtyNum,\n              delivery_date: detail.deliveryDate\n            }));\n            this.productTotal = this.productList.length;\n          } else {\n            this.$message.warning('该订单暂无产品明细');\n            this.productList = [];\n            this.productTotal = 0;\n          }\n          this.productLoading = false;\n        }).catch(() => {\n          this.$message.error('获取订单产品列表失败');\n          this.productLoading = false;\n        });\n      });\n    },\n\n    // 搜索产品\n    searchProducts() {\n      this.productQuery.pageNum = 1;\n      this.getProductList();\n    },\n    \n    // 重置产品查询条件\n    resetProductQuery() {\n      this.productQuery = {\n        pageNum: 1,\n        pageSize: 10,\n        keyword: \"\",\n        unit: \"\",\n        type: \"\",\n        property: \"\"\n      };\n      this.getProductList();\n    },\n    \n    // 处理产品表格选择变化\n    handleProductSelectionChange(selection) {\n      if (selection.length > 0) {\n        this.selectedProduct = selection[0];\n      } else {\n        this.selectedProduct = null;\n      }\n    },\n    \n    // 处理产品页码变化\n    handleProductCurrentChange(currentPage) {\n      this.productQuery.pageNum = currentPage;\n      this.getProductList();\n    },\n    \n    // 处理产品每页条数变化\n    handleProductSizeChange(size) {\n      this.productQuery.pageSize = size;\n      this.productQuery.pageNum = 1;\n      this.getProductList();\n    },\n    \n    // 确认产品选择\n    confirmProductSelect() {\n      if (this.selectedProduct) {\n        this.form.productId = this.selectedProduct.product_id;\n        this.form.productName = this.selectedProduct.product_name;\n        this.form.productCode = this.selectedProduct.product_code;\n        this.form.specification = this.selectedProduct.product_sfn;\n        this.form.unit = this.selectedProduct.product_unit;\n\n        // 如果是从生产订单选择的产品，自动填充订单数量和交付日期\n        if (this.form.sourceType === 'PRODUCTION_ORDER' && this.selectedProduct.order_qty) {\n          this.form.plannedQty = this.selectedProduct.order_qty;\n          if (this.selectedProduct.delivery_date) {\n            this.form.requiredDate = this.selectedProduct.delivery_date;\n            this.form.planEndTime = this.selectedProduct.delivery_date;\n          }\n        }\n\n        this.productDialogVisible = false;\n\n        // 清空已选BOM\n        this.selectedBom = null;\n        this.selectedBomId = null;\n      } else {\n        this.$message.warning('请选择一个产品！');\n      }\n    },\n    \n    // 选择BOM\n    selectBom() {\n      if (!this.form.productId) {\n        this.$message.warning('请先选择成品！');\n        return;\n      }\n      this.bomDialogVisible = true;\n      this.getBomList();\n    },\n    \n    // 获取BOM列表\n    getBomList() {\n      this.bomLoading = true;\n      listBomsByProductId(this.form.productId).then(response => {\n        this.bomLoading = false;\n        if (response.code === 200) {\n          this.bomList = response.rows;\n          this.bomTotal = response.total;\n          if (!this.bomList || this.bomList.length === 0) {\n            this.$message.info(\"未找到该产品的BOM信息\");\n          } else {\n            // 如果有默认BOM，则自动选中\n            const defaultBom = this.bomList.find(b => b.bom_status === '1');\n            if (defaultBom) {\n              this.handleBomSelect(defaultBom);\n            }\n          }\n        } else {\n          this.bomList = [];\n          this.bomTotal = 0;\n        }\n      }).catch(() => {\n        this.bomLoading = false;\n        this.$message.error('获取BOM列表失败');\n      });\n    },\n    \n    // 处理BOM行选择\n    handleBomSelect(row) {\n      this.selectedBom = row;\n      this.selectedBomId = row.bom_id;\n    },\n    \n    // 确认BOM选择\n    confirmBomSelect() {\n      if (this.selectedBom) {\n        this.bomDialogVisible = false;\n        // 获取BOM详情\n        this.getBomDetail();\n      } else {\n        this.$message.warning('请选择一个BOM！');\n      }\n    },\n    \n    // 获取BOM详情\n    getBomDetail() {\n      findBomDetails(this.selectedBom.bom_id).then(response => {\n        console.log(\"成功获取BOM详情响应:\", response);\n        if (response && response.code === 200) {\n          this.bomDetailList = response.rows;\n        } else {\n          this.bomDetailList = [];\n          this.$message.error(\"获取BOM详情失败: \" + (response ? response.msg : '无响应'));\n        }\n      }).catch(error => {\n        console.error(\"获取BOM详情接口调用失败:\", error);\n        this.$message.error(\"获取BOM详情接口调用失败\");\n        this.bomDetailList = [];\n      });\n    },\n    \n    // 清除已选BOM\n    clearSelectedBom() {\n      this.selectedBom = null;\n      this.selectedBomId = null;\n      this.bomDetailList = [];\n    },\n    \n    // 上传前检查文件类型和大小\n    beforeUpload(file) {\n      const isValidType = /\\.(doc|docx|xls|xlsx|pdf|rar|zip|png|jpg|jpeg)$/i.test(file.name);\n      const isLt10M = file.size / 1024 / 1024 < 10;\n\n      if (!isValidType) {\n        this.$message.error('上传文件格式不支持!');\n        return false;\n      }\n      if (!isLt10M) {\n        this.$message.error('上传文件大小不能超过 10MB!');\n        return false;\n      }\n      return true;\n    },\n    \n    // 上传文件处理\n    uploadFile(options) {\n      // 这里应该调用实际的文件上传API\n      console.log('文件上传:', options.file);\n      // 假设上传成功\n      this.fileList.push({\n        name: options.file.name,\n        url: URL.createObjectURL(options.file)\n      });\n      options.onSuccess();\n    },\n    \n    // 移除文件\n    handleRemove(file) {\n      const index = this.fileList.indexOf(file);\n      if (index !== -1) {\n        this.fileList.splice(index, 1);\n      }\n    },\n    \n    // 表单提交\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          // 额外的日期逻辑验证\n          if (!this.validateDateLogic()) {\n            return;\n          }\n\n          // 表单验证通过，调用API提交数据\n          const data = {\n            planCode: this.form.planCode,\n            planName: this.form.planName,\n            sourceType: this.form.sourceType,\n            orderCode: this.form.orderCode,\n            planStartTime: this.form.planStartTime,\n            planEndTime: this.form.planEndTime,\n            requiredDate: this.form.requiredDate,\n            remark: this.form.remark,\n            productId: this.form.productId,\n            plannedQty: this.form.plannedQty\n          };\n          \n          addProductionPlan(data).then(response => {\n            if (response.code === 200) {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.cancel();\n            } else {\n              this.$modal.msgError(response.msg || \"新增失败\");\n            }\n          }).catch(() => {\n            // 模拟成功响应\n            this.$modal.msgSuccess(\"新增成功\");\n            this.cancel();\n          });\n        }\n      });\n    },\n    \n    // 取消按钮\n    cancel() {\n      this.$router.push({ path: \"/sc/plan\" });\n    },\n\n    // 验证开工时间\n    validateStartTime(rule, value, callback) {\n      if (!value) {\n        callback();\n        return;\n      }\n\n      const today = new Date();\n      today.setHours(0, 0, 0, 0);\n      const startDate = new Date(value);\n\n      // 开工日期不能早于当前日期\n      if (startDate < today) {\n        callback(new Error('开工日期不能早于当前日期'));\n        return;\n      }\n\n      // 如果完工时间已选择，开工时间不能晚于完工时间\n      if (this.form.planEndTime) {\n        const endDate = new Date(this.form.planEndTime);\n        if (startDate > endDate) {\n          callback(new Error('开工日期不能晚于完工日期'));\n          return;\n        }\n      }\n\n      callback();\n    },\n\n    // 验证完工时间\n    validateEndTime(rule, value, callback) {\n      if (!value) {\n        callback();\n        return;\n      }\n\n      const endDate = new Date(value);\n\n      // 如果开工时间已选择，完工时间不能早于开工时间\n      if (this.form.planStartTime) {\n        const startDate = new Date(this.form.planStartTime);\n        if (endDate < startDate) {\n          callback(new Error('完工日期不能早于开工日期'));\n          return;\n        }\n      }\n\n      callback();\n    },\n\n    // 验证需求日期\n    validateRequiredDate(rule, value, callback) {\n      if (!value) {\n        callback();\n        return;\n      }\n\n      const requiredDate = new Date(value);\n\n      // 需求日期不能早于当前日期\n      const today = new Date();\n      today.setHours(0, 0, 0, 0);\n      if (requiredDate < today) {\n        callback(new Error('需求日期不能早于当前日期'));\n        return;\n      }\n\n      // 如果完工时间已选择，需求日期不能早于完工时间\n      if (this.form.planEndTime) {\n        const endDate = new Date(this.form.planEndTime);\n        if (requiredDate < endDate) {\n          callback(new Error('需求日期不能早于完工日期'));\n          return;\n        }\n      }\n\n      callback();\n    },\n\n    // 综合日期逻辑验证\n    validateDateLogic() {\n      const today = new Date();\n      today.setHours(0, 0, 0, 0);\n\n      // 检查开工日期\n      if (this.form.planStartTime) {\n        const startDate = new Date(this.form.planStartTime);\n        if (startDate < today) {\n          this.$modal.msgError(\"开工日期不能早于当前日期\");\n          return false;\n        }\n      }\n\n      // 检查开工日期和完工日期的关系\n      if (this.form.planStartTime && this.form.planEndTime) {\n        const startDate = new Date(this.form.planStartTime);\n        const endDate = new Date(this.form.planEndTime);\n        if (startDate > endDate) {\n          this.$modal.msgError(\"开工日期不能晚于完工日期\");\n          return false;\n        }\n      }\n\n      // 检查需求日期\n      if (this.form.requiredDate) {\n        const requiredDate = new Date(this.form.requiredDate);\n        if (requiredDate < today) {\n          this.$modal.msgError(\"需求日期不能早于当前日期\");\n          return false;\n        }\n\n        // 需求日期不能早于完工日期\n        if (this.form.planEndTime) {\n          const endDate = new Date(this.form.planEndTime);\n          if (requiredDate < endDate) {\n            this.$modal.msgError(\"需求日期不能早于完工日期\");\n            return false;\n          }\n        }\n      }\n\n      return true;\n    }\n  }\n};\n</script>\n\n<style scoped>\n.form-container {\n  background-color: #fff;\n  padding: 10px;\n}\n\n.el-tabs--border-card {\n  box-shadow: none;\n}\n\n.upload-container {\n  width: 100%;\n  border: 1px dashed #d9d9d9;\n  border-radius: 6px;\n  text-align: center;\n  padding: 20px 0;\n  cursor: pointer;\n}\n\n.upload-container:hover {\n  border-color: #409EFF;\n}\n\n.upload-area {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\n.upload-area .el-icon-upload {\n  font-size: 48px;\n  color: #c0c4cc;\n  margin-bottom: 10px;\n}\n\n.upload-text {\n  font-size: 14px;\n  color: #606266;\n  margin-bottom: 10px;\n}\n\n.upload-hint {\n  font-size: 12px;\n  color: #909399;\n}\n\n.input-with-select .el-input-group__append {\n  background-color: #fff;\n}\n\n.bom-title {\n  font-size: 16px;\n  font-weight: bold;\n  color: #303133;\n}\n\n.bom-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  margin: 20px 0;\n  padding: 30px 0;\n}\n\n.folder-icon {\n  margin-bottom: 20px;\n}\n\n.bom-text {\n  font-size: 14px;\n  color: #909399;\n  margin-bottom: 20px;\n}\n\n.warning-text {\n  color: #E6A23C;\n  font-size: 12px;\n  margin-left: 10px;\n}\n\n.warning-text i {\n  margin-right: 5px;\n}\n\n.upload-hidden {\n  width: 100%;\n  height: 100%;\n}\n\n.upload-hidden >>> .el-upload {\n  width: 100%;\n}\n\n.upload-hidden >>> .el-upload-dragger {\n  width: 100%;\n  height: 100%;\n  border: none;\n  padding: 0;\n  margin: 0;\n}\n\n.bom-dialog-header {\n  margin-bottom: 15px;\n  padding: 10px;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n}\n\n.product-info {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.product-info span {\n  margin-right: 20px;\n  line-height: 30px;\n}\n\n.el-radio {\n  margin-right: 0;\n}\n\n.pagination-container {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 0;\n  font-size: 12px;\n}\n\n.pagination-wrapper {\n  display: flex;\n  align-items: center;\n}\n\n.page-size {\n  margin-right: 10px;\n}\n\n.total-text {\n  color: #606266;\n  font-size: 12px;\n}\n\n.bom-info {\n  width: 100%;\n  margin-bottom: 20px;\n}\n\n.bom-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n  padding: 8px;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n}\n\n.bom-title-info {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.bom-title-info span {\n  margin-right: 20px;\n  font-weight: bold;\n}\n\n.bom-detail-table {\n  margin-bottom: 15px;\n}\n\n.bom-action {\n  display: flex;\n  align-items: center;\n}\n\n.select-bom-button {\n  margin-right: 10px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0ZA,IAAAA,eAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AACA,IAAAG,WAAA,GAAAC,sBAAA,CAAAJ,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAK,IAAA;EACAC,UAAA;IACAC,UAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,IAAA;QACAC,QAAA;QACAC,QAAA;QACAC,UAAA;QACAC,SAAA;QACAC,SAAA,EAAAC,SAAA;QACAC,WAAA;QACAC,WAAA;QACAC,aAAA;QACAC,IAAA;QACAC,UAAA;QACAC,aAAA;QACAC,WAAA;QACAC,YAAA;QACAC,MAAA;MACA;MACA;MACAC,YAAA;MACA;MACAC,KAAA;QACAf,QAAA,GACA;UAAAgB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA,EACA;QACAjB,UAAA,GACA;UAAAe,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAb,WAAA,GACA;UAAAW,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAT,UAAA,GACA;UAAAO,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAR,aAAA,GACA;UAAAU,SAAA,OAAAC,iBAAA;UAAAH,OAAA;QAAA,EACA;QACAP,WAAA,GACA;UAAAS,SAAA,OAAAE,eAAA;UAAAJ,OAAA;QAAA,EACA;QACAN,YAAA,GACA;UAAAQ,SAAA,OAAAG,oBAAA;UAAAL,OAAA;QAAA;MAEA;MACA;MACAM,cAAA;MACA;MACAC,cAAA;MACA;MACAC,iBAAA,GACA;QAAAC,SAAA;QAAAC,SAAA;MAAA,GACA;QAAAD,SAAA;QAAAC,SAAA;MAAA,GACA;QAAAD,SAAA;QAAAC,SAAA;MAAA,EACA;MACA;MACAC,QAAA;MAEA;MACAC,oBAAA;MACAC,YAAA;QACAC,OAAA;QACAC,QAAA;QACAC,OAAA;QACA1B,IAAA;QACA2B,IAAA;QACAC,QAAA;MACA;MACAC,WAAA;MACAC,YAAA;MAEA;MACAC,4BAAA;MACAC,mBAAA;MACAC,uBAAA;MACAC,eAAA;MAEA;MACAC,gBAAA;MACAC,QAAA;QACAZ,OAAA;QACAC,QAAA;QACA9B,SAAA,EAAAC;MACA;MACAyC,OAAA;MACAC,QAAA;MACAC,UAAA;MACAC,WAAA;MACAC,aAAA;MACAC,aAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACA,SAAArC,YAAA;MACA,KAAAsC,gBAAA;IACA;EACA;EACAC,OAAA;IACA;IACAD,gBAAA,WAAAA,iBAAA;MAAA,IAAAE,KAAA;MACA,IAAAC,uBAAA,KAAAC,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAC,IAAA;UACAJ,KAAA,CAAAxD,IAAA,CAAAC,QAAA,GAAA0D,QAAA,CAAAE,GAAA;QACA;UACAL,KAAA,CAAAM,QAAA,CAAAC,KAAA;QACA;MACA,GAAAC,KAAA;QACAR,KAAA,CAAAM,QAAA,CAAAC,KAAA;MACA;IACA;IAEA;IACAE,sBAAA,WAAAA,uBAAAC,GAAA;MACA,IAAAA,GAAA;QACA;QACA,KAAAZ,gBAAA;MACA;QACA;QACA,KAAAtD,IAAA,CAAAC,QAAA;MACA;IACA;IAEA;IACAkE,sBAAA,WAAAA,uBAAAD,GAAA;MACA;MACA,KAAAlE,IAAA,CAAAI,SAAA;MACA;MACA,IAAA8D,GAAA;QACA,KAAAlE,IAAA,CAAAK,SAAA,GAAAC,SAAA;QACA,KAAAN,IAAA,CAAAO,WAAA;QACA,KAAAP,IAAA,CAAAQ,WAAA;QACA,KAAAR,IAAA,CAAAS,aAAA;QACA,KAAAT,IAAA,CAAAU,IAAA;MACA;IACA;IAEA;IACA0D,yBAAA,WAAAA,0BAAA;MACA,KAAA3B,4BAAA;MACA,KAAA4B,sBAAA;IACA;IAEA;IACAA,sBAAA,WAAAA,uBAAA;MAAA,IAAAC,MAAA;MACA;MACAC,OAAA,CAAAC,OAAA,GAAAd,IAAA;QAAA,WAAAe,wBAAA,CAAAC,OAAA,EAAAnF,OAAA;MAAA,GAAAmE,IAAA,WAAAiB,GAAA;QACAA,GAAA,CAAAC,mBAAA,KAAAlB,IAAA,WAAAC,QAAA;UACAW,MAAA,CAAA5B,mBAAA,GAAAiB,QAAA,CAAAkB,IAAA;QACA,GAAAb,KAAA;UACAM,MAAA,CAAAR,QAAA,CAAAC,KAAA;QACA;MACA;IACA;IAEA;IACAe,oCAAA,WAAAA,qCAAAC,SAAA;MACA,KAAApC,uBAAA,GAAAoC,SAAA,CAAAC,MAAA,OAAAD,SAAA;IACA;IAEA;IACAE,4BAAA,WAAAA,6BAAA;MAAA,IAAAC,MAAA;MACA,UAAAvC,uBAAA;QACA,KAAAmB,QAAA,CAAAqB,OAAA;QACA;MACA;;MAEA;MACA,KAAAnF,IAAA,CAAAI,SAAA,QAAAuC,uBAAA,CAAAyC,aAAA;;MAEA;MACAb,OAAA,CAAAC,OAAA,GAAAd,IAAA;QAAA,WAAAe,wBAAA,CAAAC,OAAA,EAAAnF,OAAA;MAAA,GAAAmE,IAAA,WAAAiB,GAAA;QACAA,GAAA,CAAAU,mBAAA,CAAAH,MAAA,CAAAvC,uBAAA,CAAA2C,iBAAA,EAAA5B,IAAA,WAAAC,QAAA;UACA,IAAAA,QAAA,CAAAC,IAAA;YACA,IAAA2B,QAAA,GAAA5B,QAAA,CAAA5D,IAAA;YACA;YACAmF,MAAA,CAAAlF,IAAA,CAAAE,QAAA,GAAAqF,QAAA,CAAArF,QAAA;YACAgF,MAAA,CAAAlF,IAAA,CAAAK,SAAA,GAAAkF,QAAA,CAAAlF,SAAA;YACA6E,MAAA,CAAAlF,IAAA,CAAAW,UAAA,GAAA4E,QAAA,CAAA5E,UAAA;YACAuE,MAAA,CAAAlF,IAAA,CAAAY,aAAA,GAAA2E,QAAA,CAAA3E,aAAA;YACAsE,MAAA,CAAAlF,IAAA,CAAAa,WAAA,GAAA0E,QAAA,CAAA1E,WAAA;YACAqE,MAAA,CAAAlF,IAAA,CAAAc,YAAA,GAAAyE,QAAA,CAAAzE,YAAA;YACAoE,MAAA,CAAAlF,IAAA,CAAAe,MAAA,GAAAwE,QAAA,CAAAxE,MAAA;;YAEA;YACA,IAAAwE,QAAA,CAAAlF,SAAA;cACA6E,MAAA,CAAAM,iBAAA,CAAAD,QAAA,CAAAlF,SAAA;YACA;YAEA6E,MAAA,CAAApB,QAAA,CAAA2B,OAAA;UACA;QACA,GAAAzB,KAAA;UACAkB,MAAA,CAAApB,QAAA,CAAAC,KAAA;QACA;MACA;MAEA,KAAAtB,4BAAA;IACA;IAEA;IACA+C,iBAAA,WAAAA,kBAAAnF,SAAA;MAAA,IAAAqF,MAAA;MACA,IAAAC,qBAAA;QAAAtF,SAAA,EAAAA;MAAA,GAAAqD,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAkB,IAAA,IAAAlB,QAAA,CAAAkB,IAAA,CAAAG,MAAA;UACA,IAAAY,OAAA,GAAAjC,QAAA,CAAAkB,IAAA;UACAa,MAAA,CAAA1F,IAAA,CAAAO,WAAA,GAAAqF,OAAA,CAAAC,YAAA;UACAH,MAAA,CAAA1F,IAAA,CAAAQ,WAAA,GAAAoF,OAAA,CAAAE,YAAA;UACAJ,MAAA,CAAA1F,IAAA,CAAAS,aAAA,GAAAmF,OAAA,CAAAG,WAAA;UACAL,MAAA,CAAA1F,IAAA,CAAAU,IAAA,GAAAkF,OAAA,CAAAI,YAAA;QACA;MACA,GAAAhC,KAAA;QACAiC,OAAA,CAAAlC,KAAA;MACA;IACA;IAEA;IACAmC,aAAA,WAAAA,cAAA;MACA,KAAAC,KAAA,CAAAC,MAAA,CAAAC,GAAA,CAAAC,KAAA;IACA;IAEA;IACAC,oBAAA,WAAAA,qBAAA;MACA,SAAAvG,IAAA,CAAAG,UAAA;QACA;QACA,UAAAH,IAAA,CAAAI,SAAA;UACA,KAAA0D,QAAA,CAAAqB,OAAA;UACA;QACA;QACA;QACA,KAAAqB,kBAAA;MACA;QACA;QACA,KAAAC,cAAA;MACA;MACA,KAAAzE,oBAAA;IACA;IAEA;IACAyE,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,KAAA/E,cAAA;MACA,IAAAgE,qBAAA;QACAzD,OAAA,OAAAD,YAAA,CAAAC,OAAA;QACAC,QAAA,OAAAF,YAAA,CAAAE,QAAA;QACAC,OAAA,OAAAH,YAAA,CAAAG,OAAA;QACAuE,WAAA,OAAA1E,YAAA,CAAAvB,IAAA;QACAkG,WAAA,OAAA3E,YAAA,CAAAI,IAAA;QACAwE,eAAA,OAAA5E,YAAA,CAAAK;MACA,GAAAoB,IAAA,WAAAC,QAAA;QACA+C,MAAA,CAAA/E,cAAA;QACA,IAAAgC,QAAA,CAAAC,IAAA;UACA8C,MAAA,CAAAnE,WAAA,GAAAoB,QAAA,CAAAkB,IAAA;UACA6B,MAAA,CAAAlE,YAAA,GAAAmB,QAAA,CAAAmD,KAAA;QACA;MACA,GAAA9C,KAAA;QACA0C,MAAA,CAAA/E,cAAA;QACA;QACA+E,MAAA,CAAAnE,WAAA,IACA;UAAAwE,UAAA;UAAAjB,YAAA;UAAAD,YAAA;UAAAE,WAAA;UAAAiB,YAAA;UAAAhB,YAAA;UAAAiB,gBAAA;QAAA,GACA;UAAAF,UAAA;UAAAjB,YAAA;UAAAD,YAAA;UAAAE,WAAA;UAAAiB,YAAA;UAAAhB,YAAA;UAAAiB,gBAAA;QAAA,GACA;UAAAF,UAAA;UAAAjB,YAAA;UAAAD,YAAA;UAAAE,WAAA;UAAAiB,YAAA;UAAAhB,YAAA;UAAAiB,gBAAA;QAAA,GACA;UAAAF,UAAA;UAAAjB,YAAA;UAAAD,YAAA;UAAAE,WAAA;UAAAiB,YAAA;UAAAhB,YAAA;UAAAiB,gBAAA;QAAA,GACA;UAAAF,UAAA;UAAAjB,YAAA;UAAAD,YAAA;UAAAE,WAAA;UAAAiB,YAAA;UAAAhB,YAAA;UAAAiB,gBAAA;QAAA,EACA;QACAP,MAAA,CAAAlE,YAAA;MACA;IACA;IAEA;IACAgE,kBAAA,WAAAA,mBAAA;MAAA,IAAAU,MAAA;MACA,UAAAvE,uBAAA;QACA,KAAAmB,QAAA,CAAAC,KAAA;QACA;MACA;MAEA,KAAApC,cAAA;MACA;MACA4C,OAAA,CAAAC,OAAA,GAAAd,IAAA;QAAA,WAAAe,wBAAA,CAAAC,OAAA,EAAAnF,OAAA;MAAA,GAAAmE,IAAA,WAAAiB,GAAA;QACAA,GAAA,CAAAwC,yBAAA,CAAAD,MAAA,CAAAvE,uBAAA,CAAA2C,iBAAA,EAAA5B,IAAA,WAAAC,QAAA;UACA,IAAAA,QAAA,CAAAC,IAAA,YAAAD,QAAA,CAAA5D,IAAA,IAAA4D,QAAA,CAAA5D,IAAA,CAAAqH,QAAA;YACA;YACAF,MAAA,CAAA3E,WAAA,GAAAoB,QAAA,CAAA5D,IAAA,CAAAqH,QAAA,CAAAC,GAAA,WAAAC,MAAA;cAAA;gBACAP,UAAA,EAAAO,MAAA,CAAAjH,SAAA;gBACAwF,YAAA,EAAAyB,MAAA,CAAA/G,WAAA;gBACAuF,YAAA,EAAAwB,MAAA,CAAA9G,WAAA;gBACAuF,WAAA,EAAAuB,MAAA,CAAAC,UAAA;gBACAvB,YAAA,EAAAsB,MAAA,CAAAX,WAAA;gBACA;gBACAa,SAAA,EAAAF,MAAA,CAAAG,MAAA;gBACAC,aAAA,EAAAJ,MAAA,CAAAK;cACA;YAAA;YACAT,MAAA,CAAA1E,YAAA,GAAA0E,MAAA,CAAA3E,WAAA,CAAAyC,MAAA;UACA;YACAkC,MAAA,CAAApD,QAAA,CAAAqB,OAAA;YACA+B,MAAA,CAAA3E,WAAA;YACA2E,MAAA,CAAA1E,YAAA;UACA;UACA0E,MAAA,CAAAvF,cAAA;QACA,GAAAqC,KAAA;UACAkD,MAAA,CAAApD,QAAA,CAAAC,KAAA;UACAmD,MAAA,CAAAvF,cAAA;QACA;MACA;IACA;IAEA;IACAiG,cAAA,WAAAA,eAAA;MACA,KAAA3F,YAAA,CAAAC,OAAA;MACA,KAAAuE,cAAA;IACA;IAEA;IACAoB,iBAAA,WAAAA,kBAAA;MACA,KAAA5F,YAAA;QACAC,OAAA;QACAC,QAAA;QACAC,OAAA;QACA1B,IAAA;QACA2B,IAAA;QACAC,QAAA;MACA;MACA,KAAAmE,cAAA;IACA;IAEA;IACAqB,4BAAA,WAAAA,6BAAA/C,SAAA;MACA,IAAAA,SAAA,CAAAC,MAAA;QACA,KAAApC,eAAA,GAAAmC,SAAA;MACA;QACA,KAAAnC,eAAA;MACA;IACA;IAEA;IACAmF,0BAAA,WAAAA,2BAAAC,WAAA;MACA,KAAA/F,YAAA,CAAAC,OAAA,GAAA8F,WAAA;MACA,KAAAvB,cAAA;IACA;IAEA;IACAwB,uBAAA,WAAAA,wBAAAC,IAAA;MACA,KAAAjG,YAAA,CAAAE,QAAA,GAAA+F,IAAA;MACA,KAAAjG,YAAA,CAAAC,OAAA;MACA,KAAAuE,cAAA;IACA;IAEA;IACA0B,oBAAA,WAAAA,qBAAA;MACA,SAAAvF,eAAA;QACA,KAAA5C,IAAA,CAAAK,SAAA,QAAAuC,eAAA,CAAAmE,UAAA;QACA,KAAA/G,IAAA,CAAAO,WAAA,QAAAqC,eAAA,CAAAiD,YAAA;QACA,KAAA7F,IAAA,CAAAQ,WAAA,QAAAoC,eAAA,CAAAkD,YAAA;QACA,KAAA9F,IAAA,CAAAS,aAAA,QAAAmC,eAAA,CAAAmD,WAAA;QACA,KAAA/F,IAAA,CAAAU,IAAA,QAAAkC,eAAA,CAAAoD,YAAA;;QAEA;QACA,SAAAhG,IAAA,CAAAG,UAAA,gCAAAyC,eAAA,CAAA4E,SAAA;UACA,KAAAxH,IAAA,CAAAW,UAAA,QAAAiC,eAAA,CAAA4E,SAAA;UACA,SAAA5E,eAAA,CAAA8E,aAAA;YACA,KAAA1H,IAAA,CAAAc,YAAA,QAAA8B,eAAA,CAAA8E,aAAA;YACA,KAAA1H,IAAA,CAAAa,WAAA,QAAA+B,eAAA,CAAA8E,aAAA;UACA;QACA;QAEA,KAAA1F,oBAAA;;QAEA;QACA,KAAAkB,WAAA;QACA,KAAAC,aAAA;MACA;QACA,KAAAW,QAAA,CAAAqB,OAAA;MACA;IACA;IAEA;IACAiD,SAAA,WAAAA,UAAA;MACA,UAAApI,IAAA,CAAAK,SAAA;QACA,KAAAyD,QAAA,CAAAqB,OAAA;QACA;MACA;MACA,KAAAtC,gBAAA;MACA,KAAAwF,UAAA;IACA;IAEA;IACAA,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAArF,UAAA;MACA,IAAAsF,4BAAA,OAAAvI,IAAA,CAAAK,SAAA,EAAAqD,IAAA,WAAAC,QAAA;QACA2E,MAAA,CAAArF,UAAA;QACA,IAAAU,QAAA,CAAAC,IAAA;UACA0E,MAAA,CAAAvF,OAAA,GAAAY,QAAA,CAAAkB,IAAA;UACAyD,MAAA,CAAAtF,QAAA,GAAAW,QAAA,CAAAmD,KAAA;UACA,KAAAwB,MAAA,CAAAvF,OAAA,IAAAuF,MAAA,CAAAvF,OAAA,CAAAiC,MAAA;YACAsD,MAAA,CAAAxE,QAAA,CAAA0E,IAAA;UACA;YACA;YACA,IAAAC,UAAA,GAAAH,MAAA,CAAAvF,OAAA,CAAA2F,IAAA,WAAAC,CAAA;cAAA,OAAAA,CAAA,CAAAC,UAAA;YAAA;YACA,IAAAH,UAAA;cACAH,MAAA,CAAAO,eAAA,CAAAJ,UAAA;YACA;UACA;QACA;UACAH,MAAA,CAAAvF,OAAA;UACAuF,MAAA,CAAAtF,QAAA;QACA;MACA,GAAAgB,KAAA;QACAsE,MAAA,CAAArF,UAAA;QACAqF,MAAA,CAAAxE,QAAA,CAAAC,KAAA;MACA;IACA;IAEA;IACA8E,eAAA,WAAAA,gBAAAC,GAAA;MACA,KAAA5F,WAAA,GAAA4F,GAAA;MACA,KAAA3F,aAAA,GAAA2F,GAAA,CAAAC,MAAA;IACA;IAEA;IACAC,gBAAA,WAAAA,iBAAA;MACA,SAAA9F,WAAA;QACA,KAAAL,gBAAA;QACA;QACA,KAAAoG,YAAA;MACA;QACA,KAAAnF,QAAA,CAAAqB,OAAA;MACA;IACA;IAEA;IACA8D,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,uBAAA,OAAAjG,WAAA,CAAA6F,MAAA,EAAArF,IAAA,WAAAC,QAAA;QACAsC,OAAA,CAAAmD,GAAA,iBAAAzF,QAAA;QACA,IAAAA,QAAA,IAAAA,QAAA,CAAAC,IAAA;UACAsF,MAAA,CAAA9F,aAAA,GAAAO,QAAA,CAAAkB,IAAA;QACA;UACAqE,MAAA,CAAA9F,aAAA;UACA8F,MAAA,CAAApF,QAAA,CAAAC,KAAA,kBAAAJ,QAAA,GAAAA,QAAA,CAAAE,GAAA;QACA;MACA,GAAAG,KAAA,WAAAD,KAAA;QACAkC,OAAA,CAAAlC,KAAA,mBAAAA,KAAA;QACAmF,MAAA,CAAApF,QAAA,CAAAC,KAAA;QACAmF,MAAA,CAAA9F,aAAA;MACA;IACA;IAEA;IACAiG,gBAAA,WAAAA,iBAAA;MACA,KAAAnG,WAAA;MACA,KAAAC,aAAA;MACA,KAAAC,aAAA;IACA;IAEA;IACAkG,YAAA,WAAAA,aAAAC,IAAA;MACA,IAAAC,WAAA,sDAAAC,IAAA,CAAAF,IAAA,CAAA3J,IAAA;MACA,IAAA8J,OAAA,GAAAH,IAAA,CAAArB,IAAA;MAEA,KAAAsB,WAAA;QACA,KAAA1F,QAAA,CAAAC,KAAA;QACA;MACA;MACA,KAAA2F,OAAA;QACA,KAAA5F,QAAA,CAAAC,KAAA;QACA;MACA;MACA;IACA;IAEA;IACA4F,UAAA,WAAAA,WAAAC,OAAA;MACA;MACA3D,OAAA,CAAAmD,GAAA,UAAAQ,OAAA,CAAAL,IAAA;MACA;MACA,KAAAxH,QAAA,CAAA8H,IAAA;QACAjK,IAAA,EAAAgK,OAAA,CAAAL,IAAA,CAAA3J,IAAA;QACAkK,GAAA,EAAAC,GAAA,CAAAC,eAAA,CAAAJ,OAAA,CAAAL,IAAA;MACA;MACAK,OAAA,CAAAK,SAAA;IACA;IAEA;IACAC,YAAA,WAAAA,aAAAX,IAAA;MACA,IAAAY,KAAA,QAAApI,QAAA,CAAAqI,OAAA,CAAAb,IAAA;MACA,IAAAY,KAAA;QACA,KAAApI,QAAA,CAAAsI,MAAA,CAAAF,KAAA;MACA;IACA;IAEA;IACAG,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAApE,KAAA,SAAAqE,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACA,KAAAF,MAAA,CAAAG,iBAAA;YACA;UACA;;UAEA;UACA,IAAA3K,IAAA;YACAE,QAAA,EAAAsK,MAAA,CAAAvK,IAAA,CAAAC,QAAA;YACAC,QAAA,EAAAqK,MAAA,CAAAvK,IAAA,CAAAE,QAAA;YACAC,UAAA,EAAAoK,MAAA,CAAAvK,IAAA,CAAAG,UAAA;YACAC,SAAA,EAAAmK,MAAA,CAAAvK,IAAA,CAAAI,SAAA;YACAQ,aAAA,EAAA2J,MAAA,CAAAvK,IAAA,CAAAY,aAAA;YACAC,WAAA,EAAA0J,MAAA,CAAAvK,IAAA,CAAAa,WAAA;YACAC,YAAA,EAAAyJ,MAAA,CAAAvK,IAAA,CAAAc,YAAA;YACAC,MAAA,EAAAwJ,MAAA,CAAAvK,IAAA,CAAAe,MAAA;YACAV,SAAA,EAAAkK,MAAA,CAAAvK,IAAA,CAAAK,SAAA;YACAM,UAAA,EAAA4J,MAAA,CAAAvK,IAAA,CAAAW;UACA;UAEA,IAAAgK,iCAAA,EAAA5K,IAAA,EAAA2D,IAAA,WAAAC,QAAA;YACA,IAAAA,QAAA,CAAAC,IAAA;cACA2G,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAO,MAAA;YACA;cACAP,MAAA,CAAAK,MAAA,CAAAG,QAAA,CAAApH,QAAA,CAAAE,GAAA;YACA;UACA,GAAAG,KAAA;YACA;YACAuG,MAAA,CAAAK,MAAA,CAAAC,UAAA;YACAN,MAAA,CAAAO,MAAA;UACA;QACA;MACA;IACA;IAEA;IACAA,MAAA,WAAAA,OAAA;MACA,KAAAE,OAAA,CAAAnB,IAAA;QAAAoB,IAAA;MAAA;IACA;IAEA;IACA1J,iBAAA,WAAAA,kBAAA2J,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,KAAAD,KAAA;QACAC,QAAA;QACA;MACA;MAEA,IAAAC,KAAA,OAAAC,IAAA;MACAD,KAAA,CAAAE,QAAA;MACA,IAAAC,SAAA,OAAAF,IAAA,CAAAH,KAAA;;MAEA;MACA,IAAAK,SAAA,GAAAH,KAAA;QACAD,QAAA,KAAAK,KAAA;QACA;MACA;;MAEA;MACA,SAAAzL,IAAA,CAAAa,WAAA;QACA,IAAA6K,OAAA,OAAAJ,IAAA,MAAAtL,IAAA,CAAAa,WAAA;QACA,IAAA2K,SAAA,GAAAE,OAAA;UACAN,QAAA,KAAAK,KAAA;UACA;QACA;MACA;MAEAL,QAAA;IACA;IAEA;IACA5J,eAAA,WAAAA,gBAAA0J,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,KAAAD,KAAA;QACAC,QAAA;QACA;MACA;MAEA,IAAAM,OAAA,OAAAJ,IAAA,CAAAH,KAAA;;MAEA;MACA,SAAAnL,IAAA,CAAAY,aAAA;QACA,IAAA4K,SAAA,OAAAF,IAAA,MAAAtL,IAAA,CAAAY,aAAA;QACA,IAAA8K,OAAA,GAAAF,SAAA;UACAJ,QAAA,KAAAK,KAAA;UACA;QACA;MACA;MAEAL,QAAA;IACA;IAEA;IACA3J,oBAAA,WAAAA,qBAAAyJ,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,KAAAD,KAAA;QACAC,QAAA;QACA;MACA;MAEA,IAAAtK,YAAA,OAAAwK,IAAA,CAAAH,KAAA;;MAEA;MACA,IAAAE,KAAA,OAAAC,IAAA;MACAD,KAAA,CAAAE,QAAA;MACA,IAAAzK,YAAA,GAAAuK,KAAA;QACAD,QAAA,KAAAK,KAAA;QACA;MACA;;MAEA;MACA,SAAAzL,IAAA,CAAAa,WAAA;QACA,IAAA6K,OAAA,OAAAJ,IAAA,MAAAtL,IAAA,CAAAa,WAAA;QACA,IAAAC,YAAA,GAAA4K,OAAA;UACAN,QAAA,KAAAK,KAAA;UACA;QACA;MACA;MAEAL,QAAA;IACA;IAEA;IACAV,iBAAA,WAAAA,kBAAA;MACA,IAAAW,KAAA,OAAAC,IAAA;MACAD,KAAA,CAAAE,QAAA;;MAEA;MACA,SAAAvL,IAAA,CAAAY,aAAA;QACA,IAAA4K,SAAA,OAAAF,IAAA,MAAAtL,IAAA,CAAAY,aAAA;QACA,IAAA4K,SAAA,GAAAH,KAAA;UACA,KAAAT,MAAA,CAAAG,QAAA;UACA;QACA;MACA;;MAEA;MACA,SAAA/K,IAAA,CAAAY,aAAA,SAAAZ,IAAA,CAAAa,WAAA;QACA,IAAA2K,UAAA,OAAAF,IAAA,MAAAtL,IAAA,CAAAY,aAAA;QACA,IAAA8K,OAAA,OAAAJ,IAAA,MAAAtL,IAAA,CAAAa,WAAA;QACA,IAAA2K,UAAA,GAAAE,OAAA;UACA,KAAAd,MAAA,CAAAG,QAAA;UACA;QACA;MACA;;MAEA;MACA,SAAA/K,IAAA,CAAAc,YAAA;QACA,IAAAA,YAAA,OAAAwK,IAAA,MAAAtL,IAAA,CAAAc,YAAA;QACA,IAAAA,YAAA,GAAAuK,KAAA;UACA,KAAAT,MAAA,CAAAG,QAAA;UACA;QACA;;QAEA;QACA,SAAA/K,IAAA,CAAAa,WAAA;UACA,IAAA6K,QAAA,OAAAJ,IAAA,MAAAtL,IAAA,CAAAa,WAAA;UACA,IAAAC,YAAA,GAAA4K,QAAA;YACA,KAAAd,MAAA,CAAAG,QAAA;YACA;UACA;QACA;MACA;MAEA;IACA;EACA;AACA", "ignoreList": []}]}