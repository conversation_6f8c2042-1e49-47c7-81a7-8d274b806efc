{"remainingRequest": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\src\\views\\sc\\plan\\add_plan.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\src\\views\\sc\\plan\\add_plan.vue", "mtime": 1753786851612}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\babel.config.js", "mtime": 1749629472348}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751945356000}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751945361444}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751945356000}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751945364156}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_productionPlan", "require", "_product", "_numbers", "_Pagination", "_interopRequireDefault", "name", "components", "Pagination", "data", "form", "planCode", "planName", "sourceType", "orderCode", "productId", "undefined", "productName", "productCode", "specification", "unit", "plannedQty", "planStartTime", "planEndTime", "requiredDate", "remark", "orderQty", "isSystemCode", "rules", "required", "message", "trigger", "max", "validator", "validatePlannedQty", "validateStartTime", "validateEndTime", "validateRequiredDate", "productOptions", "productLoading", "sourceTypeOptions", "dict<PERSON><PERSON>l", "dict<PERSON><PERSON>ue", "fileList", "productDialogVisible", "productQuery", "pageNum", "pageSize", "keyword", "type", "property", "productList", "productTotal", "productionOrderDialogVisible", "productionOrderList", "selectedProductionOrder", "selectedProduct", "bomDialogVisible", "b<PERSON><PERSON><PERSON><PERSON>", "bomList", "bomTotal", "bomLoading", "selectedBom", "selectedBomId", "bomDetailList", "created", "generatePlanCode", "methods", "_this", "getAutoNumbers", "then", "response", "code", "msg", "$message", "error", "catch", "handleSystemCodeChange", "val", "handleSourceTypeChange", "openProductionOrderDialog", "getProductionOrderList", "_this2", "Promise", "resolve", "_interopRequireWildcard2", "default", "api", "listProductionOrder", "rows", "handleProductionOrderSelectionChange", "selection", "length", "confirmProductionOrderSelect", "_this3", "warning", "prodOrderCode", "createPlanFromOrder", "productionOrderId", "planData", "getProductDetails", "success", "_this4", "listProducts", "product", "product_name", "product_code", "product_sfn", "product_unit", "console", "triggerUpload", "$refs", "upload", "$el", "click", "openProductSelection", "getProductsByOrder", "getProductList", "_this5", "productUnit", "productType", "productProperty", "total", "product_id", "product_type", "product_property", "_this6", "getProductionOrderDetails", "products", "map", "detail", "productSfn", "order_qty", "qtyNum", "delivery_date", "deliveryDate", "searchProducts", "resetProduct<PERSON>uery", "handleProductSelectionChange", "handleProductCurrentChange", "currentPage", "handleProductSizeChange", "size", "confirmProductSelect", "selectBom", "getBomList", "_this7", "listBomsByProductId", "info", "defaultBom", "find", "b", "bom_status", "handleBomSelect", "row", "bom_id", "confirmBomSelect", "getBomDetail", "_this8", "findBomDetails", "log", "clearSelectedBom", "beforeUpload", "file", "isValidType", "test", "isLt10M", "uploadFile", "options", "push", "url", "URL", "createObjectURL", "onSuccess", "handleRemove", "index", "indexOf", "splice", "submitForm", "_this9", "validate", "valid", "validateDateLogic", "addProductionPlan", "$modal", "msgSuccess", "cancel", "msgError", "$router", "path", "rule", "value", "callback", "Number", "Error", "concat", "today", "Date", "setHours", "startDate", "endDate"], "sources": ["src/views/sc/plan/add_plan.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <div class=\"form-container\">\n      <!-- 基础信息区 -->\n      <el-tabs type=\"border-card\">\n        <el-tab-pane>\n          <span slot=\"label\"><i class=\"el-icon-date\"></i> 基础信息</span>\n          <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\n            <el-row :gutter=\"20\">\n              <el-col :span=\"8\">\n                <el-form-item label=\"计划编号\" prop=\"planCode\" required>\n                  <el-input v-model=\"form.planCode\" placeholder=\"请输入\" :disabled=\"isSystemCode\"></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"4\">\n                <el-switch\n                  v-model=\"isSystemCode\"\n                  active-text=\"系统编号\"\n                  inactive-text=\"\"\n                  style=\"margin-top: 13px;\"\n                  @change=\"handleSystemCodeChange\"\n                ></el-switch>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"计划名称\" prop=\"planName\" required>\n                  <el-input v-model=\"form.planName\" placeholder=\"请输入\"></el-input>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-row :gutter=\"20\">\n              <el-col :span=\"12\">\n                <el-form-item label=\"来源类型\" prop=\"sourceType\" required>\n                  <el-select v-model=\"form.sourceType\" placeholder=\"生产订单\" style=\"width: 100%\" @change=\"handleSourceTypeChange\">\n                    <el-option\n                      v-for=\"item in sourceTypeOptions\"\n                      :key=\"item.dictValue\"\n                      :label=\"item.dictLabel\"\n                      :value=\"item.dictValue\"\n                    ></el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"订单编号\" prop=\"orderCode\">\n                  <el-input v-model=\"form.orderCode\" placeholder=\"请输入\">\n                    <el-button\n                      v-if=\"form.sourceType === 'PRODUCTION_ORDER'\"\n                      slot=\"append\"\n                      icon=\"el-icon-search\"\n                      @click=\"openProductionOrderDialog\">\n                      选择\n                    </el-button>\n                  </el-input>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-row :gutter=\"20\">\n              <el-col :span=\"12\">\n                <el-form-item label=\"成品名称\" prop=\"productName\">\n                  <el-input\n                    placeholder=\"请选择成品\"\n                    v-model=\"form.productName\"\n                    class=\"input-with-select\"\n                  >\n                    <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"openProductSelection\"></el-button>\n                  </el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"成品编号\" prop=\"productCode\">\n                  <el-input v-model=\"form.productCode\" placeholder=\"请输入\" disabled></el-input>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-row :gutter=\"20\">\n              <el-col :span=\"12\">\n                <el-form-item label=\"规格型号\" prop=\"specification\">\n                  <el-input v-model=\"form.specification\" placeholder=\"请输入\" disabled></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"单位\" prop=\"unit\">\n                  <el-input v-model=\"form.unit\" placeholder=\"请输入\" disabled></el-input>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-row :gutter=\"20\">\n              <el-col :span=\"12\">\n                <el-form-item label=\"计划数量\" prop=\"plannedQty\" required>\n                  <el-input-number v-model=\"form.plannedQty\" :min=\"1\" controls-position=\"right\" style=\"width: 100%\"></el-input-number>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-row :gutter=\"20\">\n              <el-col :span=\"12\">\n                <el-form-item label=\"开工时间\" prop=\"planStartTime\">\n                  <el-date-picker\n                    v-model=\"form.planStartTime\"\n                    type=\"date\"\n                    placeholder=\"请选择日期\"\n                    style=\"width: 100%\"\n                    value-format=\"yyyy-MM-dd\"\n                  ></el-date-picker>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"完工时间\" prop=\"planEndTime\">\n                  <el-date-picker\n                    v-model=\"form.planEndTime\"\n                    type=\"date\"\n                    placeholder=\"请选择日期\"\n                    style=\"width: 100%\"\n                    value-format=\"yyyy-MM-dd\"\n                  ></el-date-picker>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-row :gutter=\"20\">\n              <el-col :span=\"12\">\n                <el-form-item label=\"需求日期\" prop=\"requiredDate\">\n                  <el-date-picker\n                    v-model=\"form.requiredDate\"\n                    type=\"date\"\n                    placeholder=\"请选择日期\"\n                    style=\"width: 100%\"\n                    value-format=\"yyyy-MM-dd\"\n                  ></el-date-picker>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-row :gutter=\"20\">\n              <el-col :span=\"24\">\n                <el-form-item label=\"备注\" prop=\"remark\">\n                  <el-input\n                    type=\"textarea\"\n                    v-model=\"form.remark\"\n                    placeholder=\"请输入\"\n                    :rows=\"4\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-row :gutter=\"20\">\n              <el-col :span=\"24\">\n                <el-form-item label=\"附件\" prop=\"attachment\">\n                  <div class=\"upload-container\" @click=\"triggerUpload\">\n                    <el-upload\n                      ref=\"upload\"\n                      class=\"upload-hidden\"\n                      action=\"#\"\n                      :http-request=\"uploadFile\"\n                      :file-list=\"fileList\"\n                      :before-upload=\"beforeUpload\"\n                      :on-remove=\"handleRemove\"\n                      multiple\n                      drag\n                    >\n                      <div class=\"upload-area\">\n                        <i class=\"el-icon-upload\"></i>\n                        <div class=\"upload-text\">点击或者拖动文件到虚线框内上传</div>\n                        <div class=\"upload-hint\">支持 docx, xls, PDF, rar, zip, PNG, JPG 等类型的文件</div>\n                      </div>\n                    </el-upload>\n                  </div>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            \n            <el-divider>\n              <span class=\"bom-title\">BOM组成</span>\n            </el-divider>\n            \n            <el-row>\n              <el-col :span=\"24\">\n                <div class=\"bom-container\">\n                  <div class=\"folder-icon\" v-if=\"!selectedBom\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"60\" height=\"60\" viewBox=\"0 0 24 24\" fill=\"#DCDFE6\">\n                      <path d=\"M10 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2h-8l-2-2z\"/>\n                    </svg>\n                  </div>\n                  <div class=\"bom-text\" v-if=\"!selectedBom\">暂无数据</div>\n                  <div class=\"bom-info\" v-else>\n                    <div class=\"bom-header\">\n                      <div class=\"bom-title-info\">\n                        <span>BOM编号：{{ selectedBom.bom_code }}</span>\n                        <span>版本号：{{ selectedBom.bom_version }}</span>\n                      </div>\n                      <el-button type=\"text\" icon=\"el-icon-delete\" @click=\"clearSelectedBom\">清除</el-button>\n                    </div>\n                    <el-table\n                      :data=\"bomDetailList\"\n                      border\n                      size=\"small\"\n                      style=\"width: 100%\"\n                      class=\"bom-detail-table\"\n                    >\n                      <el-table-column label=\"序号\" type=\"index\" width=\"50\" align=\"center\"></el-table-column>\n                      <el-table-column prop=\"material_code\" label=\"物料编码\" min-width=\"120\" align=\"center\"></el-table-column>\n                      <el-table-column prop=\"material_name\" label=\"物料名称\" min-width=\"150\" align=\"center\"></el-table-column>\n                      <el-table-column prop=\"specification\" label=\"规格型号\" min-width=\"100\" align=\"center\"></el-table-column>\n                      <el-table-column prop=\"unit\" label=\"单位\" width=\"60\" align=\"center\"></el-table-column>\n                      <el-table-column prop=\"quantity\" label=\"用量\" width=\"80\" align=\"center\"></el-table-column>\n                      <el-table-column prop=\"remark\" label=\"备注\" min-width=\"120\" align=\"center\"></el-table-column>\n                    </el-table>\n                  </div>\n                  <div class=\"bom-action\">\n                    <el-tooltip :disabled=\"form.productId\" content=\"请先选择成品!\" placement=\"right\" effect=\"light\">\n                      <el-button type=\"primary\" class=\"select-bom-button\" @click=\"selectBom\">选择BOM</el-button>\n                    </el-tooltip>\n                  </div>\n                </div>\n              </el-col>\n            </el-row>\n            \n            <el-row>\n              <el-col :span=\"24\" style=\"text-align: center; margin-top: 20px;\">\n                <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n                <el-button @click=\"cancel\">取 消</el-button>\n              </el-col>\n            </el-row>\n          </el-form>\n        </el-tab-pane>\n      </el-tabs>\n    </div>\n    \n    <!-- 产品选择对话框 -->\n    <el-dialog title=\"选择成品\" :visible.sync=\"productDialogVisible\" width=\"40%\" append-to-body>\n      <el-form :model=\"productQuery\" ref=\"productQueryForm\" :inline=\"true\" class=\"demo-form-inline\" size=\"small\">\n        <el-form-item>\n          <el-input v-model=\"productQuery.keyword\" placeholder=\"请输入产品编号/名称\" clearable style=\"width: 180px;\"></el-input>\n        </el-form-item>\n        <el-form-item>\n          <el-select v-model=\"productQuery.unit\" placeholder=\"请选择单位\" clearable style=\"width: 120px;\">\n            <el-option label=\"个\" value=\"个\"></el-option>\n            <el-option label=\"件\" value=\"件\"></el-option>\n            <el-option label=\"台\" value=\"台\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item>\n          <el-select v-model=\"productQuery.type\" placeholder=\"请选择类型\" clearable style=\"width: 120px;\">\n            <el-option label=\"成品\" value=\"成品\"></el-option>\n            <el-option label=\"半成品\" value=\"半成品\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item>\n          <el-select v-model=\"productQuery.property\" placeholder=\"请选择产品属性\" clearable style=\"width: 120px;\">\n            <el-option label=\"自制\" value=\"自制\"></el-option>\n            <el-option label=\"外购\" value=\"外购\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"small\" @click=\"searchProducts\">查询</el-button>\n          <el-button icon=\"el-icon-refresh\" size=\"small\" @click=\"resetProductQuery\">重置</el-button>\n        </el-form-item>\n      </el-form>\n      \n      <el-table\n        v-loading=\"productLoading\"\n        :data=\"productList\"\n        border\n        size=\"small\"\n        style=\"width: 100%\"\n        @selection-change=\"handleProductSelectionChange\"\n        height=\"300\"\n      >\n        <el-table-column type=\"selection\" width=\"40\" align=\"center\"></el-table-column>\n        <el-table-column label=\"序号\" type=\"index\" width=\"40\" align=\"center\"></el-table-column>\n        <el-table-column prop=\"product_code\" label=\"产品编号\" width=\"90\" align=\"center\"></el-table-column>\n        <el-table-column prop=\"product_name\" label=\"产品名称\" min-width=\"120\" align=\"center\"></el-table-column>\n        <el-table-column prop=\"product_sfn\" label=\"规格型号\" min-width=\"90\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <span :style=\"{color: '#1890ff'}\">{{ scope.row.product_sfn }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"product_unit\" label=\"单位\" width=\"60\" align=\"center\"></el-table-column>\n        <!-- 生产订单产品特有的列 -->\n        <el-table-column v-if=\"form.sourceType === 'PRODUCTION_ORDER'\" prop=\"order_qty\" label=\"订单数量\" width=\"80\" align=\"center\"></el-table-column>\n        <el-table-column v-if=\"form.sourceType === 'PRODUCTION_ORDER'\" prop=\"delivery_date\" label=\"交付日期\" width=\"100\" align=\"center\">\n          <template slot-scope=\"scope\">\n            {{ scope.row.delivery_date ? scope.row.delivery_date.substring(0, 10) : '' }}\n          </template>\n        </el-table-column>\n      </el-table>\n      \n      <div class=\"pagination-container\">\n        <span class=\"total-text\">共 {{ productTotal }} 条</span>\n        <div class=\"pagination-wrapper\">\n          <span class=\"page-size\">\n            <el-select v-model=\"productQuery.pageSize\" size=\"mini\" @change=\"handleProductSizeChange\" style=\"width: 80px;\">\n              <el-option\n                v-for=\"item in [10, 20, 30, 50]\"\n                :key=\"item\"\n                :label=\"`${item}条/页`\"\n                :value=\"item\">\n              </el-option>\n            </el-select>\n          </span>\n          <el-pagination\n            small\n            background\n            @current-change=\"handleProductCurrentChange\"\n            :current-page=\"productQuery.pageNum\"\n            :page-size=\"productQuery.pageSize\"\n            layout=\"prev, pager, next, jumper\"\n            :pager-count=\"5\"\n            :total=\"productTotal\">\n          </el-pagination>\n        </div>\n      </div>\n      \n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button size=\"small\" @click=\"productDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" size=\"small\" @click=\"confirmProductSelect\">确定</el-button>\n      </div>\n    </el-dialog>\n    \n    <!-- BOM选择对话框 -->\n    <el-dialog title=\"选择BOM\" :visible.sync=\"bomDialogVisible\" width=\"40%\" append-to-body>\n      <div class=\"bom-dialog-header\">\n        <div class=\"product-info\">\n          <span>产品名称：{{ form.productName }}</span>\n          <span>产品编号：{{ form.productCode }}</span>\n          <span>规格型号：{{ form.specification }}</span>\n          <span>单位：{{ form.unit }}</span>\n        </div>\n      </div>\n      \n      <el-table\n        v-loading=\"bomLoading\"\n        :data=\"bomList\"\n        border\n        style=\"width: 100%\"\n        @row-click=\"handleBomSelect\"\n        highlight-current-row\n        size=\"small\"\n        height=\"300\"\n      >\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <el-radio v-model=\"selectedBomId\" :label=\"scope.row.bom_id\" @change=\"handleBomSelect(scope.row)\">&nbsp;</el-radio>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"序号\" type=\"index\" width=\"55\" align=\"center\"></el-table-column>\n        <el-table-column prop=\"bom_code\" label=\"BOM编号\" min-width=\"150\" align=\"center\"></el-table-column>\n        <el-table-column prop=\"bom_version\" label=\"版本号\" width=\"100\" align=\"center\"></el-table-column>\n        <el-table-column prop=\"bom_status\" label=\"默认BOM\" width=\"100\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <span>{{ scope.row.bom_status === '1' ? '是' : '否' }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"bom_output\" label=\"日产量\" width=\"100\" align=\"center\"></el-table-column>\n      </el-table>\n      \n      <pagination\n        v-show=\"bomTotal > 0\"\n        :total=\"bomTotal\"\n        :page.sync=\"bomQuery.pageNum\"\n        :limit.sync=\"bomQuery.pageSize\"\n        @pagination=\"getBomList\"\n      />\n      \n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"bomDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"confirmBomSelect\">确定</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 生产订单选择对话框 -->\n    <el-dialog title=\"选择生产订单\" :visible.sync=\"productionOrderDialogVisible\" width=\"800px\" append-to-body>\n      <el-table\n        :data=\"productionOrderList\"\n        @selection-change=\"handleProductionOrderSelectionChange\"\n        style=\"width: 100%\">\n        <el-table-column type=\"selection\" width=\"55\"></el-table-column>\n        <el-table-column prop=\"prodOrderCode\" label=\"订单编号\" width=\"150\"></el-table-column>\n        <el-table-column prop=\"customerName\" label=\"客户名称\" width=\"150\"></el-table-column>\n        <el-table-column prop=\"deliveryDate\" label=\"交付日期\" width=\"120\">\n          <template slot-scope=\"scope\">\n            {{ scope.row.deliveryDate ? scope.row.deliveryDate.substring(0, 10) : '' }}\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"status\" label=\"状态\" width=\"80\">\n          <template slot-scope=\"scope\">\n            <el-tag v-if=\"scope.row.status === '1'\" type=\"warning\">待计划</el-tag>\n            <el-tag v-else-if=\"scope.row.status === '2'\" type=\"primary\">已计划</el-tag>\n            <el-tag v-else-if=\"scope.row.status === '3'\" type=\"success\">生产中</el-tag>\n            <el-tag v-else-if=\"scope.row.status === '4'\" type=\"info\">已完成</el-tag>\n            <el-tag v-else type=\"info\">{{ scope.row.status }}</el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"remark\" label=\"备注\" show-overflow-tooltip></el-table-column>\n      </el-table>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"productionOrderDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"confirmProductionOrderSelect\">确定</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { addProductionPlan } from \"@/api/sc/productionPlan\";\nimport { listProducts, listBomsByProductId, findBomDetails } from \"@/api/basic/product\";\nimport { getAutoNumbers } from \"@/api/basic/numbers\";\nimport Pagination from \"@/components/Pagination\";\n\nexport default {\n  name: \"AddPlan\",\n  components: {\n    Pagination\n  },\n  data() {\n    return {\n      // 表单数据\n      form: {\n        planCode: \"\",\n        planName: \"\",\n        sourceType: \"PRODUCTION_ORDER\",\n        orderCode: \"\",\n        productId: undefined,\n        productName: \"\",\n        productCode: \"\",\n        specification: \"\",\n        unit: \"\",\n        plannedQty: 1,\n        planStartTime: \"\",\n        planEndTime: \"\",\n        requiredDate: \"\",\n        remark: \"\",\n        orderQty: 0  // 添加订单数量字段\n      },\n      // 是否使用系统编号\n      isSystemCode: true,\n      // 表单验证规则\n      rules: {\n        planName: [\n          { required: true, message: \"计划名称不能为空\", trigger: \"blur\" },\n          { max: 50, message: \"长度不能超过50个字符\", trigger: \"blur\" }\n        ],\n        sourceType: [\n          { required: true, message: \"来源类型不能为空\", trigger: \"change\" }\n        ],\n        productName: [\n          { required: true, message: \"请选择产品\", trigger: \"change\" }\n        ],\n        plannedQty: [\n          { required: true, message: \"计划数量不能为空\", trigger: \"blur\" },\n          { validator: this.validatePlannedQty, trigger: \"blur\" }\n        ],\n        planStartTime: [\n          { validator: this.validateStartTime, trigger: \"change\" }\n        ],\n        planEndTime: [\n          { validator: this.validateEndTime, trigger: \"change\" }\n        ],\n        requiredDate: [\n          { validator: this.validateRequiredDate, trigger: \"change\" }\n        ]\n      },\n      // 产品下拉选项\n      productOptions: [],\n      // 产品加载状态\n      productLoading: false,\n      // 来源类型选项\n      sourceTypeOptions: [\n        { dictLabel: \"生产订单\", dictValue: \"PRODUCTION_ORDER\" }\n      ],\n      // 上传文件列表\n      fileList: [],\n      \n      // 产品选择对话框\n      productDialogVisible: false,\n      productQuery: {\n        pageNum: 1,\n        pageSize: 10,\n        keyword: \"\",\n        unit: \"\",\n        type: \"\",\n        property: \"\"\n      },\n      productList: [],\n      productTotal: 0,\n\n      // 生产订单选择对话框\n      productionOrderDialogVisible: false,\n      productionOrderList: [],\n      selectedProductionOrder: null,\n      selectedProduct: null,\n      \n      // BOM选择对话框\n      bomDialogVisible: false,\n      bomQuery: {\n        pageNum: 1,\n        pageSize: 10,\n        productId: undefined\n      },\n      bomList: [],\n      bomTotal: 0,\n      bomLoading: false,\n      selectedBom: null,\n      selectedBomId: null,\n      bomDetailList: [],\n    };\n  },\n  created() {\n    // 初始化时如果是系统编号，则生成计划编号\n    if (this.isSystemCode) {\n      this.generatePlanCode();\n    }\n  },\n  methods: {\n    // 生成计划编号\n    generatePlanCode() {\n      getAutoNumbers(6).then(response => {\n        if (response.code === 200) {\n          this.form.planCode = response.msg;\n        } else {\n          this.$message.error('获取计划编号失败');\n        }\n      }).catch(() => {\n        this.$message.error('获取计划编号失败');\n      });\n    },\n    \n    // 处理系统编号开关变化\n    handleSystemCodeChange(val) {\n      if (val) {\n        // 如果开启系统编号，则生成编号\n        this.generatePlanCode();\n      } else {\n        // 如果关闭系统编号，则清空编号\n        this.form.planCode = '';\n      }\n    },\n\n    // 处理来源类型变化\n    handleSourceTypeChange(val) {\n      // 清空订单编号\n      this.form.orderCode = \"\";\n      // 如果选择生产订单，清空产品相关信息\n      if (val === 'PRODUCTION_ORDER') {\n        this.form.productId = undefined;\n        this.form.productName = \"\";\n        this.form.productCode = \"\";\n        this.form.specification = \"\";\n        this.form.unit = \"\";\n      }\n    },\n\n    // 打开生产订单选择对话框\n    openProductionOrderDialog() {\n      this.productionOrderDialogVisible = true;\n      this.getProductionOrderList();\n    },\n\n    // 获取生产订单列表\n    getProductionOrderList() {\n      // 这里调用生产订单列表API\n      import(\"@/api/sc/productionOrder\").then(api => {\n        api.listProductionOrder({}).then(response => {\n          this.productionOrderList = response.rows || [];\n        }).catch(() => {\n          this.$message.error('获取生产订单列表失败');\n        });\n      });\n    },\n\n    // 处理生产订单选择变化\n    handleProductionOrderSelectionChange(selection) {\n      this.selectedProductionOrder = selection.length > 0 ? selection[0] : null;\n    },\n\n    // 确认选择生产订单\n    confirmProductionOrderSelect() {\n      if (!this.selectedProductionOrder) {\n        this.$message.warning('请选择一个生产订单');\n        return;\n      }\n\n      // 设置订单编号\n      this.form.orderCode = this.selectedProductionOrder.prodOrderCode;\n\n      // 调用API根据生产订单创建计划模板\n      import(\"@/api/sc/productionPlan\").then(api => {\n        api.createPlanFromOrder(this.selectedProductionOrder.productionOrderId).then(response => {\n          if (response.code === 200) {\n            const planData = response.data;\n            // 填充表单数据\n            this.form.planName = planData.planName;\n            this.form.productId = planData.productId;\n            this.form.plannedQty = planData.plannedQty;\n            this.form.orderQty = planData.plannedQty; // 保存订单数量用于验证\n            this.form.planStartTime = planData.planStartTime;\n            this.form.planEndTime = planData.planEndTime;\n            this.form.requiredDate = planData.requiredDate;\n            this.form.remark = planData.remark;\n\n            // 如果有产品信息，需要获取产品详情\n            if (planData.productId) {\n              this.getProductDetails(planData.productId);\n            }\n\n            this.$message.success('已关联生产订单，请完善其他信息');\n          }\n        }).catch(() => {\n          this.$message.error('关联生产订单失败');\n        });\n      });\n\n      this.productionOrderDialogVisible = false;\n    },\n\n    // 获取产品详情\n    getProductDetails(productId) {\n      listProducts({ productId: productId }).then(response => {\n        if (response.rows && response.rows.length > 0) {\n          const product = response.rows[0];\n          this.form.productName = product.product_name;\n          this.form.productCode = product.product_code;\n          this.form.specification = product.product_sfn;\n          this.form.unit = product.product_unit;\n        }\n      }).catch(() => {\n        console.error('获取产品详情失败');\n      });\n    },\n    \n    // 触发上传\n    triggerUpload() {\n      this.$refs.upload.$el.click();\n    },\n    \n    // 打开产品选择弹窗\n    openProductSelection() {\n      if (this.form.sourceType === 'PRODUCTION_ORDER') {\n        // 如果选择了生产订单，但没有选择具体订单\n        if (!this.form.orderCode) {\n          this.$message.warning('请先选择生产订单');\n          return;\n        }\n        // 显示该订单的产品列表\n        this.getProductsByOrder();\n      } else {\n        // 其他情况显示全部产品\n        this.getProductList();\n      }\n      this.productDialogVisible = true;\n    },\n    \n    // 获取产品列表\n    getProductList() {\n      this.productLoading = true;\n      listProducts({\n        pageNum: this.productQuery.pageNum,\n        pageSize: this.productQuery.pageSize,\n        keyword: this.productQuery.keyword,\n        productUnit: this.productQuery.unit,\n        productType: this.productQuery.type,\n        productProperty: this.productQuery.property\n      }).then(response => {\n        this.productLoading = false;\n        if (response.code === 200) {\n          this.productList = response.rows;\n          this.productTotal = response.total;\n        }\n      }).catch(() => {\n        this.productLoading = false;\n        // 模拟数据\n        this.productList = [\n          { product_id: 1, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' },\n          { product_id: 2, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' },\n          { product_id: 3, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' },\n          { product_id: 4, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' },\n          { product_id: 5, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' }\n        ];\n        this.productTotal = 50;\n      });\n    },\n\n    // 根据生产订单获取产品列表\n    getProductsByOrder() {\n      if (!this.selectedProductionOrder) {\n        this.$message.error('未找到选中的生产订单信息');\n        return;\n      }\n\n      this.productLoading = true;\n      // 调用API获取订单产品明细\n      import(\"@/api/sc/productionOrder\").then(api => {\n        api.getProductionOrderDetails(this.selectedProductionOrder.productionOrderId).then(response => {\n          if (response.code === 200 && response.data && response.data.products) {\n            // 将订单明细转换为产品列表格式\n            this.productList = response.data.products.map(detail => ({\n              product_id: detail.productId,\n              product_name: detail.productName,\n              product_code: detail.productCode,\n              product_sfn: detail.productSfn,\n              product_unit: detail.productUnit,\n              // 添加订单相关信息\n              order_qty: detail.qtyNum,\n              delivery_date: detail.deliveryDate\n            }));\n            this.productTotal = this.productList.length;\n          } else {\n            this.$message.warning('该订单暂无产品明细');\n            this.productList = [];\n            this.productTotal = 0;\n          }\n          this.productLoading = false;\n        }).catch(() => {\n          this.$message.error('获取订单产品列表失败');\n          this.productLoading = false;\n        });\n      });\n    },\n\n    // 搜索产品\n    searchProducts() {\n      this.productQuery.pageNum = 1;\n      this.getProductList();\n    },\n    \n    // 重置产品查询条件\n    resetProductQuery() {\n      this.productQuery = {\n        pageNum: 1,\n        pageSize: 10,\n        keyword: \"\",\n        unit: \"\",\n        type: \"\",\n        property: \"\"\n      };\n      this.getProductList();\n    },\n    \n    // 处理产品表格选择变化\n    handleProductSelectionChange(selection) {\n      if (selection.length > 0) {\n        this.selectedProduct = selection[0];\n      } else {\n        this.selectedProduct = null;\n      }\n    },\n    \n    // 处理产品页码变化\n    handleProductCurrentChange(currentPage) {\n      this.productQuery.pageNum = currentPage;\n      this.getProductList();\n    },\n    \n    // 处理产品每页条数变化\n    handleProductSizeChange(size) {\n      this.productQuery.pageSize = size;\n      this.productQuery.pageNum = 1;\n      this.getProductList();\n    },\n    \n    // 确认产品选择\n    confirmProductSelect() {\n      if (this.selectedProduct) {\n        this.form.productId = this.selectedProduct.product_id;\n        this.form.productName = this.selectedProduct.product_name;\n        this.form.productCode = this.selectedProduct.product_code;\n        this.form.specification = this.selectedProduct.product_sfn;\n        this.form.unit = this.selectedProduct.product_unit;\n\n        // 如果是从生产订单选择的产品，自动填充订单数量和交付日期\n        if (this.form.sourceType === 'PRODUCTION_ORDER' && this.selectedProduct.order_qty) {\n          this.form.plannedQty = this.selectedProduct.order_qty;\n          if (this.selectedProduct.delivery_date) {\n            this.form.requiredDate = this.selectedProduct.delivery_date;\n            this.form.planEndTime = this.selectedProduct.delivery_date;\n          }\n        }\n\n        this.productDialogVisible = false;\n\n        // 清空已选BOM\n        this.selectedBom = null;\n        this.selectedBomId = null;\n      } else {\n        this.$message.warning('请选择一个产品！');\n      }\n    },\n    \n    // 选择BOM\n    selectBom() {\n      if (!this.form.productId) {\n        this.$message.warning('请先选择成品！');\n        return;\n      }\n      this.bomDialogVisible = true;\n      this.getBomList();\n    },\n    \n    // 获取BOM列表\n    getBomList() {\n      this.bomLoading = true;\n      listBomsByProductId(this.form.productId).then(response => {\n        this.bomLoading = false;\n        if (response.code === 200) {\n          this.bomList = response.rows;\n          this.bomTotal = response.total;\n          if (!this.bomList || this.bomList.length === 0) {\n            this.$message.info(\"未找到该产品的BOM信息\");\n          } else {\n            // 如果有默认BOM，则自动选中\n            const defaultBom = this.bomList.find(b => b.bom_status === '1');\n            if (defaultBom) {\n              this.handleBomSelect(defaultBom);\n            }\n          }\n        } else {\n          this.bomList = [];\n          this.bomTotal = 0;\n        }\n      }).catch(() => {\n        this.bomLoading = false;\n        this.$message.error('获取BOM列表失败');\n      });\n    },\n    \n    // 处理BOM行选择\n    handleBomSelect(row) {\n      this.selectedBom = row;\n      this.selectedBomId = row.bom_id;\n    },\n    \n    // 确认BOM选择\n    confirmBomSelect() {\n      if (this.selectedBom) {\n        this.bomDialogVisible = false;\n        // 获取BOM详情\n        this.getBomDetail();\n      } else {\n        this.$message.warning('请选择一个BOM！');\n      }\n    },\n    \n    // 获取BOM详情\n    getBomDetail() {\n      findBomDetails(this.selectedBom.bom_id).then(response => {\n        console.log(\"成功获取BOM详情响应:\", response);\n        if (response && response.code === 200) {\n          this.bomDetailList = response.rows;\n        } else {\n          this.bomDetailList = [];\n          this.$message.error(\"获取BOM详情失败: \" + (response ? response.msg : '无响应'));\n        }\n      }).catch(error => {\n        console.error(\"获取BOM详情接口调用失败:\", error);\n        this.$message.error(\"获取BOM详情接口调用失败\");\n        this.bomDetailList = [];\n      });\n    },\n    \n    // 清除已选BOM\n    clearSelectedBom() {\n      this.selectedBom = null;\n      this.selectedBomId = null;\n      this.bomDetailList = [];\n    },\n    \n    // 上传前检查文件类型和大小\n    beforeUpload(file) {\n      const isValidType = /\\.(doc|docx|xls|xlsx|pdf|rar|zip|png|jpg|jpeg)$/i.test(file.name);\n      const isLt10M = file.size / 1024 / 1024 < 10;\n\n      if (!isValidType) {\n        this.$message.error('上传文件格式不支持!');\n        return false;\n      }\n      if (!isLt10M) {\n        this.$message.error('上传文件大小不能超过 10MB!');\n        return false;\n      }\n      return true;\n    },\n    \n    // 上传文件处理\n    uploadFile(options) {\n      // 这里应该调用实际的文件上传API\n      console.log('文件上传:', options.file);\n      // 假设上传成功\n      this.fileList.push({\n        name: options.file.name,\n        url: URL.createObjectURL(options.file)\n      });\n      options.onSuccess();\n    },\n    \n    // 移除文件\n    handleRemove(file) {\n      const index = this.fileList.indexOf(file);\n      if (index !== -1) {\n        this.fileList.splice(index, 1);\n      }\n    },\n    \n    // 表单提交\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          // 额外的日期逻辑验证\n          if (!this.validateDateLogic()) {\n            return;\n          }\n\n          // 表单验证通过，调用API提交数据\n          const data = {\n            planCode: this.form.planCode,\n            planName: this.form.planName,\n            sourceType: this.form.sourceType,\n            orderCode: this.form.orderCode,\n            planStartTime: this.form.planStartTime,\n            planEndTime: this.form.planEndTime,\n            requiredDate: this.form.requiredDate,\n            remark: this.form.remark,\n            productId: this.form.productId,\n            plannedQty: this.form.plannedQty\n          };\n          \n          addProductionPlan(data).then(response => {\n            if (response.code === 200) {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.cancel();\n            } else {\n              this.$modal.msgError(response.msg || \"新增失败\");\n            }\n          }).catch(() => {\n            // 模拟成功响应\n            this.$modal.msgSuccess(\"新增成功\");\n            this.cancel();\n          });\n        }\n      });\n    },\n    \n    // 取消按钮\n    cancel() {\n      this.$router.push({ path: \"/sc/plan\" });\n    },\n\n    // 验证计划数量\n    validatePlannedQty(rule, value, callback) {\n      if (!value) {\n        callback();\n        return;\n      }\n\n      const plannedQty = Number(value);\n      const orderQty = Number(this.form.orderQty);\n\n      if (plannedQty <= 0) {\n        callback(new Error('计划数量必须大于0'));\n        return;\n      }\n\n      if (orderQty > 0 && plannedQty > orderQty) {\n        callback(new Error(`计划数量不能大于订单数量(${orderQty})`));\n        return;\n      }\n\n      callback();\n    },\n\n    // 验证开工时间\n    validateStartTime(rule, value, callback) {\n      if (!value) {\n        callback();\n        return;\n      }\n\n      const today = new Date();\n      today.setHours(0, 0, 0, 0);\n      const startDate = new Date(value);\n\n      // 开工日期不能早于当前日期\n      if (startDate < today) {\n        callback(new Error('开工日期不能早于当前日期'));\n        return;\n      }\n\n      // 如果完工时间已选择，开工时间不能晚于完工时间\n      if (this.form.planEndTime) {\n        const endDate = new Date(this.form.planEndTime);\n        if (startDate > endDate) {\n          callback(new Error('开工日期不能晚于完工日期'));\n          return;\n        }\n      }\n\n      callback();\n    },\n\n    // 验证完工时间\n    validateEndTime(rule, value, callback) {\n      if (!value) {\n        callback();\n        return;\n      }\n\n      const endDate = new Date(value);\n\n      // 如果开工时间已选择，完工时间不能早于开工时间\n      if (this.form.planStartTime) {\n        const startDate = new Date(this.form.planStartTime);\n        if (endDate < startDate) {\n          callback(new Error('完工日期不能早于开工日期'));\n          return;\n        }\n      }\n\n      callback();\n    },\n\n    // 验证需求日期\n    validateRequiredDate(rule, value, callback) {\n      if (!value) {\n        callback();\n        return;\n      }\n\n      const requiredDate = new Date(value);\n\n      // 需求日期不能早于当前日期\n      const today = new Date();\n      today.setHours(0, 0, 0, 0);\n      if (requiredDate < today) {\n        callback(new Error('需求日期不能早于当前日期'));\n        return;\n      }\n\n      // 如果完工时间已选择，需求日期不能早于完工时间\n      if (this.form.planEndTime) {\n        const endDate = new Date(this.form.planEndTime);\n        if (requiredDate < endDate) {\n          callback(new Error('需求日期不能早于完工日期'));\n          return;\n        }\n      }\n\n      callback();\n    },\n\n    // 综合日期逻辑验证\n    validateDateLogic() {\n      const today = new Date();\n      today.setHours(0, 0, 0, 0);\n\n      // 检查开工日期\n      if (this.form.planStartTime) {\n        const startDate = new Date(this.form.planStartTime);\n        if (startDate < today) {\n          this.$modal.msgError(\"开工日期不能早于当前日期\");\n          return false;\n        }\n      }\n\n      // 检查开工日期和完工日期的关系\n      if (this.form.planStartTime && this.form.planEndTime) {\n        const startDate = new Date(this.form.planStartTime);\n        const endDate = new Date(this.form.planEndTime);\n        if (startDate > endDate) {\n          this.$modal.msgError(\"开工日期不能晚于完工日期\");\n          return false;\n        }\n      }\n\n      // 检查需求日期\n      if (this.form.requiredDate) {\n        const requiredDate = new Date(this.form.requiredDate);\n        if (requiredDate < today) {\n          this.$modal.msgError(\"需求日期不能早于当前日期\");\n          return false;\n        }\n\n        // 需求日期不能早于完工日期\n        if (this.form.planEndTime) {\n          const endDate = new Date(this.form.planEndTime);\n          if (requiredDate < endDate) {\n            this.$modal.msgError(\"需求日期不能早于完工日期\");\n            return false;\n          }\n        }\n      }\n\n      return true;\n    }\n  }\n};\n</script>\n\n<style scoped>\n.form-container {\n  background-color: #fff;\n  padding: 10px;\n}\n\n.el-tabs--border-card {\n  box-shadow: none;\n}\n\n.upload-container {\n  width: 100%;\n  border: 1px dashed #d9d9d9;\n  border-radius: 6px;\n  text-align: center;\n  padding: 20px 0;\n  cursor: pointer;\n}\n\n.upload-container:hover {\n  border-color: #409EFF;\n}\n\n.upload-area {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\n.upload-area .el-icon-upload {\n  font-size: 48px;\n  color: #c0c4cc;\n  margin-bottom: 10px;\n}\n\n.upload-text {\n  font-size: 14px;\n  color: #606266;\n  margin-bottom: 10px;\n}\n\n.upload-hint {\n  font-size: 12px;\n  color: #909399;\n}\n\n.input-with-select .el-input-group__append {\n  background-color: #fff;\n}\n\n.bom-title {\n  font-size: 16px;\n  font-weight: bold;\n  color: #303133;\n}\n\n.bom-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  margin: 20px 0;\n  padding: 30px 0;\n}\n\n.folder-icon {\n  margin-bottom: 20px;\n}\n\n.bom-text {\n  font-size: 14px;\n  color: #909399;\n  margin-bottom: 20px;\n}\n\n.warning-text {\n  color: #E6A23C;\n  font-size: 12px;\n  margin-left: 10px;\n}\n\n.warning-text i {\n  margin-right: 5px;\n}\n\n.upload-hidden {\n  width: 100%;\n  height: 100%;\n}\n\n.upload-hidden >>> .el-upload {\n  width: 100%;\n}\n\n.upload-hidden >>> .el-upload-dragger {\n  width: 100%;\n  height: 100%;\n  border: none;\n  padding: 0;\n  margin: 0;\n}\n\n.bom-dialog-header {\n  margin-bottom: 15px;\n  padding: 10px;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n}\n\n.product-info {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.product-info span {\n  margin-right: 20px;\n  line-height: 30px;\n}\n\n.el-radio {\n  margin-right: 0;\n}\n\n.pagination-container {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 0;\n  font-size: 12px;\n}\n\n.pagination-wrapper {\n  display: flex;\n  align-items: center;\n}\n\n.page-size {\n  margin-right: 10px;\n}\n\n.total-text {\n  color: #606266;\n  font-size: 12px;\n}\n\n.bom-info {\n  width: 100%;\n  margin-bottom: 20px;\n}\n\n.bom-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n  padding: 8px;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n}\n\n.bom-title-info {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.bom-title-info span {\n  margin-right: 20px;\n  font-weight: bold;\n}\n\n.bom-detail-table {\n  margin-bottom: 15px;\n}\n\n.bom-action {\n  display: flex;\n  align-items: center;\n}\n\n.select-bom-button {\n  margin-right: 10px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0ZA,IAAAA,eAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AACA,IAAAG,WAAA,GAAAC,sBAAA,CAAAJ,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAK,IAAA;EACAC,UAAA;IACAC,UAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,IAAA;QACAC,QAAA;QACAC,QAAA;QACAC,UAAA;QACAC,SAAA;QACAC,SAAA,EAAAC,SAAA;QACAC,WAAA;QACAC,WAAA;QACAC,aAAA;QACAC,IAAA;QACAC,UAAA;QACAC,aAAA;QACAC,WAAA;QACAC,YAAA;QACAC,MAAA;QACAC,QAAA;MACA;MACA;MACAC,YAAA;MACA;MACAC,KAAA;QACAhB,QAAA,GACA;UAAAiB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA,EACA;QACAlB,UAAA,GACA;UAAAgB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAd,WAAA,GACA;UAAAY,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAV,UAAA,GACA;UAAAQ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAE,SAAA,OAAAC,kBAAA;UAAAH,OAAA;QAAA,EACA;QACAT,aAAA,GACA;UAAAW,SAAA,OAAAE,iBAAA;UAAAJ,OAAA;QAAA,EACA;QACAR,WAAA,GACA;UAAAU,SAAA,OAAAG,eAAA;UAAAL,OAAA;QAAA,EACA;QACAP,YAAA,GACA;UAAAS,SAAA,OAAAI,oBAAA;UAAAN,OAAA;QAAA;MAEA;MACA;MACAO,cAAA;MACA;MACAC,cAAA;MACA;MACAC,iBAAA,GACA;QAAAC,SAAA;QAAAC,SAAA;MAAA,EACA;MACA;MACAC,QAAA;MAEA;MACAC,oBAAA;MACAC,YAAA;QACAC,OAAA;QACAC,QAAA;QACAC,OAAA;QACA5B,IAAA;QACA6B,IAAA;QACAC,QAAA;MACA;MACAC,WAAA;MACAC,YAAA;MAEA;MACAC,4BAAA;MACAC,mBAAA;MACAC,uBAAA;MACAC,eAAA;MAEA;MACAC,gBAAA;MACAC,QAAA;QACAZ,OAAA;QACAC,QAAA;QACAhC,SAAA,EAAAC;MACA;MACA2C,OAAA;MACAC,QAAA;MACAC,UAAA;MACAC,WAAA;MACAC,aAAA;MACAC,aAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACA,SAAAtC,YAAA;MACA,KAAAuC,gBAAA;IACA;EACA;EACAC,OAAA;IACA;IACAD,gBAAA,WAAAA,iBAAA;MAAA,IAAAE,KAAA;MACA,IAAAC,uBAAA,KAAAC,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAC,IAAA;UACAJ,KAAA,CAAA1D,IAAA,CAAAC,QAAA,GAAA4D,QAAA,CAAAE,GAAA;QACA;UACAL,KAAA,CAAAM,QAAA,CAAAC,KAAA;QACA;MACA,GAAAC,KAAA;QACAR,KAAA,CAAAM,QAAA,CAAAC,KAAA;MACA;IACA;IAEA;IACAE,sBAAA,WAAAA,uBAAAC,GAAA;MACA,IAAAA,GAAA;QACA;QACA,KAAAZ,gBAAA;MACA;QACA;QACA,KAAAxD,IAAA,CAAAC,QAAA;MACA;IACA;IAEA;IACAoE,sBAAA,WAAAA,uBAAAD,GAAA;MACA;MACA,KAAApE,IAAA,CAAAI,SAAA;MACA;MACA,IAAAgE,GAAA;QACA,KAAApE,IAAA,CAAAK,SAAA,GAAAC,SAAA;QACA,KAAAN,IAAA,CAAAO,WAAA;QACA,KAAAP,IAAA,CAAAQ,WAAA;QACA,KAAAR,IAAA,CAAAS,aAAA;QACA,KAAAT,IAAA,CAAAU,IAAA;MACA;IACA;IAEA;IACA4D,yBAAA,WAAAA,0BAAA;MACA,KAAA3B,4BAAA;MACA,KAAA4B,sBAAA;IACA;IAEA;IACAA,sBAAA,WAAAA,uBAAA;MAAA,IAAAC,MAAA;MACA;MACAC,OAAA,CAAAC,OAAA,GAAAd,IAAA;QAAA,WAAAe,wBAAA,CAAAC,OAAA,EAAArF,OAAA;MAAA,GAAAqE,IAAA,WAAAiB,GAAA;QACAA,GAAA,CAAAC,mBAAA,KAAAlB,IAAA,WAAAC,QAAA;UACAW,MAAA,CAAA5B,mBAAA,GAAAiB,QAAA,CAAAkB,IAAA;QACA,GAAAb,KAAA;UACAM,MAAA,CAAAR,QAAA,CAAAC,KAAA;QACA;MACA;IACA;IAEA;IACAe,oCAAA,WAAAA,qCAAAC,SAAA;MACA,KAAApC,uBAAA,GAAAoC,SAAA,CAAAC,MAAA,OAAAD,SAAA;IACA;IAEA;IACAE,4BAAA,WAAAA,6BAAA;MAAA,IAAAC,MAAA;MACA,UAAAvC,uBAAA;QACA,KAAAmB,QAAA,CAAAqB,OAAA;QACA;MACA;;MAEA;MACA,KAAArF,IAAA,CAAAI,SAAA,QAAAyC,uBAAA,CAAAyC,aAAA;;MAEA;MACAb,OAAA,CAAAC,OAAA,GAAAd,IAAA;QAAA,WAAAe,wBAAA,CAAAC,OAAA,EAAArF,OAAA;MAAA,GAAAqE,IAAA,WAAAiB,GAAA;QACAA,GAAA,CAAAU,mBAAA,CAAAH,MAAA,CAAAvC,uBAAA,CAAA2C,iBAAA,EAAA5B,IAAA,WAAAC,QAAA;UACA,IAAAA,QAAA,CAAAC,IAAA;YACA,IAAA2B,QAAA,GAAA5B,QAAA,CAAA9D,IAAA;YACA;YACAqF,MAAA,CAAApF,IAAA,CAAAE,QAAA,GAAAuF,QAAA,CAAAvF,QAAA;YACAkF,MAAA,CAAApF,IAAA,CAAAK,SAAA,GAAAoF,QAAA,CAAApF,SAAA;YACA+E,MAAA,CAAApF,IAAA,CAAAW,UAAA,GAAA8E,QAAA,CAAA9E,UAAA;YACAyE,MAAA,CAAApF,IAAA,CAAAgB,QAAA,GAAAyE,QAAA,CAAA9E,UAAA;YACAyE,MAAA,CAAApF,IAAA,CAAAY,aAAA,GAAA6E,QAAA,CAAA7E,aAAA;YACAwE,MAAA,CAAApF,IAAA,CAAAa,WAAA,GAAA4E,QAAA,CAAA5E,WAAA;YACAuE,MAAA,CAAApF,IAAA,CAAAc,YAAA,GAAA2E,QAAA,CAAA3E,YAAA;YACAsE,MAAA,CAAApF,IAAA,CAAAe,MAAA,GAAA0E,QAAA,CAAA1E,MAAA;;YAEA;YACA,IAAA0E,QAAA,CAAApF,SAAA;cACA+E,MAAA,CAAAM,iBAAA,CAAAD,QAAA,CAAApF,SAAA;YACA;YAEA+E,MAAA,CAAApB,QAAA,CAAA2B,OAAA;UACA;QACA,GAAAzB,KAAA;UACAkB,MAAA,CAAApB,QAAA,CAAAC,KAAA;QACA;MACA;MAEA,KAAAtB,4BAAA;IACA;IAEA;IACA+C,iBAAA,WAAAA,kBAAArF,SAAA;MAAA,IAAAuF,MAAA;MACA,IAAAC,qBAAA;QAAAxF,SAAA,EAAAA;MAAA,GAAAuD,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAkB,IAAA,IAAAlB,QAAA,CAAAkB,IAAA,CAAAG,MAAA;UACA,IAAAY,OAAA,GAAAjC,QAAA,CAAAkB,IAAA;UACAa,MAAA,CAAA5F,IAAA,CAAAO,WAAA,GAAAuF,OAAA,CAAAC,YAAA;UACAH,MAAA,CAAA5F,IAAA,CAAAQ,WAAA,GAAAsF,OAAA,CAAAE,YAAA;UACAJ,MAAA,CAAA5F,IAAA,CAAAS,aAAA,GAAAqF,OAAA,CAAAG,WAAA;UACAL,MAAA,CAAA5F,IAAA,CAAAU,IAAA,GAAAoF,OAAA,CAAAI,YAAA;QACA;MACA,GAAAhC,KAAA;QACAiC,OAAA,CAAAlC,KAAA;MACA;IACA;IAEA;IACAmC,aAAA,WAAAA,cAAA;MACA,KAAAC,KAAA,CAAAC,MAAA,CAAAC,GAAA,CAAAC,KAAA;IACA;IAEA;IACAC,oBAAA,WAAAA,qBAAA;MACA,SAAAzG,IAAA,CAAAG,UAAA;QACA;QACA,UAAAH,IAAA,CAAAI,SAAA;UACA,KAAA4D,QAAA,CAAAqB,OAAA;UACA;QACA;QACA;QACA,KAAAqB,kBAAA;MACA;QACA;QACA,KAAAC,cAAA;MACA;MACA,KAAAzE,oBAAA;IACA;IAEA;IACAyE,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,KAAA/E,cAAA;MACA,IAAAgE,qBAAA;QACAzD,OAAA,OAAAD,YAAA,CAAAC,OAAA;QACAC,QAAA,OAAAF,YAAA,CAAAE,QAAA;QACAC,OAAA,OAAAH,YAAA,CAAAG,OAAA;QACAuE,WAAA,OAAA1E,YAAA,CAAAzB,IAAA;QACAoG,WAAA,OAAA3E,YAAA,CAAAI,IAAA;QACAwE,eAAA,OAAA5E,YAAA,CAAAK;MACA,GAAAoB,IAAA,WAAAC,QAAA;QACA+C,MAAA,CAAA/E,cAAA;QACA,IAAAgC,QAAA,CAAAC,IAAA;UACA8C,MAAA,CAAAnE,WAAA,GAAAoB,QAAA,CAAAkB,IAAA;UACA6B,MAAA,CAAAlE,YAAA,GAAAmB,QAAA,CAAAmD,KAAA;QACA;MACA,GAAA9C,KAAA;QACA0C,MAAA,CAAA/E,cAAA;QACA;QACA+E,MAAA,CAAAnE,WAAA,IACA;UAAAwE,UAAA;UAAAjB,YAAA;UAAAD,YAAA;UAAAE,WAAA;UAAAiB,YAAA;UAAAhB,YAAA;UAAAiB,gBAAA;QAAA,GACA;UAAAF,UAAA;UAAAjB,YAAA;UAAAD,YAAA;UAAAE,WAAA;UAAAiB,YAAA;UAAAhB,YAAA;UAAAiB,gBAAA;QAAA,GACA;UAAAF,UAAA;UAAAjB,YAAA;UAAAD,YAAA;UAAAE,WAAA;UAAAiB,YAAA;UAAAhB,YAAA;UAAAiB,gBAAA;QAAA,GACA;UAAAF,UAAA;UAAAjB,YAAA;UAAAD,YAAA;UAAAE,WAAA;UAAAiB,YAAA;UAAAhB,YAAA;UAAAiB,gBAAA;QAAA,GACA;UAAAF,UAAA;UAAAjB,YAAA;UAAAD,YAAA;UAAAE,WAAA;UAAAiB,YAAA;UAAAhB,YAAA;UAAAiB,gBAAA;QAAA,EACA;QACAP,MAAA,CAAAlE,YAAA;MACA;IACA;IAEA;IACAgE,kBAAA,WAAAA,mBAAA;MAAA,IAAAU,MAAA;MACA,UAAAvE,uBAAA;QACA,KAAAmB,QAAA,CAAAC,KAAA;QACA;MACA;MAEA,KAAApC,cAAA;MACA;MACA4C,OAAA,CAAAC,OAAA,GAAAd,IAAA;QAAA,WAAAe,wBAAA,CAAAC,OAAA,EAAArF,OAAA;MAAA,GAAAqE,IAAA,WAAAiB,GAAA;QACAA,GAAA,CAAAwC,yBAAA,CAAAD,MAAA,CAAAvE,uBAAA,CAAA2C,iBAAA,EAAA5B,IAAA,WAAAC,QAAA;UACA,IAAAA,QAAA,CAAAC,IAAA,YAAAD,QAAA,CAAA9D,IAAA,IAAA8D,QAAA,CAAA9D,IAAA,CAAAuH,QAAA;YACA;YACAF,MAAA,CAAA3E,WAAA,GAAAoB,QAAA,CAAA9D,IAAA,CAAAuH,QAAA,CAAAC,GAAA,WAAAC,MAAA;cAAA;gBACAP,UAAA,EAAAO,MAAA,CAAAnH,SAAA;gBACA0F,YAAA,EAAAyB,MAAA,CAAAjH,WAAA;gBACAyF,YAAA,EAAAwB,MAAA,CAAAhH,WAAA;gBACAyF,WAAA,EAAAuB,MAAA,CAAAC,UAAA;gBACAvB,YAAA,EAAAsB,MAAA,CAAAX,WAAA;gBACA;gBACAa,SAAA,EAAAF,MAAA,CAAAG,MAAA;gBACAC,aAAA,EAAAJ,MAAA,CAAAK;cACA;YAAA;YACAT,MAAA,CAAA1E,YAAA,GAAA0E,MAAA,CAAA3E,WAAA,CAAAyC,MAAA;UACA;YACAkC,MAAA,CAAApD,QAAA,CAAAqB,OAAA;YACA+B,MAAA,CAAA3E,WAAA;YACA2E,MAAA,CAAA1E,YAAA;UACA;UACA0E,MAAA,CAAAvF,cAAA;QACA,GAAAqC,KAAA;UACAkD,MAAA,CAAApD,QAAA,CAAAC,KAAA;UACAmD,MAAA,CAAAvF,cAAA;QACA;MACA;IACA;IAEA;IACAiG,cAAA,WAAAA,eAAA;MACA,KAAA3F,YAAA,CAAAC,OAAA;MACA,KAAAuE,cAAA;IACA;IAEA;IACAoB,iBAAA,WAAAA,kBAAA;MACA,KAAA5F,YAAA;QACAC,OAAA;QACAC,QAAA;QACAC,OAAA;QACA5B,IAAA;QACA6B,IAAA;QACAC,QAAA;MACA;MACA,KAAAmE,cAAA;IACA;IAEA;IACAqB,4BAAA,WAAAA,6BAAA/C,SAAA;MACA,IAAAA,SAAA,CAAAC,MAAA;QACA,KAAApC,eAAA,GAAAmC,SAAA;MACA;QACA,KAAAnC,eAAA;MACA;IACA;IAEA;IACAmF,0BAAA,WAAAA,2BAAAC,WAAA;MACA,KAAA/F,YAAA,CAAAC,OAAA,GAAA8F,WAAA;MACA,KAAAvB,cAAA;IACA;IAEA;IACAwB,uBAAA,WAAAA,wBAAAC,IAAA;MACA,KAAAjG,YAAA,CAAAE,QAAA,GAAA+F,IAAA;MACA,KAAAjG,YAAA,CAAAC,OAAA;MACA,KAAAuE,cAAA;IACA;IAEA;IACA0B,oBAAA,WAAAA,qBAAA;MACA,SAAAvF,eAAA;QACA,KAAA9C,IAAA,CAAAK,SAAA,QAAAyC,eAAA,CAAAmE,UAAA;QACA,KAAAjH,IAAA,CAAAO,WAAA,QAAAuC,eAAA,CAAAiD,YAAA;QACA,KAAA/F,IAAA,CAAAQ,WAAA,QAAAsC,eAAA,CAAAkD,YAAA;QACA,KAAAhG,IAAA,CAAAS,aAAA,QAAAqC,eAAA,CAAAmD,WAAA;QACA,KAAAjG,IAAA,CAAAU,IAAA,QAAAoC,eAAA,CAAAoD,YAAA;;QAEA;QACA,SAAAlG,IAAA,CAAAG,UAAA,gCAAA2C,eAAA,CAAA4E,SAAA;UACA,KAAA1H,IAAA,CAAAW,UAAA,QAAAmC,eAAA,CAAA4E,SAAA;UACA,SAAA5E,eAAA,CAAA8E,aAAA;YACA,KAAA5H,IAAA,CAAAc,YAAA,QAAAgC,eAAA,CAAA8E,aAAA;YACA,KAAA5H,IAAA,CAAAa,WAAA,QAAAiC,eAAA,CAAA8E,aAAA;UACA;QACA;QAEA,KAAA1F,oBAAA;;QAEA;QACA,KAAAkB,WAAA;QACA,KAAAC,aAAA;MACA;QACA,KAAAW,QAAA,CAAAqB,OAAA;MACA;IACA;IAEA;IACAiD,SAAA,WAAAA,UAAA;MACA,UAAAtI,IAAA,CAAAK,SAAA;QACA,KAAA2D,QAAA,CAAAqB,OAAA;QACA;MACA;MACA,KAAAtC,gBAAA;MACA,KAAAwF,UAAA;IACA;IAEA;IACAA,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAArF,UAAA;MACA,IAAAsF,4BAAA,OAAAzI,IAAA,CAAAK,SAAA,EAAAuD,IAAA,WAAAC,QAAA;QACA2E,MAAA,CAAArF,UAAA;QACA,IAAAU,QAAA,CAAAC,IAAA;UACA0E,MAAA,CAAAvF,OAAA,GAAAY,QAAA,CAAAkB,IAAA;UACAyD,MAAA,CAAAtF,QAAA,GAAAW,QAAA,CAAAmD,KAAA;UACA,KAAAwB,MAAA,CAAAvF,OAAA,IAAAuF,MAAA,CAAAvF,OAAA,CAAAiC,MAAA;YACAsD,MAAA,CAAAxE,QAAA,CAAA0E,IAAA;UACA;YACA;YACA,IAAAC,UAAA,GAAAH,MAAA,CAAAvF,OAAA,CAAA2F,IAAA,WAAAC,CAAA;cAAA,OAAAA,CAAA,CAAAC,UAAA;YAAA;YACA,IAAAH,UAAA;cACAH,MAAA,CAAAO,eAAA,CAAAJ,UAAA;YACA;UACA;QACA;UACAH,MAAA,CAAAvF,OAAA;UACAuF,MAAA,CAAAtF,QAAA;QACA;MACA,GAAAgB,KAAA;QACAsE,MAAA,CAAArF,UAAA;QACAqF,MAAA,CAAAxE,QAAA,CAAAC,KAAA;MACA;IACA;IAEA;IACA8E,eAAA,WAAAA,gBAAAC,GAAA;MACA,KAAA5F,WAAA,GAAA4F,GAAA;MACA,KAAA3F,aAAA,GAAA2F,GAAA,CAAAC,MAAA;IACA;IAEA;IACAC,gBAAA,WAAAA,iBAAA;MACA,SAAA9F,WAAA;QACA,KAAAL,gBAAA;QACA;QACA,KAAAoG,YAAA;MACA;QACA,KAAAnF,QAAA,CAAAqB,OAAA;MACA;IACA;IAEA;IACA8D,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,uBAAA,OAAAjG,WAAA,CAAA6F,MAAA,EAAArF,IAAA,WAAAC,QAAA;QACAsC,OAAA,CAAAmD,GAAA,iBAAAzF,QAAA;QACA,IAAAA,QAAA,IAAAA,QAAA,CAAAC,IAAA;UACAsF,MAAA,CAAA9F,aAAA,GAAAO,QAAA,CAAAkB,IAAA;QACA;UACAqE,MAAA,CAAA9F,aAAA;UACA8F,MAAA,CAAApF,QAAA,CAAAC,KAAA,kBAAAJ,QAAA,GAAAA,QAAA,CAAAE,GAAA;QACA;MACA,GAAAG,KAAA,WAAAD,KAAA;QACAkC,OAAA,CAAAlC,KAAA,mBAAAA,KAAA;QACAmF,MAAA,CAAApF,QAAA,CAAAC,KAAA;QACAmF,MAAA,CAAA9F,aAAA;MACA;IACA;IAEA;IACAiG,gBAAA,WAAAA,iBAAA;MACA,KAAAnG,WAAA;MACA,KAAAC,aAAA;MACA,KAAAC,aAAA;IACA;IAEA;IACAkG,YAAA,WAAAA,aAAAC,IAAA;MACA,IAAAC,WAAA,sDAAAC,IAAA,CAAAF,IAAA,CAAA7J,IAAA;MACA,IAAAgK,OAAA,GAAAH,IAAA,CAAArB,IAAA;MAEA,KAAAsB,WAAA;QACA,KAAA1F,QAAA,CAAAC,KAAA;QACA;MACA;MACA,KAAA2F,OAAA;QACA,KAAA5F,QAAA,CAAAC,KAAA;QACA;MACA;MACA;IACA;IAEA;IACA4F,UAAA,WAAAA,WAAAC,OAAA;MACA;MACA3D,OAAA,CAAAmD,GAAA,UAAAQ,OAAA,CAAAL,IAAA;MACA;MACA,KAAAxH,QAAA,CAAA8H,IAAA;QACAnK,IAAA,EAAAkK,OAAA,CAAAL,IAAA,CAAA7J,IAAA;QACAoK,GAAA,EAAAC,GAAA,CAAAC,eAAA,CAAAJ,OAAA,CAAAL,IAAA;MACA;MACAK,OAAA,CAAAK,SAAA;IACA;IAEA;IACAC,YAAA,WAAAA,aAAAX,IAAA;MACA,IAAAY,KAAA,QAAApI,QAAA,CAAAqI,OAAA,CAAAb,IAAA;MACA,IAAAY,KAAA;QACA,KAAApI,QAAA,CAAAsI,MAAA,CAAAF,KAAA;MACA;IACA;IAEA;IACAG,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAApE,KAAA,SAAAqE,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACA,KAAAF,MAAA,CAAAG,iBAAA;YACA;UACA;;UAEA;UACA,IAAA7K,IAAA;YACAE,QAAA,EAAAwK,MAAA,CAAAzK,IAAA,CAAAC,QAAA;YACAC,QAAA,EAAAuK,MAAA,CAAAzK,IAAA,CAAAE,QAAA;YACAC,UAAA,EAAAsK,MAAA,CAAAzK,IAAA,CAAAG,UAAA;YACAC,SAAA,EAAAqK,MAAA,CAAAzK,IAAA,CAAAI,SAAA;YACAQ,aAAA,EAAA6J,MAAA,CAAAzK,IAAA,CAAAY,aAAA;YACAC,WAAA,EAAA4J,MAAA,CAAAzK,IAAA,CAAAa,WAAA;YACAC,YAAA,EAAA2J,MAAA,CAAAzK,IAAA,CAAAc,YAAA;YACAC,MAAA,EAAA0J,MAAA,CAAAzK,IAAA,CAAAe,MAAA;YACAV,SAAA,EAAAoK,MAAA,CAAAzK,IAAA,CAAAK,SAAA;YACAM,UAAA,EAAA8J,MAAA,CAAAzK,IAAA,CAAAW;UACA;UAEA,IAAAkK,iCAAA,EAAA9K,IAAA,EAAA6D,IAAA,WAAAC,QAAA;YACA,IAAAA,QAAA,CAAAC,IAAA;cACA2G,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAO,MAAA;YACA;cACAP,MAAA,CAAAK,MAAA,CAAAG,QAAA,CAAApH,QAAA,CAAAE,GAAA;YACA;UACA,GAAAG,KAAA;YACA;YACAuG,MAAA,CAAAK,MAAA,CAAAC,UAAA;YACAN,MAAA,CAAAO,MAAA;UACA;QACA;MACA;IACA;IAEA;IACAA,MAAA,WAAAA,OAAA;MACA,KAAAE,OAAA,CAAAnB,IAAA;QAAAoB,IAAA;MAAA;IACA;IAEA;IACA3J,kBAAA,WAAAA,mBAAA4J,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,KAAAD,KAAA;QACAC,QAAA;QACA;MACA;MAEA,IAAA3K,UAAA,GAAA4K,MAAA,CAAAF,KAAA;MACA,IAAArK,QAAA,GAAAuK,MAAA,MAAAvL,IAAA,CAAAgB,QAAA;MAEA,IAAAL,UAAA;QACA2K,QAAA,KAAAE,KAAA;QACA;MACA;MAEA,IAAAxK,QAAA,QAAAL,UAAA,GAAAK,QAAA;QACAsK,QAAA,KAAAE,KAAA,6EAAAC,MAAA,CAAAzK,QAAA;QACA;MACA;MAEAsK,QAAA;IACA;IAEA;IACA7J,iBAAA,WAAAA,kBAAA2J,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,KAAAD,KAAA;QACAC,QAAA;QACA;MACA;MAEA,IAAAI,KAAA,OAAAC,IAAA;MACAD,KAAA,CAAAE,QAAA;MACA,IAAAC,SAAA,OAAAF,IAAA,CAAAN,KAAA;;MAEA;MACA,IAAAQ,SAAA,GAAAH,KAAA;QACAJ,QAAA,KAAAE,KAAA;QACA;MACA;;MAEA;MACA,SAAAxL,IAAA,CAAAa,WAAA;QACA,IAAAiL,OAAA,OAAAH,IAAA,MAAA3L,IAAA,CAAAa,WAAA;QACA,IAAAgL,SAAA,GAAAC,OAAA;UACAR,QAAA,KAAAE,KAAA;UACA;QACA;MACA;MAEAF,QAAA;IACA;IAEA;IACA5J,eAAA,WAAAA,gBAAA0J,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,KAAAD,KAAA;QACAC,QAAA;QACA;MACA;MAEA,IAAAQ,OAAA,OAAAH,IAAA,CAAAN,KAAA;;MAEA;MACA,SAAArL,IAAA,CAAAY,aAAA;QACA,IAAAiL,SAAA,OAAAF,IAAA,MAAA3L,IAAA,CAAAY,aAAA;QACA,IAAAkL,OAAA,GAAAD,SAAA;UACAP,QAAA,KAAAE,KAAA;UACA;QACA;MACA;MAEAF,QAAA;IACA;IAEA;IACA3J,oBAAA,WAAAA,qBAAAyJ,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,KAAAD,KAAA;QACAC,QAAA;QACA;MACA;MAEA,IAAAxK,YAAA,OAAA6K,IAAA,CAAAN,KAAA;;MAEA;MACA,IAAAK,KAAA,OAAAC,IAAA;MACAD,KAAA,CAAAE,QAAA;MACA,IAAA9K,YAAA,GAAA4K,KAAA;QACAJ,QAAA,KAAAE,KAAA;QACA;MACA;;MAEA;MACA,SAAAxL,IAAA,CAAAa,WAAA;QACA,IAAAiL,OAAA,OAAAH,IAAA,MAAA3L,IAAA,CAAAa,WAAA;QACA,IAAAC,YAAA,GAAAgL,OAAA;UACAR,QAAA,KAAAE,KAAA;UACA;QACA;MACA;MAEAF,QAAA;IACA;IAEA;IACAV,iBAAA,WAAAA,kBAAA;MACA,IAAAc,KAAA,OAAAC,IAAA;MACAD,KAAA,CAAAE,QAAA;;MAEA;MACA,SAAA5L,IAAA,CAAAY,aAAA;QACA,IAAAiL,SAAA,OAAAF,IAAA,MAAA3L,IAAA,CAAAY,aAAA;QACA,IAAAiL,SAAA,GAAAH,KAAA;UACA,KAAAZ,MAAA,CAAAG,QAAA;UACA;QACA;MACA;;MAEA;MACA,SAAAjL,IAAA,CAAAY,aAAA,SAAAZ,IAAA,CAAAa,WAAA;QACA,IAAAgL,UAAA,OAAAF,IAAA,MAAA3L,IAAA,CAAAY,aAAA;QACA,IAAAkL,OAAA,OAAAH,IAAA,MAAA3L,IAAA,CAAAa,WAAA;QACA,IAAAgL,UAAA,GAAAC,OAAA;UACA,KAAAhB,MAAA,CAAAG,QAAA;UACA;QACA;MACA;;MAEA;MACA,SAAAjL,IAAA,CAAAc,YAAA;QACA,IAAAA,YAAA,OAAA6K,IAAA,MAAA3L,IAAA,CAAAc,YAAA;QACA,IAAAA,YAAA,GAAA4K,KAAA;UACA,KAAAZ,MAAA,CAAAG,QAAA;UACA;QACA;;QAEA;QACA,SAAAjL,IAAA,CAAAa,WAAA;UACA,IAAAiL,QAAA,OAAAH,IAAA,MAAA3L,IAAA,CAAAa,WAAA;UACA,IAAAC,YAAA,GAAAgL,QAAA;YACA,KAAAhB,MAAA,CAAAG,QAAA;YACA;UACA;QACA;MACA;MAEA;IACA;EACA;AACA", "ignoreList": []}]}