package com.cssl.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cssl.mapper.BasicCustomersMapper;
import com.cssl.mapper.BasicSuppliersMapper;
import com.cssl.pojo.BasicCustomers;
import com.cssl.pojo.BasicSuppliers;
import com.cssl.service.BasicCustomersService;
import com.cssl.service.BasicSuppliersService;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class BasicSuppliersServiceImpl extends ServiceImpl<BasicSuppliersMapper, BasicSuppliers> implements BasicSuppliersService {
    @Resource
    private BasicSuppliersMapper basicSuppliersMapper;

    @Override
    public List<BasicSuppliers> listBasicSuppliers(BasicSuppliers basicSuppliers) {
        return basicSuppliersMapper.listBasicSuppliers(basicSuppliers);
    }

    @Override
    public int addBasicSuppliers(BasicSuppliers basicSuppliers) {
        basicSuppliers.setCreate_by(SecurityUtils.getUsername());
        basicSuppliers.setCreate_time(new Date());
        basicSuppliers.setIs_delete("0");
        return basicSuppliersMapper.addBasicSuppliers(basicSuppliers);
    }

    @Override
    public int updateBasicSuppliers(BasicSuppliers basicSuppliers) {
        basicSuppliers.setUpdate_by(SecurityUtils.getUsername());
        basicSuppliers.setUpdate_time(new Date());
        return basicSuppliersMapper.updateBasicSuppliers(basicSuppliers);
    }

    @Override
    public int delBasicSuppliers(Long supplier_id) {
        return basicSuppliersMapper.delBasicSuppliers(supplier_id);
    }

    @Override
    public int delBatchBasicSuppliers(List<Long> supplier_ids) {
        return basicSuppliersMapper.delBatchBasicSuppliers(supplier_ids);
    }
}
