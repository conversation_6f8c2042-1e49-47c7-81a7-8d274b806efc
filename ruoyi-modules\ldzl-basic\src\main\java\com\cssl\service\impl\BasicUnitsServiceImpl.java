package com.cssl.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cssl.mapper.BasicUnitsMapper;
import com.cssl.mapper.BasicWlflMapper;
import com.cssl.pojo.BasicUnits;
import com.cssl.pojo.BasicWlfl;
import com.cssl.service.BasicUnitsService;
import com.cssl.service.BasicWlflService;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class BasicUnitsServiceImpl extends ServiceImpl<BasicUnitsMapper, BasicUnits> implements BasicUnitsService {

    @Resource
    private BasicUnitsMapper basicUnitsMapper;

    @Override
    public List<BasicUnits> listBasicUnits(BasicUnits basicUnits) {
        return basicUnitsMapper.listBasicUnits(basicUnits);
    }

    @Override
    public int addBasicUnits(BasicUnits basicUnits) {
        basicUnits.setCreate_time(new Date());
        basicUnits.setIs_delete("0");
        basicUnits.setCreate_by(SecurityUtils.getUsername());
        return basicUnitsMapper.addBasicUnits(basicUnits);
    }

    @Override
    public List<BasicUnits> findByBasicUnits() {
        return basicUnitsMapper.findByBasicUnits();
    }

    @Override
    public int updateBasicUnits(BasicUnits basicUnits) {
        basicUnits.setUpdate_time(new Date());
        basicUnits.setUpdate_by(SecurityUtils.getUsername());
        if(basicUnits.getIsPrimary_unit()==1){
            basicUnits.setConversionrate(null);
           return basicUnitsMapper.updateBasicUnits(basicUnits);
        }
        return basicUnitsMapper.updateBasicUnits(basicUnits);
    }

    @Override
    public int delBasicUnits(Long unit_id) {
        return basicUnitsMapper.delBasicUnits(unit_id);
    }

    @Override
    public int delBatchBasicUnits(List<Long> unit_ids) {
        return basicUnitsMapper.delBatchBasicUnits(unit_ids);
    }
}
