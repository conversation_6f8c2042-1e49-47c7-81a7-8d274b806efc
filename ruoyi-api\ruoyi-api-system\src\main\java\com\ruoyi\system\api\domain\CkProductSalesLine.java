package com.ruoyi.system.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 产品销售出库行表
 * @TableName ck_product_sales_line
 */
@TableName(value ="ck_product_sales_line")
@Data
public class CkProductSalesLine implements Serializable {
    /**
     * 行ID
     */
    @TableId(type = IdType.AUTO)
    private Long line_id;

    /**
     * 出库单ID
     */
    private Long sales_id;

    /**
     * 库存记录ID
     */
    private Long material_stock_id;

    /**
     * 产品ID
     */
    private Long product_id;

    /**
     * 产品名称
     */
    private String product_name;

    /**
     * 规格型号
     */
    private String specification;

    /**
     * 单位
     */
    private String unit;

    /**
     * 出库数量
     */
    private BigDecimal sales_num;

    /**
     * 批次ID
     */
    private Long batch_id;

    /**
     * 批次号
     */
    private String batch_code;

    /**
     * 仓库ID
     */
    private Long warehouse_id;

    /**
     * 库区ID
     */
    private Long location_id;

    /**
     * 库位ID
     */
    private Long area_id;

    /**
     * 备注
     */
    private String remark;

    /**
     * 预留字段1
     */
    private String attr1;

    /**
     * 预留字段2
     */
    private String attr2;

    /**
     * 预留字段3
     */
    private Integer attr3;

    /**
     * 预留字段4
     */
    private Integer attr4;

    /**
     * 创建者
     */
    private String create_by;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date create_time;

    /**
     * 更新者
     */
    private String update_by;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date update_time;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    private String is_delete;

    /**
     * 产品编码
     */
    private String product_code;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        CkProductSalesLine other = (CkProductSalesLine) that;
        return (this.getLine_id() == null ? other.getLine_id() == null : this.getLine_id().equals(other.getLine_id()))
            && (this.getSales_id() == null ? other.getSales_id() == null : this.getSales_id().equals(other.getSales_id()))
            && (this.getMaterial_stock_id() == null ? other.getMaterial_stock_id() == null : this.getMaterial_stock_id().equals(other.getMaterial_stock_id()))
            && (this.getProduct_id() == null ? other.getProduct_id() == null : this.getProduct_id().equals(other.getProduct_id()))
            && (this.getProduct_name() == null ? other.getProduct_name() == null : this.getProduct_name().equals(other.getProduct_name()))
            && (this.getSpecification() == null ? other.getSpecification() == null : this.getSpecification().equals(other.getSpecification()))
            && (this.getUnit() == null ? other.getUnit() == null : this.getUnit().equals(other.getUnit()))
            && (this.getSales_num() == null ? other.getSales_num() == null : this.getSales_num().equals(other.getSales_num()))
            && (this.getBatch_id() == null ? other.getBatch_id() == null : this.getBatch_id().equals(other.getBatch_id()))
            && (this.getBatch_code() == null ? other.getBatch_code() == null : this.getBatch_code().equals(other.getBatch_code()))
            && (this.getWarehouse_id() == null ? other.getWarehouse_id() == null : this.getWarehouse_id().equals(other.getWarehouse_id()))
            && (this.getLocation_id() == null ? other.getLocation_id() == null : this.getLocation_id().equals(other.getLocation_id()))
            && (this.getArea_id() == null ? other.getArea_id() == null : this.getArea_id().equals(other.getArea_id()))
            && (this.getRemark() == null ? other.getRemark() == null : this.getRemark().equals(other.getRemark()))
            && (this.getAttr1() == null ? other.getAttr1() == null : this.getAttr1().equals(other.getAttr1()))
            && (this.getAttr2() == null ? other.getAttr2() == null : this.getAttr2().equals(other.getAttr2()))
            && (this.getAttr3() == null ? other.getAttr3() == null : this.getAttr3().equals(other.getAttr3()))
            && (this.getAttr4() == null ? other.getAttr4() == null : this.getAttr4().equals(other.getAttr4()))
            && (this.getCreate_by() == null ? other.getCreate_by() == null : this.getCreate_by().equals(other.getCreate_by()))
            && (this.getCreate_time() == null ? other.getCreate_time() == null : this.getCreate_time().equals(other.getCreate_time()))
            && (this.getUpdate_by() == null ? other.getUpdate_by() == null : this.getUpdate_by().equals(other.getUpdate_by()))
            && (this.getUpdate_time() == null ? other.getUpdate_time() == null : this.getUpdate_time().equals(other.getUpdate_time()))
            && (this.getIs_delete() == null ? other.getIs_delete() == null : this.getIs_delete().equals(other.getIs_delete()))
            && (this.getProduct_code() == null ? other.getProduct_code() == null : this.getProduct_code().equals(other.getProduct_code()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getLine_id() == null) ? 0 : getLine_id().hashCode());
        result = prime * result + ((getSales_id() == null) ? 0 : getSales_id().hashCode());
        result = prime * result + ((getMaterial_stock_id() == null) ? 0 : getMaterial_stock_id().hashCode());
        result = prime * result + ((getProduct_id() == null) ? 0 : getProduct_id().hashCode());
        result = prime * result + ((getProduct_name() == null) ? 0 : getProduct_name().hashCode());
        result = prime * result + ((getSpecification() == null) ? 0 : getSpecification().hashCode());
        result = prime * result + ((getUnit() == null) ? 0 : getUnit().hashCode());
        result = prime * result + ((getSales_num() == null) ? 0 : getSales_num().hashCode());
        result = prime * result + ((getBatch_id() == null) ? 0 : getBatch_id().hashCode());
        result = prime * result + ((getBatch_code() == null) ? 0 : getBatch_code().hashCode());
        result = prime * result + ((getWarehouse_id() == null) ? 0 : getWarehouse_id().hashCode());
        result = prime * result + ((getLocation_id() == null) ? 0 : getLocation_id().hashCode());
        result = prime * result + ((getArea_id() == null) ? 0 : getArea_id().hashCode());
        result = prime * result + ((getRemark() == null) ? 0 : getRemark().hashCode());
        result = prime * result + ((getAttr1() == null) ? 0 : getAttr1().hashCode());
        result = prime * result + ((getAttr2() == null) ? 0 : getAttr2().hashCode());
        result = prime * result + ((getAttr3() == null) ? 0 : getAttr3().hashCode());
        result = prime * result + ((getAttr4() == null) ? 0 : getAttr4().hashCode());
        result = prime * result + ((getCreate_by() == null) ? 0 : getCreate_by().hashCode());
        result = prime * result + ((getCreate_time() == null) ? 0 : getCreate_time().hashCode());
        result = prime * result + ((getUpdate_by() == null) ? 0 : getUpdate_by().hashCode());
        result = prime * result + ((getUpdate_time() == null) ? 0 : getUpdate_time().hashCode());
        result = prime * result + ((getIs_delete() == null) ? 0 : getIs_delete().hashCode());
        result = prime * result + ((getProduct_code() == null) ? 0 : getProduct_code().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", line_id=").append(line_id);
        sb.append(", sales_id=").append(sales_id);
        sb.append(", material_stock_id=").append(material_stock_id);
        sb.append(", product_id=").append(product_id);
        sb.append(", product_name=").append(product_name);
        sb.append(", specification=").append(specification);
        sb.append(", unit=").append(unit);
        sb.append(", sales_num=").append(sales_num);
        sb.append(", batch_id=").append(batch_id);
        sb.append(", batch_code=").append(batch_code);
        sb.append(", warehouse_id=").append(warehouse_id);
        sb.append(", location_id=").append(location_id);
        sb.append(", area_id=").append(area_id);
        sb.append(", remark=").append(remark);
        sb.append(", attr1=").append(attr1);
        sb.append(", attr2=").append(attr2);
        sb.append(", attr3=").append(attr3);
        sb.append(", attr4=").append(attr4);
        sb.append(", create_by=").append(create_by);
        sb.append(", create_time=").append(create_time);
        sb.append(", update_by=").append(update_by);
        sb.append(", update_time=").append(update_time);
        sb.append(", is_delete=").append(is_delete);
        sb.append(", product_code=").append(product_code);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}