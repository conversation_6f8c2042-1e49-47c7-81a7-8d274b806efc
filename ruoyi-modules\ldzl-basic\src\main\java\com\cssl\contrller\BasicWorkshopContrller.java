package com.cssl.contrller;

import com.cssl.pojo.BasicProduct;
import com.cssl.pojo.BasicWlgl;
import com.cssl.pojo.BasicWorkshop;
import com.cssl.service.BasicWorkshopService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/wsp")
public class BasicWorkshopContrller extends BaseController {
    @Resource
    private BasicWorkshopService basicWorkshopService;

    //查询所有车间信息
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody BasicWorkshop basicWorkshop)
    {
        startPage();
        List<BasicWorkshop> list =basicWorkshopService.listBasicWorkshop(basicWorkshop);
        return getDataTable(list);
    }

    //添加车间信息
    @Log(title = "添加车间信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasicWorkshop basicWorkshop)
    {
        return toAjax(basicWorkshopService.addBasicWorkshop(basicWorkshop));
    }

    //修改车间
    @Log(title = "修改车间", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasicWorkshop basicWorkshop)
    {
        return toAjax(basicWorkshopService.updateBasicWorkshop(basicWorkshop));
    }

    //删除车间
    @Log(title = "删除车间", businessType = BusinessType.UPDATE)
    @PutMapping("/dp/{workshop_id}")
    public AjaxResult edit1(@PathVariable Long workshop_id)
    {
        return toAjax(basicWorkshopService.delBasicWorkshop(workshop_id));
    }

    @PostMapping("/find")
    public TableDataInfo find(@RequestBody BasicWorkshop basicWorkshop)
    {

        List<BasicWorkshop> list =basicWorkshopService.listBasicWorkshop(basicWorkshop);
        return getDataTable(list);
    }


}
