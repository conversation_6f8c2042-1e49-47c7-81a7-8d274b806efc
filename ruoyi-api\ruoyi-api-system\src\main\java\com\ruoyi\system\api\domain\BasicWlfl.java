package com.ruoyi.system.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


@Data
@TableName("basic_wlfl")
public class BasicWlfl implements Serializable {
    @TableId(value ="material_classification_id",type = IdType.AUTO)
    private Integer material_classification_id;
    private String material_classification_code;
    private String material_classification_name;
    private String create_by;
    @JsonFormat(pattern = "yyyy-MM-dd" , timezone = "GMT+8")
    private Date  create_time;
    private String update_by;
    @JsonFormat(pattern = "yyyy-MM-dd" , timezone = "GMT+8")
    private Date update_time;
    private String is_delete;
    @TableField(exist = false)
    private List<BasicWlflz> basicWlflz;
    @TableField(exist = false)
    private BasicWlflz bw;

}
