package com.cssl.contrller;

import com.cssl.pojo.BasicCustomers;
import com.cssl.pojo.BasicFactory;
import com.cssl.pojo.BasicProduct;
import com.cssl.service.BasicFactoryService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/factory")
public class BasicFactoryContrller extends BaseController {
    @Resource
    private BasicFactoryService basicFactoryService;

    //查询所有工厂信息
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody BasicFactory basicFactory)
    {
        startPage();
        List<BasicFactory> list =basicFactoryService.listBasicFactory(basicFactory);
        return getDataTable(list);
    }

    //添加工厂信息
    @Log(title = "添加工厂信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasicFactory basicFactory)
    {

        return toAjax(basicFactoryService.addBasicFactory(basicFactory));
    }

    //修改工厂信息
    @Log(title = "修改工厂信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasicFactory basicFactory)
    {
        return toAjax(basicFactoryService.updateFactory(basicFactory));
    }

    //删除产品
    @Log(title = "删除产品", businessType = BusinessType.UPDATE)
    @PutMapping("/dp/{factory_id}")
    public AjaxResult edit1(@PathVariable Long factory_id)
    {
        return toAjax(basicFactoryService.delFactory(factory_id));
    }

}
