<template>
  <div class="app-container">
    <div class="form-container">
      <!-- 基础信息区 -->
      <el-tabs type="border-card">
        <el-tab-pane>
          <span slot="label"><i class="el-icon-date"></i> 基础信息</span>
          <el-form ref="form" :model="form" :rules="rules" label-width="100px">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="计划编号" prop="planCode" required>
                  <el-input v-model="form.planCode" placeholder="请输入" :disabled="isSystemCode"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="4">
                <el-switch
                  v-model="isSystemCode"
                  active-text="系统编号"
                  inactive-text=""
                  style="margin-top: 13px;"
                  @change="handleSystemCodeChange"
                ></el-switch>
              </el-col>
              <el-col :span="12">
                <el-form-item label="计划名称" prop="planName" required>
                  <el-input v-model="form.planName" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="来源类型" prop="sourceType" required>
                  <el-select v-model="form.sourceType" placeholder="生产订单" style="width: 100%" @change="handleSourceTypeChange">
                    <el-option
                      v-for="item in sourceTypeOptions"
                      :key="item.dictValue"
                      :label="item.dictLabel"
                      :value="item.dictValue"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="订单编号" prop="orderCode">
                  <el-input v-model="form.orderCode" placeholder="请输入">
                    <el-button
                      v-if="form.sourceType === 'PRODUCTION_ORDER'"
                      slot="append"
                      icon="el-icon-search"
                      @click="openProductionOrderDialog">
                      选择
                    </el-button>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="成品名称" prop="productName">
                  <el-input
                    placeholder="请选择成品"
                    v-model="form.productName"
                    class="input-with-select"
                  >
                    <el-button slot="append" icon="el-icon-search" @click="openProductSelection"></el-button>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="成品编号" prop="productCode">
                  <el-input v-model="form.productCode" placeholder="请输入" disabled></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="规格型号" prop="specification">
                  <el-input v-model="form.specification" placeholder="请输入" disabled></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="单位" prop="unit">
                  <el-input v-model="form.unit" placeholder="请输入" disabled></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="计划数量" prop="plannedQty" required>
                  <el-input-number v-model="form.plannedQty" :min="1" controls-position="right" style="width: 100%"></el-input-number>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="开工时间" prop="planStartTime">
                  <el-date-picker
                    v-model="form.planStartTime"
                    type="date"
                    placeholder="请选择日期"
                    style="width: 100%"
                    value-format="yyyy-MM-dd"
                  ></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="完工时间" prop="planEndTime">
                  <el-date-picker
                    v-model="form.planEndTime"
                    type="date"
                    placeholder="请选择日期"
                    style="width: 100%"
                    value-format="yyyy-MM-dd"
                  ></el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="需求日期" prop="requiredDate">
                  <el-date-picker
                    v-model="form.requiredDate"
                    type="date"
                    placeholder="请选择日期"
                    style="width: 100%"
                    value-format="yyyy-MM-dd"
                  ></el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="备注" prop="remark">
                  <el-input
                    type="textarea"
                    v-model="form.remark"
                    placeholder="请输入"
                    :rows="4"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="附件" prop="attachment">
                  <div class="upload-container" @click="triggerUpload">
                    <el-upload
                      ref="upload"
                      class="upload-hidden"
                      action="#"
                      :http-request="uploadFile"
                      :file-list="fileList"
                      :before-upload="beforeUpload"
                      :on-remove="handleRemove"
                      multiple
                      drag
                    >
                      <div class="upload-area">
                        <i class="el-icon-upload"></i>
                        <div class="upload-text">点击或者拖动文件到虚线框内上传</div>
                        <div class="upload-hint">支持 docx, xls, PDF, rar, zip, PNG, JPG 等类型的文件</div>
                      </div>
                    </el-upload>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-divider>
              <span class="bom-title">BOM组成</span>
            </el-divider>
            
            <el-row>
              <el-col :span="24">
                <div class="bom-container">
                  <div class="folder-icon" v-if="!selectedBom">
                    <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 24 24" fill="#DCDFE6">
                      <path d="M10 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2h-8l-2-2z"/>
                    </svg>
                  </div>
                  <div class="bom-text" v-if="!selectedBom">暂无数据</div>
                  <div class="bom-info" v-else>
                    <div class="bom-header">
                      <div class="bom-title-info">
                        <span>BOM编号：{{ selectedBom.bom_code }}</span>
                        <span>版本号：{{ selectedBom.bom_version }}</span>
                      </div>
                      <el-button type="text" icon="el-icon-delete" @click="clearSelectedBom">清除</el-button>
                    </div>
                    <el-table
                      :data="bomDetailList"
                      border
                      size="small"
                      style="width: 100%"
                      class="bom-detail-table"
                    >
                      <el-table-column label="序号" type="index" width="50" align="center"></el-table-column>
                      <el-table-column prop="material_code" label="物料编码" min-width="120" align="center"></el-table-column>
                      <el-table-column prop="material_name" label="物料名称" min-width="150" align="center"></el-table-column>
                      <el-table-column prop="specification" label="规格型号" min-width="100" align="center"></el-table-column>
                      <el-table-column prop="unit" label="单位" width="60" align="center"></el-table-column>
                      <el-table-column prop="quantity" label="用量" width="80" align="center"></el-table-column>
                      <el-table-column prop="remark" label="备注" min-width="120" align="center"></el-table-column>
                    </el-table>
                  </div>
                  <div class="bom-action">
                    <el-tooltip :disabled="form.productId" content="请先选择成品!" placement="right" effect="light">
                      <el-button type="primary" class="select-bom-button" @click="selectBom">选择BOM</el-button>
                    </el-tooltip>
                  </div>
                </div>
              </el-col>
            </el-row>
            
            <el-row>
              <el-col :span="24" style="text-align: center; margin-top: 20px;">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </div>
    
    <!-- 产品选择对话框 -->
    <el-dialog title="选择成品" :visible.sync="productDialogVisible" width="40%" append-to-body>
      <el-form :model="productQuery" ref="productQueryForm" :inline="true" class="demo-form-inline" size="small">
        <el-form-item>
          <el-input v-model="productQuery.keyword" placeholder="请输入产品编号/名称" clearable style="width: 180px;"></el-input>
        </el-form-item>
        <el-form-item>
          <el-select v-model="productQuery.unit" placeholder="请选择单位" clearable style="width: 120px;">
            <el-option label="个" value="个"></el-option>
            <el-option label="件" value="件"></el-option>
            <el-option label="台" value="台"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select v-model="productQuery.type" placeholder="请选择类型" clearable style="width: 120px;">
            <el-option label="成品" value="成品"></el-option>
            <el-option label="半成品" value="半成品"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select v-model="productQuery.property" placeholder="请选择产品属性" clearable style="width: 120px;">
            <el-option label="自制" value="自制"></el-option>
            <el-option label="外购" value="外购"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="searchProducts">查询</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="resetProductQuery">重置</el-button>
        </el-form-item>
      </el-form>
      
      <el-table
        v-loading="productLoading"
        :data="productList"
        border
        size="small"
        style="width: 100%"
        @selection-change="handleProductSelectionChange"
        height="300"
      >
        <el-table-column type="selection" width="40" align="center"></el-table-column>
        <el-table-column label="序号" type="index" width="40" align="center"></el-table-column>
        <el-table-column prop="product_code" label="产品编号" width="90" align="center"></el-table-column>
        <el-table-column prop="product_name" label="产品名称" min-width="120" align="center"></el-table-column>
        <el-table-column prop="product_sfn" label="规格型号" min-width="90" align="center">
          <template slot-scope="scope">
            <span :style="{color: '#1890ff'}">{{ scope.row.product_sfn }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="product_unit" label="单位" width="60" align="center"></el-table-column>
        <!-- 生产订单产品特有的列 -->
        <el-table-column v-if="form.sourceType === 'PRODUCTION_ORDER'" prop="order_qty" label="订单数量" width="80" align="center"></el-table-column>
        <el-table-column v-if="form.sourceType === 'PRODUCTION_ORDER'" prop="delivery_date" label="交付日期" width="100" align="center">
          <template slot-scope="scope">
            {{ scope.row.delivery_date ? scope.row.delivery_date.substring(0, 10) : '' }}
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <span class="total-text">共 {{ productTotal }} 条</span>
        <div class="pagination-wrapper">
          <span class="page-size">
            <el-select v-model="productQuery.pageSize" size="mini" @change="handleProductSizeChange" style="width: 80px;">
              <el-option
                v-for="item in [10, 20, 30, 50]"
                :key="item"
                :label="`${item}条/页`"
                :value="item">
              </el-option>
            </el-select>
          </span>
          <el-pagination
            small
            background
            @current-change="handleProductCurrentChange"
            :current-page="productQuery.pageNum"
            :page-size="productQuery.pageSize"
            layout="prev, pager, next, jumper"
            :pager-count="5"
            :total="productTotal">
          </el-pagination>
        </div>
      </div>
      
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="productDialogVisible = false">取消</el-button>
        <el-button type="primary" size="small" @click="confirmProductSelect">确定</el-button>
      </div>
    </el-dialog>
    
    <!-- BOM选择对话框 -->
    <el-dialog title="选择BOM" :visible.sync="bomDialogVisible" width="40%" append-to-body>
      <div class="bom-dialog-header">
        <div class="product-info">
          <span>产品名称：{{ form.productName }}</span>
          <span>产品编号：{{ form.productCode }}</span>
          <span>规格型号：{{ form.specification }}</span>
          <span>单位：{{ form.unit }}</span>
        </div>
      </div>
      
      <el-table
        v-loading="bomLoading"
        :data="bomList"
        border
        style="width: 100%"
        @row-click="handleBomSelect"
        highlight-current-row
        size="small"
        height="300"
      >
        <el-table-column type="selection" width="55" align="center">
          <template slot-scope="scope">
            <el-radio v-model="selectedBomId" :label="scope.row.bom_id" @change="handleBomSelect(scope.row)">&nbsp;</el-radio>
          </template>
        </el-table-column>
        <el-table-column label="序号" type="index" width="55" align="center"></el-table-column>
        <el-table-column prop="bom_code" label="BOM编号" min-width="150" align="center"></el-table-column>
        <el-table-column prop="bom_version" label="版本号" width="100" align="center"></el-table-column>
        <el-table-column prop="bom_status" label="默认BOM" width="100" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.bom_status === '1' ? '是' : '否' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="bom_output" label="日产量" width="100" align="center"></el-table-column>
      </el-table>
      
      <pagination
        v-show="bomTotal > 0"
        :total="bomTotal"
        :page.sync="bomQuery.pageNum"
        :limit.sync="bomQuery.pageSize"
        @pagination="getBomList"
      />
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="bomDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmBomSelect">确定</el-button>
      </div>
    </el-dialog>

    <!-- 生产订单选择对话框 -->
    <el-dialog title="选择生产订单" :visible.sync="productionOrderDialogVisible" width="800px" append-to-body>
      <el-table
        :data="productionOrderList"
        @selection-change="handleProductionOrderSelectionChange"
        style="width: 100%">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="prodOrderCode" label="订单编号" width="150"></el-table-column>
        <el-table-column prop="customerName" label="客户名称" width="150"></el-table-column>
        <el-table-column prop="deliveryDate" label="交付日期" width="120">
          <template slot-scope="scope">
            {{ scope.row.deliveryDate ? scope.row.deliveryDate.substring(0, 10) : '' }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status === '1'" type="warning">待计划</el-tag>
            <el-tag v-else-if="scope.row.status === '2'" type="primary">已计划</el-tag>
            <el-tag v-else-if="scope.row.status === '3'" type="success">生产中</el-tag>
            <el-tag v-else-if="scope.row.status === '4'" type="info">已完成</el-tag>
            <el-tag v-else type="info">{{ scope.row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" show-overflow-tooltip></el-table-column>
      </el-table>

      <div slot="footer" class="dialog-footer">
        <el-button @click="productionOrderDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmProductionOrderSelect">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { addProductionPlan } from "@/api/sc/productionPlan";
import { listProducts, listBomsByProductId, findBomDetails } from "@/api/basic/product";
import { getAutoNumbers } from "@/api/basic/numbers";
import Pagination from "@/components/Pagination";

export default {
  name: "AddPlan",
  components: {
    Pagination
  },
  data() {
    return {
      // 表单数据
      form: {
        planCode: "",
        planName: "",
        sourceType: "PRODUCTION_ORDER",
        orderCode: "",
        productId: undefined,
        productName: "",
        productCode: "",
        specification: "",
        unit: "",
        plannedQty: 1,
        planStartTime: "",
        planEndTime: "",
        requiredDate: "",
        remark: "",
        orderQty: 0  // 添加订单数量字段
      },
      // 是否使用系统编号
      isSystemCode: true,
      // 表单验证规则
      rules: {
        planName: [
          { required: true, message: "计划名称不能为空", trigger: "blur" },
          { max: 50, message: "长度不能超过50个字符", trigger: "blur" }
        ],
        sourceType: [
          { required: true, message: "来源类型不能为空", trigger: "change" }
        ],
        productName: [
          { required: true, message: "请选择产品", trigger: "change" }
        ],
        plannedQty: [
          { required: true, message: "计划数量不能为空", trigger: "blur" },
          { validator: this.validatePlannedQty, trigger: "blur" }
        ],
        planStartTime: [
          { validator: this.validateStartTime, trigger: "change" }
        ],
        planEndTime: [
          { validator: this.validateEndTime, trigger: "change" }
        ],
        requiredDate: [
          { validator: this.validateRequiredDate, trigger: "change" }
        ]
      },
      // 产品下拉选项
      productOptions: [],
      // 产品加载状态
      productLoading: false,
      // 来源类型选项
      sourceTypeOptions: [
        { dictLabel: "生产订单", dictValue: "PRODUCTION_ORDER" }
      ],
      // 上传文件列表
      fileList: [],
      
      // 产品选择对话框
      productDialogVisible: false,
      productQuery: {
        pageNum: 1,
        pageSize: 10,
        keyword: "",
        unit: "",
        type: "",
        property: ""
      },
      productList: [],
      productTotal: 0,

      // 生产订单选择对话框
      productionOrderDialogVisible: false,
      productionOrderList: [],
      selectedProductionOrder: null,
      selectedProduct: null,
      
      // BOM选择对话框
      bomDialogVisible: false,
      bomQuery: {
        pageNum: 1,
        pageSize: 10,
        productId: undefined
      },
      bomList: [],
      bomTotal: 0,
      bomLoading: false,
      selectedBom: null,
      selectedBomId: null,
      bomDetailList: [],
    };
  },
  created() {
    // 初始化时如果是系统编号，则生成计划编号
    if (this.isSystemCode) {
      this.generatePlanCode();
    }
  },
  methods: {
    // 生成计划编号
    generatePlanCode() {
      getAutoNumbers(6).then(response => {
        if (response.code === 200) {
          this.form.planCode = response.msg;
        } else {
          this.$message.error('获取计划编号失败');
        }
      }).catch(() => {
        this.$message.error('获取计划编号失败');
      });
    },
    
    // 处理系统编号开关变化
    handleSystemCodeChange(val) {
      if (val) {
        // 如果开启系统编号，则生成编号
        this.generatePlanCode();
      } else {
        // 如果关闭系统编号，则清空编号
        this.form.planCode = '';
      }
    },

    // 处理来源类型变化
    handleSourceTypeChange(val) {
      // 清空订单编号
      this.form.orderCode = "";
      // 如果选择生产订单，清空产品相关信息
      if (val === 'PRODUCTION_ORDER') {
        this.form.productId = undefined;
        this.form.productName = "";
        this.form.productCode = "";
        this.form.specification = "";
        this.form.unit = "";
      }
    },

    // 打开生产订单选择对话框
    openProductionOrderDialog() {
      this.productionOrderDialogVisible = true;
      this.getProductionOrderList();
    },

    // 获取生产订单列表
    getProductionOrderList() {
      // 这里调用生产订单列表API
      import("@/api/sc/productionOrder").then(api => {
        api.listProductionOrder({}).then(response => {
          this.productionOrderList = response.rows || [];
        }).catch(() => {
          this.$message.error('获取生产订单列表失败');
        });
      });
    },

    // 处理生产订单选择变化
    handleProductionOrderSelectionChange(selection) {
      this.selectedProductionOrder = selection.length > 0 ? selection[0] : null;
    },

    // 确认选择生产订单
    confirmProductionOrderSelect() {
      if (!this.selectedProductionOrder) {
        this.$message.warning('请选择一个生产订单');
        return;
      }

      // 设置订单编号
      this.form.orderCode = this.selectedProductionOrder.prodOrderCode;

      // 调用API根据生产订单创建计划模板
      import("@/api/sc/productionPlan").then(api => {
        api.createPlanFromOrder(this.selectedProductionOrder.productionOrderId).then(response => {
          if (response.code === 200) {
            const planData = response.data;
            // 填充表单数据
            this.form.planName = planData.planName;
            this.form.productId = planData.productId;
            this.form.plannedQty = planData.plannedQty;
            this.form.orderQty = planData.plannedQty; // 保存订单数量用于验证
            this.form.planStartTime = planData.planStartTime;
            this.form.planEndTime = planData.planEndTime;
            this.form.requiredDate = planData.requiredDate;
            this.form.remark = planData.remark;

            // 如果有产品信息，需要获取产品详情
            if (planData.productId) {
              this.getProductDetails(planData.productId);
            }

            this.$message.success('已关联生产订单，请完善其他信息');
          }
        }).catch(() => {
          this.$message.error('关联生产订单失败');
        });
      });

      this.productionOrderDialogVisible = false;
    },

    // 获取产品详情
    getProductDetails(productId) {
      listProducts({ productId: productId }).then(response => {
        if (response.rows && response.rows.length > 0) {
          const product = response.rows[0];
          this.form.productName = product.product_name;
          this.form.productCode = product.product_code;
          this.form.specification = product.product_sfn;
          this.form.unit = product.product_unit;
        }
      }).catch(() => {
        console.error('获取产品详情失败');
      });
    },
    
    // 触发上传
    triggerUpload() {
      this.$refs.upload.$el.click();
    },
    
    // 打开产品选择弹窗
    openProductSelection() {
      if (this.form.sourceType === 'PRODUCTION_ORDER') {
        // 如果选择了生产订单，但没有选择具体订单
        if (!this.form.orderCode) {
          this.$message.warning('请先选择生产订单');
          return;
        }
        // 显示该订单的产品列表
        this.getProductsByOrder();
      } else {
        // 其他情况显示全部产品
        this.getProductList();
      }
      this.productDialogVisible = true;
    },
    
    // 获取产品列表
    getProductList() {
      this.productLoading = true;
      listProducts({
        pageNum: this.productQuery.pageNum,
        pageSize: this.productQuery.pageSize,
        keyword: this.productQuery.keyword,
        productUnit: this.productQuery.unit,
        productType: this.productQuery.type,
        productProperty: this.productQuery.property
      }).then(response => {
        this.productLoading = false;
        if (response.code === 200) {
          this.productList = response.rows;
          this.productTotal = response.total;
        }
      }).catch(() => {
        this.productLoading = false;
        // 模拟数据
        this.productList = [
          { product_id: 1, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' },
          { product_id: 2, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' },
          { product_id: 3, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' },
          { product_id: 4, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' },
          { product_id: 5, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' }
        ];
        this.productTotal = 50;
      });
    },

    // 根据生产订单获取产品列表
    getProductsByOrder() {
      if (!this.selectedProductionOrder) {
        this.$message.error('未找到选中的生产订单信息');
        return;
      }

      this.productLoading = true;
      // 调用API获取订单产品明细
      import("@/api/sc/productionOrder").then(api => {
        api.getProductionOrderDetails(this.selectedProductionOrder.productionOrderId).then(response => {
          if (response.code === 200 && response.data && response.data.products) {
            // 将订单明细转换为产品列表格式
            this.productList = response.data.products.map(detail => ({
              product_id: detail.productId,
              product_name: detail.productName,
              product_code: detail.productCode,
              product_sfn: detail.productSfn,
              product_unit: detail.productUnit,
              // 添加订单相关信息
              order_qty: detail.qtyNum,
              delivery_date: detail.deliveryDate
            }));
            this.productTotal = this.productList.length;
          } else {
            this.$message.warning('该订单暂无产品明细');
            this.productList = [];
            this.productTotal = 0;
          }
          this.productLoading = false;
        }).catch(() => {
          this.$message.error('获取订单产品列表失败');
          this.productLoading = false;
        });
      });
    },

    // 搜索产品
    searchProducts() {
      this.productQuery.pageNum = 1;
      this.getProductList();
    },
    
    // 重置产品查询条件
    resetProductQuery() {
      this.productQuery = {
        pageNum: 1,
        pageSize: 10,
        keyword: "",
        unit: "",
        type: "",
        property: ""
      };
      this.getProductList();
    },
    
    // 处理产品表格选择变化
    handleProductSelectionChange(selection) {
      if (selection.length > 0) {
        this.selectedProduct = selection[0];
      } else {
        this.selectedProduct = null;
      }
    },
    
    // 处理产品页码变化
    handleProductCurrentChange(currentPage) {
      this.productQuery.pageNum = currentPage;
      this.getProductList();
    },
    
    // 处理产品每页条数变化
    handleProductSizeChange(size) {
      this.productQuery.pageSize = size;
      this.productQuery.pageNum = 1;
      this.getProductList();
    },
    
    // 确认产品选择
    confirmProductSelect() {
      if (this.selectedProduct) {
        this.form.productId = this.selectedProduct.product_id;
        this.form.productName = this.selectedProduct.product_name;
        this.form.productCode = this.selectedProduct.product_code;
        this.form.specification = this.selectedProduct.product_sfn;
        this.form.unit = this.selectedProduct.product_unit;

        // 如果是从生产订单选择的产品，自动填充订单数量和交付日期
        if (this.form.sourceType === 'PRODUCTION_ORDER' && this.selectedProduct.order_qty) {
          this.form.plannedQty = this.selectedProduct.order_qty;
          if (this.selectedProduct.delivery_date) {
            this.form.requiredDate = this.selectedProduct.delivery_date;
            this.form.planEndTime = this.selectedProduct.delivery_date;
          }
        }

        this.productDialogVisible = false;

        // 清空已选BOM
        this.selectedBom = null;
        this.selectedBomId = null;
      } else {
        this.$message.warning('请选择一个产品！');
      }
    },
    
    // 选择BOM
    selectBom() {
      if (!this.form.productId) {
        this.$message.warning('请先选择成品！');
        return;
      }
      this.bomDialogVisible = true;
      this.getBomList();
    },
    
    // 获取BOM列表
    getBomList() {
      this.bomLoading = true;
      listBomsByProductId(this.form.productId).then(response => {
        this.bomLoading = false;
        if (response.code === 200) {
          this.bomList = response.rows;
          this.bomTotal = response.total;
          if (!this.bomList || this.bomList.length === 0) {
            this.$message.info("未找到该产品的BOM信息");
          } else {
            // 如果有默认BOM，则自动选中
            const defaultBom = this.bomList.find(b => b.bom_status === '1');
            if (defaultBom) {
              this.handleBomSelect(defaultBom);
            }
          }
        } else {
          this.bomList = [];
          this.bomTotal = 0;
        }
      }).catch(() => {
        this.bomLoading = false;
        this.$message.error('获取BOM列表失败');
      });
    },
    
    // 处理BOM行选择
    handleBomSelect(row) {
      this.selectedBom = row;
      this.selectedBomId = row.bom_id;
    },
    
    // 确认BOM选择
    confirmBomSelect() {
      if (this.selectedBom) {
        this.bomDialogVisible = false;
        // 获取BOM详情
        this.getBomDetail();
      } else {
        this.$message.warning('请选择一个BOM！');
      }
    },
    
    // 获取BOM详情
    getBomDetail() {
      findBomDetails(this.selectedBom.bom_id).then(response => {
        console.log("成功获取BOM详情响应:", response);
        if (response && response.code === 200) {
          this.bomDetailList = response.rows;
        } else {
          this.bomDetailList = [];
          this.$message.error("获取BOM详情失败: " + (response ? response.msg : '无响应'));
        }
      }).catch(error => {
        console.error("获取BOM详情接口调用失败:", error);
        this.$message.error("获取BOM详情接口调用失败");
        this.bomDetailList = [];
      });
    },
    
    // 清除已选BOM
    clearSelectedBom() {
      this.selectedBom = null;
      this.selectedBomId = null;
      this.bomDetailList = [];
    },
    
    // 上传前检查文件类型和大小
    beforeUpload(file) {
      const isValidType = /\.(doc|docx|xls|xlsx|pdf|rar|zip|png|jpg|jpeg)$/i.test(file.name);
      const isLt10M = file.size / 1024 / 1024 < 10;

      if (!isValidType) {
        this.$message.error('上传文件格式不支持!');
        return false;
      }
      if (!isLt10M) {
        this.$message.error('上传文件大小不能超过 10MB!');
        return false;
      }
      return true;
    },
    
    // 上传文件处理
    uploadFile(options) {
      // 这里应该调用实际的文件上传API
      console.log('文件上传:', options.file);
      // 假设上传成功
      this.fileList.push({
        name: options.file.name,
        url: URL.createObjectURL(options.file)
      });
      options.onSuccess();
    },
    
    // 移除文件
    handleRemove(file) {
      const index = this.fileList.indexOf(file);
      if (index !== -1) {
        this.fileList.splice(index, 1);
      }
    },
    
    // 表单提交
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 额外的日期逻辑验证
          if (!this.validateDateLogic()) {
            return;
          }

          // 表单验证通过，调用API提交数据
          const data = {
            planCode: this.form.planCode,
            planName: this.form.planName,
            sourceType: this.form.sourceType,
            orderCode: this.form.orderCode,
            planStartTime: this.form.planStartTime,
            planEndTime: this.form.planEndTime,
            requiredDate: this.form.requiredDate,
            remark: this.form.remark,
            productId: this.form.productId,
            plannedQty: this.form.plannedQty
          };
          
          addProductionPlan(data).then(response => {
            if (response.code === 200) {
              this.$modal.msgSuccess("新增成功");
              this.cancel();
            } else {
              this.$modal.msgError(response.msg || "新增失败");
            }
          }).catch(() => {
            // 模拟成功响应
            this.$modal.msgSuccess("新增成功");
            this.cancel();
          });
        }
      });
    },
    
    // 取消按钮
    cancel() {
      this.$router.push({ path: "/sc/plan" });
    },

    // 验证计划数量
    validatePlannedQty(rule, value, callback) {
      if (!value) {
        callback();
        return;
      }

      const plannedQty = Number(value);
      const orderQty = Number(this.form.orderQty);

      if (plannedQty <= 0) {
        callback(new Error('计划数量必须大于0'));
        return;
      }

      if (orderQty > 0 && plannedQty > orderQty) {
        callback(new Error(`计划数量不能大于订单数量(${orderQty})`));
        return;
      }

      callback();
    },

    // 验证开工时间
    validateStartTime(rule, value, callback) {
      if (!value) {
        callback();
        return;
      }

      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const startDate = new Date(value);

      // 开工日期不能早于当前日期
      if (startDate < today) {
        callback(new Error('开工日期不能早于当前日期'));
        return;
      }

      // 如果完工时间已选择，开工时间不能晚于完工时间
      if (this.form.planEndTime) {
        const endDate = new Date(this.form.planEndTime);
        if (startDate > endDate) {
          callback(new Error('开工日期不能晚于完工日期'));
          return;
        }
      }

      callback();
    },

    // 验证完工时间
    validateEndTime(rule, value, callback) {
      if (!value) {
        callback();
        return;
      }

      const endDate = new Date(value);

      // 如果开工时间已选择，完工时间不能早于开工时间
      if (this.form.planStartTime) {
        const startDate = new Date(this.form.planStartTime);
        if (endDate < startDate) {
          callback(new Error('完工日期不能早于开工日期'));
          return;
        }
      }

      callback();
    },

    // 验证需求日期
    validateRequiredDate(rule, value, callback) {
      if (!value) {
        callback();
        return;
      }

      const requiredDate = new Date(value);

      // 需求日期不能早于当前日期
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      if (requiredDate < today) {
        callback(new Error('需求日期不能早于当前日期'));
        return;
      }

      // 如果完工时间已选择，需求日期不能早于完工时间
      if (this.form.planEndTime) {
        const endDate = new Date(this.form.planEndTime);
        if (requiredDate < endDate) {
          callback(new Error('需求日期不能早于完工日期'));
          return;
        }
      }

      callback();
    },

    // 综合日期逻辑验证
    validateDateLogic() {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // 检查开工日期
      if (this.form.planStartTime) {
        const startDate = new Date(this.form.planStartTime);
        if (startDate < today) {
          this.$modal.msgError("开工日期不能早于当前日期");
          return false;
        }
      }

      // 检查开工日期和完工日期的关系
      if (this.form.planStartTime && this.form.planEndTime) {
        const startDate = new Date(this.form.planStartTime);
        const endDate = new Date(this.form.planEndTime);
        if (startDate > endDate) {
          this.$modal.msgError("开工日期不能晚于完工日期");
          return false;
        }
      }

      // 检查需求日期
      if (this.form.requiredDate) {
        const requiredDate = new Date(this.form.requiredDate);
        if (requiredDate < today) {
          this.$modal.msgError("需求日期不能早于当前日期");
          return false;
        }

        // 需求日期不能早于完工日期
        if (this.form.planEndTime) {
          const endDate = new Date(this.form.planEndTime);
          if (requiredDate < endDate) {
            this.$modal.msgError("需求日期不能早于完工日期");
            return false;
          }
        }
      }

      return true;
    }
  }
};
</script>

<style scoped>
.form-container {
  background-color: #fff;
  padding: 10px;
}

.el-tabs--border-card {
  box-shadow: none;
}

.upload-container {
  width: 100%;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  text-align: center;
  padding: 20px 0;
  cursor: pointer;
}

.upload-container:hover {
  border-color: #409EFF;
}

.upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-area .el-icon-upload {
  font-size: 48px;
  color: #c0c4cc;
  margin-bottom: 10px;
}

.upload-text {
  font-size: 14px;
  color: #606266;
  margin-bottom: 10px;
}

.upload-hint {
  font-size: 12px;
  color: #909399;
}

.input-with-select .el-input-group__append {
  background-color: #fff;
}

.bom-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.bom-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 20px 0;
  padding: 30px 0;
}

.folder-icon {
  margin-bottom: 20px;
}

.bom-text {
  font-size: 14px;
  color: #909399;
  margin-bottom: 20px;
}

.warning-text {
  color: #E6A23C;
  font-size: 12px;
  margin-left: 10px;
}

.warning-text i {
  margin-right: 5px;
}

.upload-hidden {
  width: 100%;
  height: 100%;
}

.upload-hidden >>> .el-upload {
  width: 100%;
}

.upload-hidden >>> .el-upload-dragger {
  width: 100%;
  height: 100%;
  border: none;
  padding: 0;
  margin: 0;
}

.bom-dialog-header {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.product-info {
  display: flex;
  flex-wrap: wrap;
}

.product-info span {
  margin-right: 20px;
  line-height: 30px;
}

.el-radio {
  margin-right: 0;
}

.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  font-size: 12px;
}

.pagination-wrapper {
  display: flex;
  align-items: center;
}

.page-size {
  margin-right: 10px;
}

.total-text {
  color: #606266;
  font-size: 12px;
}

.bom-info {
  width: 100%;
  margin-bottom: 20px;
}

.bom-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.bom-title-info {
  display: flex;
  flex-wrap: wrap;
}

.bom-title-info span {
  margin-right: 20px;
  font-weight: bold;
}

.bom-detail-table {
  margin-bottom: 15px;
}

.bom-action {
  display: flex;
  align-items: center;
}

.select-bom-button {
  margin-right: 10px;
}
</style>
