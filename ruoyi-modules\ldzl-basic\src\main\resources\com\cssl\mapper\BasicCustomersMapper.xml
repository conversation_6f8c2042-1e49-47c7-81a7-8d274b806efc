<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cssl.mapper.BasicCustomersMapper">

    <insert id="addBasicCustomers">
        INSERT into basic_customers
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customer_code!= null and customer_code!=''">customer_code,</if>
            <if test="customer_name != null and customer_name != ''">customer_name,</if>
            <if test="customer_type != null  ">customer_type,</if>
            <if test="customer_person != null and customer_person != ''" >customer_person,</if>
            <if test="customer_phone != null and customer_phone !=''">customer_phone,</if>
            <if test="cp_status != null ">cp_status,</if>
            <if test="customer_status != null ">customer_status,</if>
            <if test="remarks != null and remarks !=''">remarks,</if>
            <if test="is_detele != null ">is_detele,</if>
            <if test="create_by != null and create_by !='' ">create_by,</if>
            <if test="create_time!= null ">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customer_code!= null and customer_code!=''">#{customer_code},</if>
            <if test="customer_name != null and customer_name != ''">#{customer_name},</if>
            <if test="customer_type != null  ">#{customer_type},</if>
            <if test="customer_person != null and customer_person != ''" >#{customer_person},</if>
            <if test="customer_phone != null and customer_phone !=''">#{customer_phone},</if>
            <if test="cp_status != null ">#{cp_status},</if>
            <if test="customer_status != null ">#{customer_status},</if>
            <if test="remarks != null and remarks !=''">#{remarks},</if>
            <if test="is_detele != null ">#{is_detele},</if>
            <if test="create_by != null and create_by !='' ">#{create_by},</if>
            <if test="create_time!= null ">#{create_time},</if>
        </trim>
    </insert>

    <update id="updateCustomers">
        update basic_customers
        <trim prefix="SET" suffixOverrides=",">
            <if test="customer_name != null and customer_name != ''">customer_name=#{customer_name},</if>
            <if test="customer_type != null and customer_type !='' ">customer_type=#{customer_type},</if>
            <if test="customer_person != null and customer_person != ''" >customer_person=#{customer_person},</if>
            <if test="customer_phone != null and customer_phone !=''">customer_phone=#{customer_phone},</if>
            <if test="cp_status != null ">cp_status=#{cp_status},</if>
            <if test="customer_status != null ">customer_status=#{customer_status},</if>
            <if test="remarks != null and remarks !=''">remarks=#{remarks},</if>
            <if test="update_by != null and update_by !='' ">update_by=#{update_by},</if>
            <if test="update_time!= null ">update_time=#{update_time},</if>
        </trim>
        where customer_id = #{customer_id}
    </update>


    <update id="delCustomers">
        update basic_customers
        <trim prefix="SET" suffixOverrides=",">
            <if test="is_detele != null ">is_detele =1,</if>
        </trim>
        where customer_id = #{customer_id}
    </update>

    <update id="delBatchCustomers">
        update basic_customers set is_detele = '1' where customer_id in
        <foreach item="customer_ids" collection="list" open="(" separator="," close=")">
            #{customer_ids}
        </foreach>
    </update>


    <select id="listBasicCustomers" resultType="com.cssl.pojo.BasicCustomers">
        SELECT * from basic_customers where is_detele=0
        <if test="customer_name !=null and customer_name !=''">and customer_name like CONCAT('%',#{customer_name},'%')</if>
        <if test="customer_type !=null and customer_type !=''">and customer_type=#{customer_type}</if>
        <if test="customer_status!=null and customer_status!=''">and customer_status=#{customer_status}</if>
    </select>
</mapper>