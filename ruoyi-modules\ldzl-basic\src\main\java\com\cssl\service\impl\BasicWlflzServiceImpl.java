package com.cssl.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cssl.mapper.BasicWlflMapper;
import com.cssl.mapper.BasicWlflzMapper;
import com.cssl.pojo.BasicWlfl;
import com.cssl.pojo.BasicWlflz;
import com.cssl.service.BasicWlflService;
import com.cssl.service.BasicWlflzService;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class BasicWlflzServiceImpl extends ServiceImpl<BasicWlflzMapper, BasicWlflz> implements BasicWlflzService {
    @Resource
    private BasicWlflzMapper basicWlflzMapper;
    @Resource
    private BasicWlflMapper basicWlflMapper;
    @Override
    public int insertBasicWlflz(BasicWlflz basicWlflz) {
        basicWlflz.setCreate_time(DateUtils.getNowDate());
        basicWlflz.setIs_delete("0");
        basicWlflz.setCreate_by(SecurityUtils.getUsername());
        return basicWlflzMapper.insertBasicWlflz(basicWlflz);
    }
    @Override
    public List<BasicWlflz> selectBasicWlflzList(BasicWlflz basicWlflz) {

        return basicWlflzMapper.selectBasicWlflzList(basicWlflz);
    }

    @Override
    public int updateBasicWlflz(BasicWlflz basicWlflz) {
        basicWlflz.setUpdate_time(new Date());
        basicWlflz.setUpdate_by(SecurityUtils.getUsername());
        return basicWlflzMapper.updateBasicWlflz(basicWlflz);
    }

    @Override
    public int delBasicWlflz(Long material_subcategory_id) {
        return basicWlflzMapper.delBasicWlflz(material_subcategory_id);
    }
}
