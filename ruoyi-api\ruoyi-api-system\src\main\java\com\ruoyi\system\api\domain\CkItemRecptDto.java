package com.ruoyi.system.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 物料入库单表
 * @TableName ck_item_recpt
 */
@TableName(value ="ck_item_recpt")
@Data
public class CkItemRecptDto implements Serializable {
    /**
     * 入库单ID
     */
    @TableId(type = IdType.AUTO)
    private Long recpt_id;

    /**
     * 入库单编号
     */
    private String recpt_code;

    /**
     * 入库单名称
     */
    private String recpt_name;

    /**
     * 来料检验单ID
     */
    private Long iqc_id;

    /**
     * 来料检验单编号
     */
    private String iqc_code;

    /**
     * 来料检验单名称
     */
    private String iqc_name;

    /**
     * 采购订单编号
     */
    private Long po_id;

    /**
     * 采购订单编号
     */
    private String po_code;

    /**
     * 采购订单编号
     */
    private String po_name;

    /**
     * 批次编号
     */
    private String batch_code;

    /**
     * 供应商ID
     */
    private Long supplier_id;

    /**
     * 供应商名称
     */
    private String supplier_name;

    /**
     * 生产工单ID
     */
    private Long work_order_id;

    /**
     * 生产工单编号
     */
    private String work_order_code;

    /**
     * 生产工单名称
     */
    private String work_order_name;

    /**
     * 入库类型（1.采购入库 2.生产退料 3.产品入库）
     */
    private String storage_type;

    /**
     * 仓库ID
     */
    private Long warehouse_id;

    /**
     * 仓库名称
     */
    private String warehouse_name;

    /**
     * 入库日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date recpt_date;

    /**
     * 单据状态
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 预留字段1
     */
    private String attr1;

    /**
     * 预留字段2
     */
    private String attr2;

    /**
     * 预留字段3
     */
    private Integer attr3;

    /**
     * 预留字段4
     */
    private Integer attr4;

    /**
     * 创建者
     */
    private String create_by;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date create_time;

    /**
     * 更新者
     */
    private String update_by;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date update_time;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    private String is_delete;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        CkItemRecptDto other = (CkItemRecptDto) that;
        return (this.getRecpt_id() == null ? other.getRecpt_id() == null : this.getRecpt_id().equals(other.getRecpt_id()))
            && (this.getRecpt_code() == null ? other.getRecpt_code() == null : this.getRecpt_code().equals(other.getRecpt_code()))
            && (this.getRecpt_name() == null ? other.getRecpt_name() == null : this.getRecpt_name().equals(other.getRecpt_name()))
            && (this.getIqc_id() == null ? other.getIqc_id() == null : this.getIqc_id().equals(other.getIqc_id()))
            && (this.getIqc_code() == null ? other.getIqc_code() == null : this.getIqc_code().equals(other.getIqc_code()))
            && (this.getIqc_name() == null ? other.getIqc_name() == null : this.getIqc_name().equals(other.getIqc_name()))
            && (this.getPo_id() == null ? other.getPo_id() == null : this.getPo_id().equals(other.getPo_id()))
            && (this.getPo_code() == null ? other.getPo_code() == null : this.getPo_code().equals(other.getPo_code()))
            && (this.getPo_name() == null ? other.getPo_name() == null : this.getPo_name().equals(other.getPo_name()))
            && (this.getBatch_code() == null ? other.getBatch_code() == null : this.getBatch_code().equals(other.getBatch_code()))
            && (this.getSupplier_id() == null ? other.getSupplier_id() == null : this.getSupplier_id().equals(other.getSupplier_id()))
            && (this.getSupplier_name() == null ? other.getSupplier_name() == null : this.getSupplier_name().equals(other.getSupplier_name()))
            && (this.getWork_order_id() == null ? other.getWork_order_id() == null : this.getWork_order_id().equals(other.getWork_order_id()))
            && (this.getWork_order_code() == null ? other.getWork_order_code() == null : this.getWork_order_code().equals(other.getWork_order_code()))
            && (this.getWork_order_name() == null ? other.getWork_order_name() == null : this.getWork_order_name().equals(other.getWork_order_name()))
            && (this.getStorage_type() == null ? other.getStorage_type() == null : this.getStorage_type().equals(other.getStorage_type()))
            && (this.getWarehouse_id() == null ? other.getWarehouse_id() == null : this.getWarehouse_id().equals(other.getWarehouse_id()))
            && (this.getWarehouse_name() == null ? other.getWarehouse_name() == null : this.getWarehouse_name().equals(other.getWarehouse_name()))
            && (this.getRecpt_date() == null ? other.getRecpt_date() == null : this.getRecpt_date().equals(other.getRecpt_date()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getRemark() == null ? other.getRemark() == null : this.getRemark().equals(other.getRemark()))
            && (this.getAttr1() == null ? other.getAttr1() == null : this.getAttr1().equals(other.getAttr1()))
            && (this.getAttr2() == null ? other.getAttr2() == null : this.getAttr2().equals(other.getAttr2()))
            && (this.getAttr3() == null ? other.getAttr3() == null : this.getAttr3().equals(other.getAttr3()))
            && (this.getAttr4() == null ? other.getAttr4() == null : this.getAttr4().equals(other.getAttr4()))
            && (this.getCreate_by() == null ? other.getCreate_by() == null : this.getCreate_by().equals(other.getCreate_by()))
            && (this.getCreate_time() == null ? other.getCreate_time() == null : this.getCreate_time().equals(other.getCreate_time()))
            && (this.getUpdate_by() == null ? other.getUpdate_by() == null : this.getUpdate_by().equals(other.getUpdate_by()))
            && (this.getUpdate_time() == null ? other.getUpdate_time() == null : this.getUpdate_time().equals(other.getUpdate_time()))
            && (this.getIs_delete() == null ? other.getIs_delete() == null : this.getIs_delete().equals(other.getIs_delete()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getRecpt_id() == null) ? 0 : getRecpt_id().hashCode());
        result = prime * result + ((getRecpt_code() == null) ? 0 : getRecpt_code().hashCode());
        result = prime * result + ((getRecpt_name() == null) ? 0 : getRecpt_name().hashCode());
        result = prime * result + ((getIqc_id() == null) ? 0 : getIqc_id().hashCode());
        result = prime * result + ((getIqc_code() == null) ? 0 : getIqc_code().hashCode());
        result = prime * result + ((getIqc_name() == null) ? 0 : getIqc_name().hashCode());
        result = prime * result + ((getPo_id() == null) ? 0 : getPo_id().hashCode());
        result = prime * result + ((getPo_code() == null) ? 0 : getPo_code().hashCode());
        result = prime * result + ((getPo_name() == null) ? 0 : getPo_name().hashCode());
        result = prime * result + ((getBatch_code() == null) ? 0 : getBatch_code().hashCode());
        result = prime * result + ((getSupplier_id() == null) ? 0 : getSupplier_id().hashCode());
        result = prime * result + ((getSupplier_name() == null) ? 0 : getSupplier_name().hashCode());
        result = prime * result + ((getWork_order_id() == null) ? 0 : getWork_order_id().hashCode());
        result = prime * result + ((getWork_order_code() == null) ? 0 : getWork_order_code().hashCode());
        result = prime * result + ((getWork_order_name() == null) ? 0 : getWork_order_name().hashCode());
        result = prime * result + ((getStorage_type() == null) ? 0 : getStorage_type().hashCode());
        result = prime * result + ((getWarehouse_id() == null) ? 0 : getWarehouse_id().hashCode());
        result = prime * result + ((getWarehouse_name() == null) ? 0 : getWarehouse_name().hashCode());
        result = prime * result + ((getRecpt_date() == null) ? 0 : getRecpt_date().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getRemark() == null) ? 0 : getRemark().hashCode());
        result = prime * result + ((getAttr1() == null) ? 0 : getAttr1().hashCode());
        result = prime * result + ((getAttr2() == null) ? 0 : getAttr2().hashCode());
        result = prime * result + ((getAttr3() == null) ? 0 : getAttr3().hashCode());
        result = prime * result + ((getAttr4() == null) ? 0 : getAttr4().hashCode());
        result = prime * result + ((getCreate_by() == null) ? 0 : getCreate_by().hashCode());
        result = prime * result + ((getCreate_time() == null) ? 0 : getCreate_time().hashCode());
        result = prime * result + ((getUpdate_by() == null) ? 0 : getUpdate_by().hashCode());
        result = prime * result + ((getUpdate_time() == null) ? 0 : getUpdate_time().hashCode());
        result = prime * result + ((getIs_delete() == null) ? 0 : getIs_delete().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", recpt_id=").append(recpt_id);
        sb.append(", recpt_code=").append(recpt_code);
        sb.append(", recpt_name=").append(recpt_name);
        sb.append(", iqc_id=").append(iqc_id);
        sb.append(", iqc_code=").append(iqc_code);
        sb.append(", iqc_name=").append(iqc_name);
        sb.append(", po_id=").append(po_id);
        sb.append(", po_code=").append(po_code);
        sb.append(", po_name=").append(po_name);
        sb.append(", batch_name=").append(batch_code);
        sb.append(", supplier_id=").append(supplier_id);
        sb.append(", supplier_name=").append(supplier_name);
        sb.append(", work_order_id=").append(work_order_id);
        sb.append(", work_order_code=").append(work_order_code);
        sb.append(", work_order_name=").append(work_order_name);
        sb.append(", storage_type=").append(storage_type);
        sb.append(", warehouse_id=").append(warehouse_id);
        sb.append(", warehouse_name=").append(warehouse_name);
        sb.append(", recpt_date=").append(recpt_date);
        sb.append(", status=").append(status);
        sb.append(", remark=").append(remark);
        sb.append(", attr1=").append(attr1);
        sb.append(", attr2=").append(attr2);
        sb.append(", attr3=").append(attr3);
        sb.append(", attr4=").append(attr4);
        sb.append(", create_by=").append(create_by);
        sb.append(", create_time=").append(create_time);
        sb.append(", update_by=").append(update_by);
        sb.append(", update_time=").append(update_time);
        sb.append(", is_delete=").append(is_delete);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}