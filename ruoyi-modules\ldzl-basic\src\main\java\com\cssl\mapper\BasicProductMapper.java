package com.cssl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cssl.pojo.BasicProduct;

import java.util.List;

public interface BasicProductMapper extends BaseMapper<BasicProduct> {
    //查询产品信息
    public List<BasicProduct> listBasicProductMapper(BasicProduct basicProduct);

    //添加产品信息
    public int addBasicProduct(BasicProduct basicProduct);


    //修改产品信息
    public int updateBasicProduct(BasicProduct basicProduct);

    //删除产品信息
    public int delBasicProduct(Long product_id);

    //批量删除产品信息
    public int delBasicProductByIds(List<Long> product_ids);

    //查询产品详细信息
    public BasicProduct selectBasicProductById(Long product_id);

}
