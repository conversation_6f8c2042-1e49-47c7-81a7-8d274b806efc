package com.ruoyi.system.api.domain;

import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class BasicProcessDto extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private Long process_id;
    private String process_code;
    private String process_name;
    private String process_status;
    private String process_description;
    private String remarks;
} 