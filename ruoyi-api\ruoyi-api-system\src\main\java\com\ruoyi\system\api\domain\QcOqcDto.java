package com.ruoyi.system.api.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class QcOqcDto {

    private static final long serialVersionUID = 1L;

    /** 出货检验单ID */
    private Long oqcId;

    /** 出货检验单编号 */
    @Excel(name = "出货检验单编号")
    private String oqcCode;

    /** 出货检验单名称 */
    @Excel(name = "出货检验单名称")
    private String oqcName;

    /** 检验模板ID */
    private Long templateId;

    /** 来源单据ID */
    private Long sourceDocId;

    /** 来源单据类型 */
    @Excel(name = "来源单据类型")
    private String sourceDocType;

    /** 来源单据编号 */
    @Excel(name = "来源单据编号")
    private String sourceDocCode;

    /** 来源单据行ID */
    private Long sourceLineId;

    /** 客户ID */
    private Long customerId;

    /** 客户编码 */
    @Excel(name = "客户编码")
    private String customerCode;

    /** 客户名称 */
    @Excel(name = "客户名称")
    private String customerName;

    /** 批次号 */
    @Excel(name = "批次号")
    private String batchCode;

    /** 产品ID */
    private Long productId;

    /** 产品编码 */
    @Excel(name = "产品编码")
    private String productCode;

    /** 产品名称 */
    @Excel(name = "产品名称")
    private String productName;

    /** 产品规格型号 */
    @Excel(name = "产品规格型号")
    private String productSfn;

    /** 单位 */
    @Excel(name = "单位")
    private String unit;

    /** 最低检测数 */
    private Long quantityMinCheckNum;

    /** 最大不合格数 */
    private Long quantityMaxUnqualifiedNum;

    /** 发货数量 */
    @Excel(name = "发货数量")
    private Long quantityOutNum;

    /** 本次检测数量 */
    @Excel(name = "本次检测数量")
    private Long quantityCheckNum;

    /** 不合格数 */
    @Excel(name = "不合格数")
    private Long quantityUnqualifiedNum;

    /** 合格数量 */
    @Excel(name = "合格数量")
    private Long quantityQuanlifiedNum;

    /** 致命缺陷率 */
    private BigDecimal crRate;

    /** 严重缺陷率 */
    private BigDecimal majRate;

    /** 轻微缺陷率 */
    private BigDecimal minRate;

    /** 致命缺陷数量 */
    private Long crQuantityNum;

    /** 严重缺陷数量 */
    private Long majQuantityNum;

    /** 轻微缺陷数量 */
    private Long minQuantityNum;

    /** 检测结果(1/合格，2/不合格,3/待检) */
    @Excel(name = "检测结果(1/合格，2/不合格,3/待检)")
    private String checkResult;

    /** 出货日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "出货日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date outDate;

    /** 检测日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "检测日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date inspectDate;

    /** 检测人员 */
    private Long inspectorId;

    /** 单据状态(1/草稿,2/已提交,3/已审核) */
    @Excel(name = "单据状态(1/草稿,2/已提交,3/已审核)")
    private String status;

    /** 预留字段1 */
    private String attr1;

    /** 预留字段2 */
    private String attr2;

    /** 预留字段3 */
    private Long attr3;

    /** 预留字段4 */
    private Long attr4;

    /** 删除标志（0代表存在 1代表删除） */
    private String isDelete;
}
