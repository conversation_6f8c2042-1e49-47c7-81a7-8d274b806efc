package com.cssl.contrller;

import com.cssl.pojo.BasicProduct;
import com.cssl.pojo.BasicUnits;
import com.cssl.pojo.BasicWlgl;
import com.cssl.service.BasicProductService;
import com.github.pagehelper.PageHelper;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.file.FileTypeUtils;
import com.ruoyi.common.core.utils.file.MimeTypeUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.system.api.RemoteFileService;
import com.ruoyi.system.api.domain.SysFile;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.awt.geom.QuadCurve2D;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@RestController
@RequestMapping("/pro")
public class BasciProductContrller  extends BaseController {
    @Resource
    private BasicProductService basicProductService;

    @Resource
    private RemoteFileService remoteFileService;

    //添加产品信息
    @Log(title = "添加产品信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasicProduct basicProduct)
    {
        return toAjax(basicProductService.addBasicProduct(basicProduct));
    }

    //修改产品信息
    @Log(title = "修改产品信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasicProduct basicProduct)
    {
        return toAjax(basicProductService.updateBasicProduct(basicProduct));
    }

    //删除产品
    @Log(title = "删除产品", businessType = BusinessType.UPDATE)
    @PutMapping("/dp")
    public AjaxResult edit1(@RequestParam("product_id") Long product_id)
    {
        return toAjax(basicProductService.delBasicProduct(product_id));
    }

    //查询所有产品信息
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody BasicProduct basicProduct) {
        Map<String, Object> params=basicProduct.getParams();
        if(params!=null && params.get("pageNum") !=null && params.get("pageSize")!=null){
            Integer pageNum = Integer.parseInt(params.get("pageNum").toString());
            Integer pageSize = Integer.parseInt(params.get("pageSize").toString());
            PageHelper.startPage(pageNum, pageSize);
        }else {
            startPage();
        }
        List<BasicProduct> list =basicProductService.listBasicProductMapper(basicProduct);
        return getDataTable(list);
    }


    //上传产品图片
    @Log(title = "产品图片", businessType = BusinessType.UPDATE)
    @PostMapping("/avatar")
    public AjaxResult avatar(@RequestParam("avatarfile") MultipartFile file)
    {
        if (!file.isEmpty())
        {
            String extension = FileTypeUtils.getExtension(file);
            if (!StringUtils.equalsAnyIgnoreCase(extension, MimeTypeUtils.IMAGE_EXTENSION))
            {
                return error("文件格式不正确，请上传" + Arrays.toString(MimeTypeUtils.IMAGE_EXTENSION) + "格式");
            }
            R<SysFile> fileResult = remoteFileService.upload(file);
            if (StringUtils.isNull(fileResult) || StringUtils.isNull(fileResult.getData()))
            {
                return error("文件服务异常，请联系管理员");
            }
            BasicProduct basicProduct=new BasicProduct();
            basicProduct.setImg(fileResult.getData().getUrl());
            System.out.println("sfefef:"+basicProduct.getImg());
            AjaxResult ajax = AjaxResult.success();
            ajax.put("imgUrl", basicProduct.getImg());
            return ajax;
        }

        return error("上传图片异常，请联系管理员");
    }

    //批量删除产品
    @Log(title = "批量删除产品", businessType = BusinessType.UPDATE)
    @PutMapping("/batch/{product_ids}")
    public AjaxResult edit(@PathVariable List<Long> product_ids)
    {
        return toAjax(basicProductService.delBasicProductByIds(product_ids));
    }
}
