package com.cssl.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@TableName("basic_team")
public class BasicTeam extends BaseEntity {
    @TableId(value ="team_id",type = IdType.AUTO)
    private Long team_id;
    private String team_code;
    private String team_name;
    private Long workshop_id;
    private String workshop_name;
    private Long production_line_id;
    private String production_line_name;
    private String team_status;
    private String remarks;
    private String is_delete;
    private Integer team_num;
    private String team_type;
    private String create_by;
    private Date create_time;
    private String update_by;
    private Date update_time;
    @TableField(exist = false)
    private List<BasicTeamdetails> basicTeamdetails;
}
