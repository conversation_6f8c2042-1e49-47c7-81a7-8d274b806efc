<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cssl.mapper.BasicBomdetailsMapper">
    <resultMap id="selectBasicBomdetails" type="com.cssl.pojo.BasicBomdetails">
        <id column="bomdetails_id" property="bomdetails_id"/>
        <association property="basicWlgl" javaType="com.cssl.pojo.BasicWlgl">
            <id column="material_id" property="material_id"/>
        </association>
        <association property="basicProduct" javaType="com.cssl.pojo.BasicProduct">
            <id column="product_id" property="product_id"/>
        </association>
        <association property="basicBom" javaType="com.cssl.pojo.BasicBom">
            <id column="bom_id" property="bom_id"/>
        </association>
        <association property="basicOperational" javaType="com.cssl.pojo.BasicOperational">
            <id column="operational_id" property="operational_id"/>
        </association>
    </resultMap>
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO basic_bomdetails
        (is_delete, create_by, create_time, bom_id, material_id, operational_id)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.is_delete}, #{item.create_by}, #{item.create_time},
            #{item.bom_id}, #{item.material_id}, #{item.operational_id})
        </foreach>
    </insert>

    <update id="deleteBasicBomdetails">
        update basic_bomdetails
        set is_detele = 1
        where bom_id = #{bom_id}
    </update>
    <update id="deleteAllBasicBomdetails">
        update basic_bomdetails set is_detele = 1 where bomdetails_id in
        <foreach item="bomdetails_ids" collection="list" open="(" separator="," close=")">
            #{bomdetails_ids}
        </foreach>
    </update>
    <update id="updateBasicBomdetails">
        update basic_bomdetails
        <trim prefix="SET" suffixOverrides=",">
            <if test="material_id != null and material_id != ''">material_id=#{material_id},</if>
            <if test="operational_id != null and operational_id !='' ">operational_id=#{operational_id},</if>
            <if test="update_by != null and update_by !='' ">update_by=#{update_by},</if>
            <if test="update_time!= null ">update_time=#{update_time},</if>
        </trim>
        where bomdetails_id = #{bomdetails_id}
    </update>
    <update id="deleteBasicBomdetailsByIds">
        update basic_bomdetails set is_detele = '1' where bom_id in
        <foreach item="bom_ids" collection="list" open="(" separator="," close=")">
            #{bom_ids}
        </foreach>
    </update>
    <update id="updateBasicBomdetailsByBomId">
        update basic_bomdetails
        <trim prefix="SET" suffixOverrides=",">
            <if test="material_id != null and material_id != ''">material_id=#{material_id},</if>
            <if test="operational_id != null and operational_id !='' ">operational_id=#{operational_id},</if>
            <if test="update_by != null and update_by !='' ">update_by=#{update_by},</if>
            <if test="update_time!= null ">update_time=#{update_time},</if>
        </trim>
        where bom_id = #{bom_id}
    </update>
    <update id="delByoperational">
        UPDATE  basic_bomdetails
        SET operational_id = NULL
        <where>
            <!-- 检查集合是否不为空 -->
            <if test="list != null and list.size() > 0">
                bom_id in
                <foreach item="bom_ids" collection="list" open="(" separator="," close=")">
                    #{bom_ids}
                </foreach>
            </if>
        </where>
        <!-- 若集合为空，可添加额外逻辑，这里简单返回 0 表示未更新任何记录 -->
        <if test="list == null or list.size() == 0">
            WHERE 1 = 0
        </if>
    </update>


    <select id="listBasicBomdetails" resultType="com.cssl.pojo.BasicBomdetails">
        SELECT * from basic_bomdetails
        <where>
            is_detele = 0
            <if test="bom_id != null">
                AND bom_id = #{bom_id}
            </if>
        </where>
    </select>
    <select id="selectBasicBomdetailsByMaterialId" resultType="com.cssl.pojo.BasicBomdetails">
        SELECT
            bm.bomdetails_id,
            bm.bom_id,
            bm.material_id,
            bm.operational_id,
            bm.position,
            bm.material_usage as quantity,
            bm.material_Ingredient_ratio,
            wl.material_code,
            wl.material_name,
            wl.material_sfn as specification,
            wl.material_unit as unit,
            wl.remarks as remark
        FROM
            basic_bomdetails bm
                INNER JOIN
            basic_wl wl ON bm.material_id = wl.material_id
        <where>
            bm.is_detele = 0 AND wl.is_detele = 0
            <if test="material_ids != null and !material_ids.isEmpty()">
                AND bm.material_id IN
                <foreach item="id" collection="material_ids" open="(" separator="," close=")">
                    #{id}
                </foreach>
        </if>
        <if test="bom_id !=null and bom_id !=''">
            and bm.bom_id=#{bom_id}
        </if>
        </where>
    </select>
    <select id="selectBasicBomdetailsVoByBomId" resultType="com.cssl.pojo.vo.BasicBomdetailsVo">
        SELECT * from basic_bomdetails bm inner join basic_bom bb on bm.bom_id=bb.bom_id inner join basic_wl bw on bm.material_id=bw.material_id inner join basic_operational bo on bm.operational_id=bo.operational_id WHERE bm.is_detele=0 and bb.is_delete=0 and bw.is_detele=0 and bo.is_delete=0
        <if test="bom_id !=null and bom_id !=''">
            and bb.bom_id=#{bom_id}
        </if>
    </select>
    <select id="selectBasicBomdetailsVoByBomIdAndIsDelete" resultType="com.cssl.pojo.BasicBomdetails">
        SELECT
        bm.bomdetails_id,
        bm.bom_id,
        bm.material_id,
        bm.operational_id,
        bm.position,
        bm.material_usage as quantity,
        bm.material_Ingredient_ratio,
        wl.material_code,
        wl.material_name,
        wl.material_sfn as specification,
        wl.material_unit as unit,
        wl.remarks as remark
        FROM
        basic_bomdetails bm
        INNER JOIN
        basic_wl wl ON bm.material_id = wl.material_id
        <where>
            bm.is_detele = 0 AND wl.is_detele = 0
            <if test="bom_id != null ">
                AND bm.bom_id=#{bom_id}

            </if>
        </where>

    </select>
    <select id="selectBasicBomdetailsVoByOperationalId" resultType="com.cssl.pojo.BasicBomdetails">
        SELECT bm.bomdetails_id,
               bm.bom_id,
               bm.material_id,
               bm.operational_id,
               bm.position,
               bm.material_usage as quantity,
               bm.material_Ingredient_ratio,
               bo.operational_code,
               bo.operational_name,
               bo.operational_status,
               bo.operational_description
        from basic_bomdetails bm inner join basic_operational bo on bm.operational_id=bo.operational_id where bm.is_detele=0 and bo.is_delete=0
        <if test="operational_ids != null and !operational_ids.isEmpty()">
            AND bm.operational_id IN
            <foreach item="id" collection="operational_ids" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="bom_id !=null and bom_id !=''">
            and bm.bom_id=#{bom_id}
        </if>

    </select>
    <select id="selectBasicBomdetailsVoByOperationalIdIsNull" resultType="com.cssl.pojo.vo.BasicBomdetailsVo">
        SELECT bm.bomdetails_id,bm.bom_id,bp.product_id,bp.product_code,bp.product_name,bp.product_sfn,bp.product_unit,bb.bom_version,bb.bom_code from basic_bomdetails bm inner join basic_bom bb on bm.bom_id=bb.bom_id inner join basic_products bp on bp.product_id=bb.product_id WHERE bm.is_detele=0 and bb.is_delete=0 and bp.is_delete=0 and bm.operational_id is NULL

    </select>
    <select id="selectBasicBomdetailsVoByBom" resultType="com.cssl.pojo.BasicBomdetails">
        SELECT * from basic_bomdetails where is_detele=0
    </select>
</mapper>