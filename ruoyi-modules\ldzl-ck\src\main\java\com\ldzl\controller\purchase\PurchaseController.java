package com.ldzl.controller.purchase;


import com.github.pagehelper.PageHelper;
import com.ldzl.dto.AddPurchaseOrderDTO;
import com.ldzl.pojo.CkPurchaseOrder;
import com.ldzl.pojo.CkPurchaseOrderLine;
import com.ldzl.pojo.ScPurchaseRequisition;
import com.ldzl.service.CkPurchaseOrderLineService;
import com.ldzl.service.CkPurchaseOrderService;
import com.ldzl.service.ScPurchaseRequisitionService;
import com.ruoyi.common.core.domain.UR;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.system.api.BasicService;
import com.ruoyi.system.api.domain.BasicProduct;
import com.ruoyi.system.api.domain.BasicSuppliers;
import com.ruoyi.system.api.domain.BasicWlgl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

import static com.fasterxml.jackson.databind.type.LogicalType.Map;

/**
 * 采购管理
 */
@RestController
@RequestMapping("/purchase")
public class PurchaseController extends BaseController {

    @Autowired
    private CkPurchaseOrderService pos; //采购单

    @Autowired
    private CkPurchaseOrderLineService pols; //采购单行

    @Autowired
    private BasicService bs; // 基础数据 外部服务

    @Autowired
    private ScPurchaseRequisitionService prs; //生产采购申请

    /**
     * 查询采购单
     * @param po
     * @return
     */
    @PostMapping("/findAll_po")
    public TableDataInfo findAll_po(CkPurchaseOrder po) {
        startPage();
        return getDataTable(pos.findOrder(po));
    }

    /**
     * 查询采购单详情 未到货
     * @param po
     * @return
     */
    @PostMapping("/findAll_po_line")
    public TableDataInfo findAll_po_line(CkPurchaseOrder po) {
        startPage();
        return getDataTable(pols.selectArrivalNotice(po));
    }

    /**
     * 查询物料信息
     * @param bw
     * @return
     */
    @PostMapping("/findAll_wlgl")
    public TableDataInfo findAll_wlgl(@RequestBody BasicWlgl bw) {
        System.out.println("获取物料编号："+bw.getMaterial_code());
        Map<String,Object> params = bw.getParams();
        if (params != null && params.get("pageNum")!= null && params.get("pageSize")!= null) {
            Integer pageNum = Integer.parseInt(params.get("pageNum").toString());
            Integer pageSize = Integer.parseInt(params.get("pageSize").toString());
            PageHelper.startPage(pageNum, pageSize);
        }else {
            startPage();
        }
        //startPage();
        return bs.list(bw);
    }

    /**
     * 查询产品信息
     * @param basicProduct
     * @return
     */
    @PostMapping("/findAll_pro")
    public TableDataInfo findAll_pro(@RequestBody BasicProduct basicProduct){
        System.out.println("获取产品编号："+basicProduct.getProduct_code());
        Map<String,Object> params = basicProduct.getParams();
        if (params != null && params.get("pageNum")!= null && params.get("pageSize")!= null) {
            Integer pageNum = Integer.parseInt(params.get("pageNum").toString());
            Integer pageSize = Integer.parseInt(params.get("pageSize").toString());
            PageHelper.startPage(pageNum, pageSize);
        }else {
            startPage();
        }
        return bs.list(basicProduct);
    }

    /**
     * 查询产品信息 ***
     * @param basicProduct
     * @return
     */
    @PostMapping("/findAll_produce")
    public TableDataInfo findAll_produce(@RequestBody BasicProduct basicProduct){
        return null;
    }

    /**
     * 查询生产采购信息
     * @param po
     * @return
     */
    @PostMapping("/findPurchase")
    public TableDataInfo findPurchase(ScPurchaseRequisition po){
        startPage();
        return getDataTable(prs.findPurchase(po));
    }

    /**
     * 查询供应商信息
     * @param basicSuppliers
     * @return
     */
    @PostMapping("/findSupplier")
    public TableDataInfo findSupplier(@RequestBody BasicSuppliers basicSuppliers){
        Map<String,Object> params = basicSuppliers.getParams();
        if (params != null && params.get("pageNum")!= null && params.get("pageSize")!= null) {
            Integer pageNum = Integer.parseInt(params.get("pageNum").toString());
            Integer pageSize = Integer.parseInt(params.get("pageSize").toString());
            PageHelper.startPage(pageNum, pageSize);
        }else {
            startPage();
        }
        return bs.list(basicSuppliers);
    }


    /**
     * 新增采购订单
     * @param orderDTO
     * @return
     */
    @PostMapping("/addPurchaseOrder")
    public UR addPurchaseOrder(@RequestBody AddPurchaseOrderDTO orderDTO){
        System.out.println("获取的采购订单："+ orderDTO.getOrder());
        System.out.println("获取采购商品列表："+ orderDTO.getListOrderLine());

        if(pos.addOrder(orderDTO))
            return UR.ok("添加成功");
        else
            return UR.fail("添加失败");
    }

    /**
     * 根据id 查询采购订单详情
     * @param po_id
     * @return
     */
    @GetMapping("/selectOrderLine_id/{po_id}")
    public TableDataInfo selectOrderLine_id(@PathVariable("po_id") Long po_id){
        System.out.println("*****获取的采购订单id："+ po_id);
        return getDataTable(pols.selectOrderLine_id(po_id));
    }

    /**
     * 删除采购订单
     * @param po_id
     * @return
     */
    @PostMapping("/delete_po/{po_id}")
    public UR delete_po (@PathVariable("po_id") Long po_id){
        System.out.println("*****获取的采购订单id："+ po_id);
        if(pos.updateIs_delete(po_id))
            return UR.ok("删除成功");
        else
            return UR.fail("删除失败");
    }

    /**
     * 批量删除采购订单
     * @param list_po_id
     * @return
     */
    @PostMapping("/batch_delete_po")
    public UR batch_delete_po (@RequestBody List<Long> list_po_id){
        System.out.println("*****获取的采购订单id："+ list_po_id);
        if(pos.updateIs_delete_batch(list_po_id))
            return UR.ok("删除成功");
        else
            return UR.fail("删除失败");
    }

    /**
     * 提交采购订单
     * @param po_id
     * @return
     */
    @PostMapping("/updateStatus/{po_id}")
    public UR updateStatus(@PathVariable("po_id") Long po_id){
        if(pos.updateStatus(po_id))
            return UR.ok("提交成功");
        else
            return UR.fail("提交失败");
    }
}
