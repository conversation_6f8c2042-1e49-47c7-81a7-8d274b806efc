package com.cssl.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cssl.mapper.BasicBomMapper;
import com.cssl.mapper.BasicBomdetailsMapper;
import com.cssl.pojo.BasicBom;
import com.cssl.pojo.BasicBomdetails;
import com.cssl.pojo.vo.BasicBomdetailsVo;
import com.cssl.service.BasicBomService;
import com.cssl.service.BasicBomdetailsService;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service
public class BasicBomdetailsServiceImpl extends ServiceImpl<BasicBomdetailsMapper, BasicBomdetails> implements BasicBomdetailsService {
    @Resource
    private BasicBomdetailsMapper basicBomdetailsMapper;

    @Override
    public List<BasicBomdetails> listBasicBomdetails(BasicBomdetails basicBomdetails) {

        return basicBomdetailsMapper.listBasicBomdetails(basicBomdetails);
    }

    @Override
    public int deleteBasicBomdetails(Long bom_id) {

        return basicBomdetailsMapper.deleteBasicBomdetails(bom_id);
    }

    @Override
    public List<BasicBomdetails> selectBasicBomdetailsByMaterialId(Map<String, Object> params ) {
        return basicBomdetailsMapper.selectBasicBomdetailsByMaterialId(params);
    }

    @Override
    public int updateBasicBomdetails(BasicBomdetails basicBomdetails) {

        return basicBomdetailsMapper.updateBasicBomdetails(basicBomdetails);
    }

    @Override
    public int deleteAllBasicBomdetails(List<Long> bomdetails_ids) {
        return basicBomdetailsMapper.deleteAllBasicBomdetails(bomdetails_ids);
    }



    @Override
    public List<BasicBomdetailsVo> selectBasicBomdetailsVoByBomId(Long bom_id) {
        return basicBomdetailsMapper.selectBasicBomdetailsVoByBomId(bom_id);
    }

    @Override
    public List<BasicBomdetails> selectBasicBomdetailsVoByBomIdAndIsDelete(Long bom_id) {
        return basicBomdetailsMapper.selectBasicBomdetailsVoByBomIdAndIsDelete(bom_id);
    }

    @Override
    public int deleteBasicBomdetailsByIds(List<Long> bom_ids) {
        return basicBomdetailsMapper.deleteBasicBomdetailsByIds(bom_ids);
    }

    @Override
    public List<BasicBomdetails> selectBasicBomdetailsVoByOperationalId(Map<String, Object> params) {
        return basicBomdetailsMapper.selectBasicBomdetailsVoByOperationalId(params);
    }

    @Override
    public int updateBasicBomdetailsByBomId(BasicBomdetails basicBomdetails) {
        return basicBomdetailsMapper.updateBasicBomdetailsByBomId(basicBomdetails);
    }

    @Override
    public List<BasicBomdetailsVo> selectBasicBomdetailsVoByOperationalIdIsNull(BasicBomdetailsVo basicBomdetailsVo) {
        List<BasicBomdetailsVo> list = basicBomdetailsMapper.selectBasicBomdetailsVoByOperationalIdIsNull(basicBomdetailsVo);
        System.out.println("dehfh："+list);
        // 使用 LinkedHashMap 去重，保留第一条
        Map<String, BasicBomdetailsVo> map = new LinkedHashMap<>();
        for (BasicBomdetailsVo vo : list) {
            String key = vo.getProduct_code(); // 根据 productId 去重
            if (!map.containsKey(key)) {
                map.put(key, vo);
            }
        }

        List<BasicBomdetailsVo> result = new ArrayList<>(map.values());
            return result;
    }

    @Override
    public List<BasicBomdetails> selectBasicBomdetailsVoByBom() {
        return basicBomdetailsMapper.selectBasicBomdetailsVoByBom();
    }

    @Override
    public int delByoperational(List<Long> bom_ids) {
        return basicBomdetailsMapper.delByoperational(bom_ids);
    }


}
