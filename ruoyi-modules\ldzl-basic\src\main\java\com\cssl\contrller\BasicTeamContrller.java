package com.cssl.contrller;

import com.cssl.pojo.BasicCustomers;
import com.cssl.pojo.BasicSuppliers;
import com.cssl.pojo.BasicTeam;
import com.cssl.pojo.vo.BasicBomdetailsVo;
import com.cssl.pojo.vo.BasicTeamVo;
import com.cssl.service.BasicTeamService;
import com.cssl.service.BasicTeamdetailsService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/team")
public class BasicTeamContrller extends BaseController {
    @Resource
    private BasicTeamService basicTeamService;
    @Resource
    private BasicTeamdetailsService basicTeamdetailsService;

    //查询所有班组信息
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody BasicTeam basicTeam) {
        startPage();
        List<BasicTeam> list = basicTeamService.listBasicTeam(basicTeam);
        return getDataTable(list);
    }
    //添加班组信息
    @Log(title = "添加班组信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasicTeam basicTeam)
    {

        return toAjax(basicTeamService.insertBasicTeam(basicTeam));
    }

    //查看班组详情信息
    @PostMapping("/detail/{team_id}")
    public TableDataInfo detail(@PathVariable Long team_id)
    {
        List<BasicTeamVo> list =basicTeamdetailsService.selectTeamdetailsById(team_id);
        return getDataTable(list);
    }

    //修改班组信息
    @Log(title = "修改班组信息", businessType = BusinessType.UPDATE)
   @PutMapping
    public AjaxResult update(@RequestBody BasicTeam basicTeam)
    {
        return toAjax(basicTeamService.updateBasicTeam(basicTeam));
    }

    //删除班组
    @Log(title = "删除班组", businessType = BusinessType.UPDATE)
    @PutMapping("/dp/{team_id}")
    public AjaxResult edit1(@PathVariable Long team_id)
    {
        return toAjax(basicTeamService.delBasicTeam(team_id));
    }

    //批量删除
    @Log(title = "批量删除班组", businessType = BusinessType.UPDATE)
    @PutMapping("/batch/{team_ids}")
    public AjaxResult edit(@PathVariable List<Long> team_ids)
    {
        return toAjax(basicTeamService.delBatchBasicTeam(team_ids));
    }
}
