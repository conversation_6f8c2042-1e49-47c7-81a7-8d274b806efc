package com.cssl.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

@Data
@TableName("basic_station")
public class BasicStation extends BaseEntity {
    @TableId(value ="station_id",type = IdType.AUTO)
    private Long station_id; // 工位id
    private Long factory_id; // 工厂id
    private String factory_name; // 所属工厂
    private Long workshop_id; // 车间id
    private String workshop_name; // 所属车间
    private Long production_line_id; // 生产线id
    private String production_line_name; // 所属生产线
    private String station_code; // 工位编码
    private String station_name; // 工位名称
    private String remarks; // 备注
    private String is_delete; // 逻辑删除
    @TableField(exist = false)
    private Long process_id; // 工序id（从关联查询中获取）
    private String create_by;
    private Date create_time;
    private String update_by;
    private Date update_time;
    @TableField(exist = false)
    private BasicProductionline basicProductionline;
}
