package com.ldzl.mapper;

import com.ldzl.pojo.CkStorageArea;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ck_storage_area(库位表)】的数据库操作Mapper
* @createDate 2025-07-03 09:32:13
* @Entity com.ldzl.pojo.CkStorageArea
*/
public interface CkStorageAreaMapper extends BaseMapper<CkStorageArea> {

    /**
     * 查询指定库区下的所有库位
     * @param area
     * @return
     */
    List<CkStorageArea> selectArea(CkStorageArea area);
}




