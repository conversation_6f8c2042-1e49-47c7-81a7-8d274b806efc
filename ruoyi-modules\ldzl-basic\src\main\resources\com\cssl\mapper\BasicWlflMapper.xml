<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cssl.mapper.BasicWlflMapper">

    <insert id="insertBasicWlfl" parameterType="com.cssl.pojo.BasicWlfl" useGeneratedKeys="true" keyProperty="material_classification_id">
        insert into basic_wlfl
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="material_classification_code != null and material_classification_code !='' ">material_classification_code,</if>
            <if test="material_classification_name != null and material_classification_name != ''">material_classification_name,</if>
            <if test="create_by != null and create_by != ''">create_by,</if>
            <if test="create_time != null">create_time,</if>
            <if test="is_delete != null">is_delete,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="material_classification_code != null and material_classification_code !='' ">#{material_classification_code},</if>
            <if test="material_classification_name != null and material_classification_name != ''">#{material_classification_name},</if>
            <if test="create_by != null and create_by != ''">#{create_by},</if>
            <if test="create_time != null">#{create_time},</if>
            <if test="is_delete != null">#{is_delete},</if>
        </trim>
    </insert>
    <update id="updateBasicWlflz">
        update basic_wlfl
        <trim prefix="SET" suffixOverrides=",">
            <if test="material_classification_name != null and material_classification_name != ''">material_classification_name = #{material_classification_name},</if>
        </trim>
        where material_classification_id = #{material_classification_id}
    </update>

    <update id="delByBasicWlflzInt">
        update basic_wlfl
        <trim prefix="SET" suffixOverrides=",">
            <if test="is_delete != null ">is_delete =1,</if>
        </trim>
        where material_classification_id = #{material_classification_id}
    </update>

    <select id="findBasicWlflz" resultType="com.cssl.pojo.BasicWlfl">
        SELECT * from basic_wlfl WHERE is_delete=0
    </select>





    <!-- 主表查询 -->
    <select id="selectBasicWlflWithChildren" resultMap="BasicWlflResultMap">
        SELECT
            a.material_classification_id AS a_material_classification_id,
            a.material_classification_code AS a_material_classification_code,
            a.material_classification_name AS a_material_classification_name,
            a.create_by AS a_create_by,
            a.create_time AS a_create_time,
            a.update_by AS a_update_by,
            a.update_time AS a_update_time,
            a.is_delete AS a_is_delete,

            b.material_subcategory_id AS b_material_subcategory_id,
            b.material_subcategory_code AS b_material_subcategory_code,
            b.material_subcategory_name AS b_material_subcategory_name,
            b.create_by AS b_create_by,
            b.create_time AS b_create_time,
            b.update_by AS b_update_by,
            b.update_time AS b_update_time,
            b.is_delete AS b_is_delete
        FROM basic_wlfl a
                 LEFT JOIN basic_wlflz b ON a.material_classification_id = b.material_classification_id AND b.is_delete = '0'
        WHERE a.is_delete = '0'
    </select>


    <resultMap id="BasicWlflResultMap"
               type="com.cssl.pojo.BasicWlfl" autoMapping="true">
        <id property="material_classification_id" column="a_material_classification_id"/>
        <result property="material_classification_code" column="a_material_classification_code"/>
        <result property="material_classification_name" column="a_material_classification_name"/>
        <result property="create_by" column="a_create_by"/>
        <result property="create_time" column="a_create_time"/>
        <result property="update_by" column="a_update_by"/>
        <result property="update_time" column="a_update_time"/>
        <result property="is_delete" column="a_is_delete"/>

        <collection property="basicWlflz"
                    ofType="com.cssl.pojo.BasicWlflz"
                    resultMap="WlflzMap"/>
    </resultMap>

    <resultMap id="WlflzMap"
               type="com.cssl.pojo.BasicWlflz" autoMapping="true">
        <id property="material_subcategory_id" column="b_material_subcategory_id"/>
        <result property="material_subcategory_code" column="b_material_subcategory_code"/>
        <result property="material_subcategory_name" column="b_material_subcategory_name"/>
        <result property="create_by" column="b_create_by"/>
        <result property="create_time" column="b_create_time"/>
        <result property="update_by" column="b_update_by"/>
        <result property="update_time" column="b_update_time"/>
        <result property="is_delete" column="b_is_delete"/>
    </resultMap>

</mapper>