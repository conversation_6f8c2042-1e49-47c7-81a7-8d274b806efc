package com.ldzl.mapper;

import com.ldzl.pojo.CkPurchaseOrder;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ck_purchase_order(采购订单表)】的数据库操作Mapper
* @createDate 2025-07-11 11:17:44
* @Entity com.ldzl.pojo.CkPurchaseOrder
*/
public interface CkPurchaseOrderMapper extends BaseMapper<CkPurchaseOrder> {

    /**
     * 查询采购订单中待到货的
     * @param po
     * @return
     */
    List<CkPurchaseOrder> selectOrder(CkPurchaseOrder po);

    /**
     * 移除采购订单
     * @param po_id
     * @return
     */
    int updateIs_delete(@Param("po_id") Long po_id);

    /**
     * 查询采购订单 中已到货和采购中的
     * @param po
     * @return
     */
    List<CkPurchaseOrder> selectOrder_arrived(CkPurchaseOrder po);
}




