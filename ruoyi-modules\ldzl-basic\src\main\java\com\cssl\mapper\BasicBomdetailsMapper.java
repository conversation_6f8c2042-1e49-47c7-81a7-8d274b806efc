package com.cssl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cssl.pojo.BasicBomdetails;
import com.cssl.pojo.vo.BasicBomdetailsVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface BasicBomdetailsMapper extends BaseMapper<BasicBomdetails> {
    //查看BOM中物料配料信息
    public List<BasicBomdetails> listBasicBomdetails(BasicBomdetails basicBomdetails);

    //删除BOM明细表信息
    public int deleteBasicBomdetails(Long bom_id);

    //根据物料id或产品id查询BOM物料信息
    public List<BasicBomdetails> selectBasicBomdetailsByMaterialId(Map<String, Object> params);

    //修改明细信息
    public int updateBasicBomdetails(BasicBomdetails basicBomdetails);

    //删除所有明细表信息
    public int deleteAllBasicBomdetails(List<Long> bomdetails_ids);



    //根据BOMid查询所有明细信息
    public List<BasicBomdetailsVo> selectBasicBomdetailsVoByBomId(Long bom_id);

    //根据BOMid查询所有明细信息
    public List<BasicBomdetails> selectBasicBomdetailsVoByBomIdAndIsDelete(@Param("bom_id") Long bom_id);

    //批量删除
    public int deleteBasicBomdetailsByIds(List<Long> bom_ids);

    //根据工艺路线id查询BOM工艺路线信息
    public List<BasicBomdetails> selectBasicBomdetailsVoByOperationalId(Map<String, Object> params);

    //根据bomid修改明细信息
    public int updateBasicBomdetailsByBomId(BasicBomdetails basicBomdetails);

    //查询工艺路线id为空的产品
    public List<BasicBomdetailsVo> selectBasicBomdetailsVoByOperationalIdIsNull(BasicBomdetailsVo basicBomdetailsVo);

    //查询明细信息
    public List<BasicBomdetails> selectBasicBomdetailsVoByBom();

    //根据bomid删除工艺路线id
    public int delByoperational(List<Long> bom_ids);





}
