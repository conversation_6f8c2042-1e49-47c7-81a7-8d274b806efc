package com.cssl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cssl.mapper.BasicFactoryMapper;
import com.cssl.pojo.BasicCustomers;
import com.cssl.pojo.BasicFactory;

import java.util.List;

public interface BasicFactoryService extends IService<BasicFactory> {

    //查看所有工厂信息
    public List<BasicFactory> listBasicFactory(BasicFactory basicFactory);

    //添加工厂信息
    public int addBasicFactory(BasicFactory basicFactory);

    //修改工厂信息
    public int updateFactory(BasicFactory basicFactory);

    //删除工厂信息
    public int delFactory(Long factory_id);
}
