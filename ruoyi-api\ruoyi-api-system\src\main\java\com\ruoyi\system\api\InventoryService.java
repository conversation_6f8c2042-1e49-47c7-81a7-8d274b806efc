package com.ruoyi.system.api;

import com.ruoyi.common.core.domain.UR;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.system.api.domain.CkBatch;
import com.ruoyi.system.api.factory.InventoryFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 仓库库存服务的对外api接口
 */
@FeignClient(name="ldzl-ck", fallbackFactory = InventoryFallbackFactory.class)
public interface InventoryService {

    /**
     * 根据产品编号查看对应库存 和库存上限
     * @param code
     * @return
     */
    @GetMapping("inventory/viewInventory/{code}")
    public UR viewInventory(@PathVariable("code") String code);

    /**
     * 根据产品编号查看对应库存 和库存上限 批量
     * @param listCode
     * @return
     */
    @PostMapping("inventory/viewInventoryList")
    public UR viewInventoryList(@RequestParam("listCode") List<String> listCode);

    /**
     * 修改库存
     * @param item_code
     * @param quantity_num
     * @return
     */
    @PostMapping("inventory/modifyInventory")
    public UR modifyInventory(@RequestParam("item_code") String item_code,@RequestParam("quantity_num") BigDecimal quantity_num);

    /**
     * 修改库存 批量
     * @param map
     * @return
     */
    @PostMapping("inventory/modifyInventoryBatch")
    public UR modifyInventoryBatch(@RequestBody Map<String, BigDecimal> map);

    /**
     * 新增批次
     * @param batch
     * @return
     */
    @PostMapping("receipt/addBatch")
    public UR addBatch(CkBatch batch);


    /**
     * 查询待质检的入库单
     * @param recpt_coed
     * @param recpt_name
     * @return
     */
    @PostMapping("receipt/selectCkProductRecptDetectSingle/{recpt_code}/{recpt_name}")
    public TableDataInfo selectCkProductRecptDetectSingle(@PathVariable("recpt_code") String recpt_coed,
                                                          @PathVariable("recpt_name") String recpt_name);


    /**
     * 修改入库单状态 合格或不合格
     * @param recpt_id
     * @param status
     * @return
     */
    @GetMapping("receipt/updateCkProductRecptStatusStatus/{recpt_id}/{status}")
    public UR updateCkProductRecptStatusStatus(@PathVariable("recpt_id") Long recpt_id,
                                               @PathVariable("status") Long status);


    /**
     * 查询指定采购入库单下的商品
     * @param recpt_id
     * @return
     */
    @PostMapping("receipt/findReceipt_id/{recpt_id}")
    public TableDataInfo findReceipt_id(@PathVariable("recpt_id") Long recpt_id);


    /**
     * 查询待质检的出库单
     * @param sales_code
     * @param sales_name
     * @return
     */
    @GetMapping("/selectCkProductSales/{sales_code}/{sales_name}")
    public TableDataInfo selectCkProductSales(@PathVariable("sales_code") String sales_code,
                                              @PathVariable("sales_name") String sales_name);

    /**
     * 查询指定出库单下的商品详情
     * @param sales_id
     * @return
     */
    @GetMapping("/ckProductSalesLine/{sales_id}")
    public TableDataInfo selectCkProductSalesLine(@PathVariable("sales_id") Long sales_id);

    /**
     * 修改出库单状态
     * @param sales_id
     * @param status
     * @return
     */
    @GetMapping("/ckProductSalesStatus/{sales_id}/{status}")
    public UR updateCkProductSalesStatusStatus(@PathVariable("sales_id") Long sales_id,
                                               @PathVariable("status") String status);

}
