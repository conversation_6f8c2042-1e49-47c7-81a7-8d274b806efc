package com.cssl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cssl.pojo.BasicSuppliers;

import java.util.List;

public interface BasicSuppliersMapper extends BaseMapper<BasicSuppliers> {
    //查询所有供应商信息
    public List<BasicSuppliers> listBasicSuppliers(BasicSuppliers basicSuppliers);

    //添加供应商信息
    public int addBasicSuppliers(BasicSuppliers basicSuppliers);

    //修改供应商信息
    public int updateBasicSuppliers(BasicSuppliers basicSuppliers);

    //删除供应商信息
    public int delBasicSuppliers(Long supplier_id);

    //批量删除供应商信息
    public int delBatchBasicSuppliers(List<Long> supplier_ids);
}
