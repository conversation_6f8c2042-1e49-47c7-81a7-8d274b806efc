package com.cssl.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

@Data
@TableName("basic_productionline")
public class BasicProductionline extends BaseEntity {
    @TableId(value ="production_line_id",type = IdType.AUTO)
    private Long production_line_id; // 生产线id
    private Integer factory_id; // 工厂id
    private String factory_name; // 所属工厂
    private Integer workshop_id; // 车间id
    private String workshop_name; // 所属车间
    private String production_line_code; // 生产线编号
    private String production_line_name; // 生产线名称
    private String remarks; // 备注
    private String is_delete; // 逻辑删除
    private String create_by;
    private Date create_time;
    private String update_by;
    private Date update_time;
    @TableField(exist = false)
    private BasicWorkshop  basicWorkshop;
}
