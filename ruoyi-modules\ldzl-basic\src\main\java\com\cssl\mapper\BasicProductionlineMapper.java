package com.cssl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cssl.pojo.BasicProductionline;

import java.util.List;

public interface BasicProductionlineMapper extends BaseMapper<BasicProductionline> {
    //查询生产线信息
    public List<BasicProductionline> listBasicProductionline(BasicProductionline basicProductionline);

    //添加生产线信息
    public int addBasicProductionline(BasicProductionline basicProductionline);

    //修改生产线信息
    public int updateBasicProductionline(BasicProductionline basicProductionline);

    //删除生产线信息
    public int delBasicProductionline(Long production_line_id);
}
