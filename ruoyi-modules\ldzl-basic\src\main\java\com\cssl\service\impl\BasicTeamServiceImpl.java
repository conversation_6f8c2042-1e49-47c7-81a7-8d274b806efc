package com.cssl.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cssl.mapper.BasicSuppliersMapper;
import com.cssl.mapper.BasicTeamMapper;
import com.cssl.pojo.BasicProcessdetails;
import com.cssl.pojo.BasicSuppliers;
import com.cssl.pojo.BasicTeam;
import com.cssl.pojo.BasicTeamdetails;
import com.cssl.service.BasicSuppliersService;
import com.cssl.service.BasicTeamService;
import com.cssl.service.BasicTeamdetailsService;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class BasicTeamServiceImpl extends ServiceImpl<BasicTeamMapper, BasicTeam> implements BasicTeamService {
    @Resource
    private BasicTeamdetailsService basicTeamdetailsService;
    @Resource
    private BasicTeamMapper basicTeamMapper;
    @Override
    public List<BasicTeam> listBasicTeam(BasicTeam basicTeam) {
        return basicTeamMapper.listBasicTeam(basicTeam);
    }

    @Override
    public int insertBasicTeam(BasicTeam basicTeam) {
        basicTeam.setCreate_by(SecurityUtils.getUsername());
        basicTeam.setCreate_time(new Date());
        basicTeam.setIs_delete("0");
        int num=basicTeamMapper.insertBasicTeam(basicTeam);
        if(num>0){
            List<BasicTeamdetails> basicTeamdetails=basicTeam.getBasicTeamdetails();
            System.out.println("list:"+basicTeamdetails);
            if(basicTeamdetails!=null && !basicTeamdetails.isEmpty()){
                for(BasicTeamdetails basicTeamdetail:basicTeamdetails){
                    basicTeamdetail.setTeam_id(basicTeam.getTeam_id());
                    basicTeamdetail.setCreate_by(SecurityUtils.getUsername());
                    basicTeamdetail.setCreate_time(new Date());
                   basicTeamdetail.setIs_delete("0");
                    basicTeamdetailsService.save(basicTeamdetail);
                }
            }

        }
        return num;
    }

    @Override
    public int updateBasicTeam(BasicTeam basicTeam) {
        basicTeam.setUpdate_by(SecurityUtils.getUsername());
        basicTeam.setUpdate_time(new Date());
        int num=basicTeamMapper.updateBasicTeam(basicTeam);
        if(num>0){
            List<BasicTeamdetails> list=basicTeam.getBasicTeamdetails();
            if (list != null && !list.isEmpty()) {
                // 查询已有ID
                List<Long> existingIds =basicTeamdetailsService.selectTeamdetailsById1(basicTeam.getTeam_id())
                        .stream()
                        .map(BasicTeamdetails::getTeamdetails_id)
                        .collect(Collectors.toList());

                List<BasicTeamdetails> insertList = new ArrayList<>();
                List<BasicTeamdetails> updateList = new ArrayList<>();

                for (BasicTeamdetails detail : list) {
                    detail.setTeam_id(basicTeam.getTeam_id());
                    if (detail.getTeamdetails_id() != null) {
                        // 更新
                        detail.setUpdate_by(SecurityUtils.getUsername());
                        detail.setUpdate_time(new Date());
                        updateList.add(detail);
                    } else {
                        // 新增
                        detail.setCreate_by(SecurityUtils.getUsername());
                        detail.setCreate_time(new Date());
                        detail.setIs_delete("0");
                        insertList.add(detail);
                    }
                }

                // 批量操作
                if (!insertList.isEmpty()) {
                    basicTeamdetailsService.saveBatch(insertList);
                }
                if (!updateList.isEmpty()) {
                    basicTeamdetailsService.updateBatchById(updateList);
                }

                // 删除未提交的ID
                Set<Long> newIds = list.stream()
                        .map(BasicTeamdetails::getTeamdetails_id)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());

                List<Long> toDeleteIds = existingIds.stream()
                        .filter(id -> !newIds.contains(id))
                        .collect(Collectors.toList());

                if (!toDeleteIds.isEmpty()) {
                    basicTeamdetailsService.delBatchTeamdetails(toDeleteIds);
                }
            } else {
               basicTeamdetailsService.delTeamdetailsById(basicTeam.getTeam_id());
            }
        }
        return num;
    }

    @Override
    @Transactional
    public int delBasicTeam(Long team_id) {
        basicTeamdetailsService.delTeamdetailsById(team_id);
        return basicTeamMapper.delBasicTeam(team_id);
    }

    @Override
    public int delBatchBasicTeam(List<Long> team_ids) {
       basicTeamdetailsService.delBatchTeamdetails1(team_ids);
        return basicTeamMapper.delBatchBasicTeam(team_ids);
    }
}
