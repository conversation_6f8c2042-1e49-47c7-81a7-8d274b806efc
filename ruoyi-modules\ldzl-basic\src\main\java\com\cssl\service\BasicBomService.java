package com.cssl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cssl.pojo.BasicBom;

import java.util.List;

public interface BasicBomService extends IService<BasicBom> {
    //查看BOM信息
    public List<BasicBom> listBasicBom(BasicBom basicBom);

    //添加BOM头部信息
    public int addBasicBom(BasicBom basicBom);

    //修改BOM头部信息
    public int updateBasicBom(BasicBom basicBom);

    //删除BOM头部信息
    public int deleteBasicBom(Long bom_id);

    //批量删除
    public int deleteBasicBomByIds(List<Long> bom_ids);

    //获取bom头部信息
    public List<BasicBom> getBasicBom(Long bom_id);

    //通过产品id获取bomid
    public List<BasicBom> getBomIdByProductId(List<Long> product_ids);
}
