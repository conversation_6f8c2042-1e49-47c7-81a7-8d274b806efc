package com.cssl.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cssl.mapper.BasicNumbersMapper;
import com.cssl.mapper.BasicWlflMapper;
import com.cssl.pojo.BasicNumbers;
import com.cssl.pojo.BasicWlfl;
import com.cssl.service.BasicNumbersService;
import com.cssl.service.BasicWlflService;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;

@Service
public class BasicWlflServiceImpl extends ServiceImpl<BasicWlflMapper,BasicWlfl> implements BasicWlflService {
    @Resource
    private BasicWlflMapper basicWlflMapper;



    @Override
    public int insertBasicWlfl(BasicWlfl basicWlfl) {
        basicWlfl.setCreate_time(DateUtils.getNowDate());
        basicWlfl.setIs_delete("0");
        basicWlfl.setCreate_by(SecurityUtils.getUsername());
        return basicWlflMapper.insertBasicWlfl(basicWlfl);
    }

    @Override
    public int updateBasicWlflz(BasicWlfl basicWlfl) {
        basicWlfl.setUpdate_time(new Date());
        basicWlfl.setUpdate_by(SecurityUtils.getUsername());
        return basicWlflMapper.updateBasicWlflz(basicWlfl);
    }

    @Override
    public int delByBasicWlflzInt(Long material_classification_id) {
        return basicWlflMapper.delByBasicWlflzInt(material_classification_id);
    }


    @Override
    public List<BasicWlfl> findBasicWlflz() {
        return basicWlflMapper.findBasicWlflz();
    }

    /**
     * 查询所有分类 一对多
     * @return
     */
    @Override
    public List<BasicWlfl> selectBasicWlflWithChildren() {
        return super.baseMapper.selectBasicWlflWithChildren();
    }


}
