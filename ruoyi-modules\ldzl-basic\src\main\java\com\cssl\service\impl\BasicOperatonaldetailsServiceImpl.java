package com.cssl.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cssl.mapper.BasicOperationalMapper;
import com.cssl.mapper.BasicOperationdetailsMapper;
import com.cssl.pojo.BasicOperational;
import com.cssl.pojo.BasicOperatonaldetails;
import com.cssl.pojo.vo.BasicOperationdetailsVo;
import com.cssl.service.BasicOperationalService;
import com.cssl.service.BasicOperatonaldetailsService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class BasicOperatonaldetailsServiceImpl extends ServiceImpl<BasicOperationdetailsMapper, BasicOperatonaldetails> implements BasicOperatonaldetailsService {
    @Resource
    private BasicOperationdetailsMapper basicOperationdetailsMapper;
    @Override
    public int deleteBasicOperationdetailsById(Long operational_id) {
        return basicOperationdetailsMapper.deleteBasicOperationdetailsById(operational_id);
    }

    @Override
    public int deleteBatchBasicOperationdetails(List<Long> operationalIds) {
        return basicOperationdetailsMapper.deleteBatchBasicOperationdetails(operationalIds);
    }

    @Override
    public List<BasicOperatonaldetails> getBasicOperationdetailsByOperationalId(Long operational_id) {
        return basicOperationdetailsMapper.getBasicOperationdetailsByOperationalId(operational_id);
    }

    @Override
    public List<BasicOperationdetailsVo> getBasicOperationdetailsByOperationalId2(Long operational_id) {
        return basicOperationdetailsMapper.getBasicOperationdetailsByOperationalId2(operational_id);
    }

    @Override
    public List<BasicOperatonaldetails> getBasicOperationdetailsByOperationalId3(Long operational_id) {
        return basicOperationdetailsMapper.getBasicOperationdetailsByOperationalId3(operational_id);
    }

    @Override
    public int delBatchBasicOperationdetails(List<Long> operationalIds) {
        return basicOperationdetailsMapper.delBatchBasicOperationdetails(operationalIds);
    }
}
