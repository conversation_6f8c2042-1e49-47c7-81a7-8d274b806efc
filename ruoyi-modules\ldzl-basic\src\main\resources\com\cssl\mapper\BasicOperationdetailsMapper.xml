<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cssl.mapper.BasicOperationdetailsMapper">
    <resultMap id="selectBasicOperationdetails" type="com.cssl.pojo.BasicOperatonaldetails">
        <id column="operational_id" property="operational_id"/>
        <association property="basicProduct" javaType="com.cssl.pojo.BasicProduct">
            <id column="product_id" property="product_id"/>
        </association>
        <association property="basicOperational" javaType="com.cssl.pojo.BasicOperational">
            <id column="operational_id" property="operational_id"/>
        </association>
        <association property="basicProcess" javaType="com.cssl.pojo.BasicProcess">
            <id column="process_id" property="process_id"/>
        </association>
    </resultMap>

    <update id="deleteBasicOperationdetailsById">
        update basic_operatonaldetails
        <trim prefix="SET" suffixOverrides=",">
            <if test="is_delete != null ">is_delete =1,</if>
        </trim>
        where operational_id = #{operational_id}
    </update>
    <update id="deleteBatchBasicOperationdetails">
        update basic_operatonaldetails set is_delete = '1' where operationaldetails_id in
        <foreach item="operationalIds" collection="list" open="(" separator="," close=")">
            #{operationalIds}
        </foreach>
    </update>
    <update id="delBatchBasicOperationdetails">
        update basic_operatonaldetails set is_delete = '1' where operational_id in
        <foreach item="operationalIds" collection="list" open="(" separator="," close=")">
            #{operationalIds}
        </foreach>
    </update>
    <select id="getBasicOperationdetailsByOperationalId" resultType="com.cssl.pojo.BasicOperatonaldetails">
        select bd.product_id from basic_operational bo inner join basic_operatonaldetails bd on bo.operational_id=bd.operational_id WHERE
            bo.is_delete=0 and bd.is_delete=0
        <if test="operational_id != null and operational_id !=''">
            and bo.operational_id=#{operational_id}
        </if>
    </select>
    <select id="getBasicOperationdetailsByOperationalId2" resultType="com.cssl.pojo.vo.BasicOperationdetailsVo">
        SELECT * from basic_operatonaldetails bo inner join basic_operational be on bo.operational_id=be.operational_id inner join basic_products bp on bp.product_id=bo.product_id inner join basic_process bc on
        bo.process_id=bc.process_id inner join basic_bom bb on bp.product_id=bb.product_id where bo.is_delete=0 and be.is_delete=0 and bp.is_delete=0 and bc.is_delete=0 and bb.is_delete=0
        <if test="operational_id != null and operational_id !=''">
            and bo.operational_id=#{operational_id}
        </if>
    </select>
    <select id="getBasicOperationdetailsByOperationalId3" resultType="com.cssl.pojo.BasicOperatonaldetails">
        SELECT * from basic_operatonaldetails bo inner join basic_operational be on bo.operational_id=be.operational_id inner join basic_products bp on bp.product_id=bo.product_id inner join basic_process bc on
        bo.process_id=bc.process_id inner join basic_bom bb on bp.product_id=bb.product_id where bo.is_delete=0 and be.is_delete=0 and bp.is_delete=0 and bc.is_delete=0 and bb.is_delete=0
        <if test="operational_id != null and operational_id !=''">
            and bo.operational_id=#{operational_id}
        </if>
    </select>
</mapper>



