package com.ldzl.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 接收添加的 采购订单详情
 */
@Data
public class ItemDetailDTO implements Serializable {

    /**
     * 订单详情ID
     */
    private Long line_id;

    /**
     * 采购详情订单ID
     */
    private Long order_line_id;

    /**
     * 商品编号
     */
    private String goods_code;

    /**
     * 商品名称
     */
    private String goods_name;

    /**
     *  规格型号
     */
    private String stock_sfn;

    /**
     *  单位
     */
    private String unit_name;

    /**
     * 到货数量
     */
    private BigDecimal arrived_num;

    /**
     * 待上架数量
     */
    private BigDecimal treat_list_num;

    /**
     * 已上架数量
     */
    private BigDecimal list_num;

    /**
     * 本次入库数量
     */
    private BigDecimal receipt_num;

    /**
     * 单价
     */
    private BigDecimal unit_price;

    /**
     * 合计
     */
    private BigDecimal total_price;

    /**
     * 父分类id
     */
    private Long material_classification_id;

    /**
     * 子分类id
     */
    private Long material_subcategory_id;

    /**
     * 仓库id
     */
    private Long warehouse_id;

    /**
     * 仓库名称
     */
    private String warehouse_name;

    /**
     * 库区id
     */
    private Long location_id;

    /**
     * 库区名称
     */
    private String location_name;

    /**
     * 库位id
     */
    private Long area_id;

    /**
     * 库位名称
     */
    private String area_name;

    /**
     * 生产日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date production_date;

    /**
     * 失效日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date expiry_date;

    /**
     * 备注
     */
    private String remark;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
