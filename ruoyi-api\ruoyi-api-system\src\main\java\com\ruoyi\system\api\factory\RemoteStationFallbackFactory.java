package com.ruoyi.system.api.factory;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.system.api.RemoteStationService;
import com.ruoyi.system.api.domain.BasicStationDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class RemoteStationFallbackFactory implements FallbackFactory<RemoteStationService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteStationFallbackFactory.class);

    @Override
    public RemoteStationService create(Throwable cause) {
        log.error("工位服务调用失败:{}", cause.getMessage());
        return new RemoteStationService() {
            @Override
            public R<List<BasicStationDto>> getStationListByIds(List<Long> stationIds) {
                return R.fail("获取工位列表失败:" + cause.getMessage());
            }

            @Override
            public R<List<BasicStationDto>> getStationListByProcessIds(List<Long> processIds, String source) {
                return R.fail("根据工序获取工位列表失败:" + cause.getMessage());
            }
        };
    }
} 