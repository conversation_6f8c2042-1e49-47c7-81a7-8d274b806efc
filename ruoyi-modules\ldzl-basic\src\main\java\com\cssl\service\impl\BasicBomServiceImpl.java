package com.cssl.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cssl.mapper.BasicBomMapper;
import com.cssl.pojo.BasicBom;
import com.cssl.pojo.BasicBomdetails;
import com.cssl.pojo.vo.BasicBomdetailsVo;
import com.cssl.service.BasicBomService;
import com.cssl.service.BasicBomdetailsService;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class BasicBomServiceImpl extends ServiceImpl<BasicBomMapper, BasicBom> implements BasicBomService {
    @Resource
    private BasicBomMapper basicBomMapper;
    @Resource
    private BasicBomdetailsService basicBomdetailsService;
    @Override
    public List<BasicBom> listBasicBom(BasicBom basicBom) {
        List<BasicBom> list=basicBomMapper.listBasicBom(basicBom);
        return list;
    }

    @Override
    @Transactional
    public int addBasicBom(BasicBom basicBom) {
        basicBom.setCreate_by(SecurityUtils.getUsername());
        basicBom.setCreate_time(new Date());
        basicBom.setIs_delete("0");
       int num=basicBomMapper.addBasicBom(basicBom);
        System.out.println("lx:"+basicBom.getBom_id());
       if(num>0){
           List<BasicBomdetails> list=basicBom.getBasicBomdetails();
           if(list!=null){
               for(BasicBomdetails b:list){
                   b.setCreate_by(SecurityUtils.getUsername());
                   b.setCreate_time(new Date());
                   b.setIs_detele("0");
                   b.setBom_id(basicBom.getBom_id());
                   basicBomdetailsService.save(b);
               }
           }


           System.out.println("sss:"+list);

       }
        return num;
    }

    @Override
    @Transactional
    public int updateBasicBom(BasicBom basicBom) {
        basicBom.setUpdate_by(SecurityUtils.getUsername());
        basicBom.setUpdate_time(new Date());
        int num=basicBomMapper.updateBasicBom(basicBom);
       List<BasicBomdetails> list=basicBom.getBasicBomdetails();
       List<BasicBomdetailsVo> list1= BeanUtil.copyToList(list, BasicBomdetailsVo.class);
        if (list != null && !list.isEmpty()) {
            // 查询已有ID
            List<Long> existingIds = basicBomdetailsService.selectBasicBomdetailsVoByBomIdAndIsDelete(basicBom.getBom_id())
                    .stream()
                    .map(BasicBomdetails::getBomdetails_id)
                    .collect(Collectors.toList());

            List<BasicBomdetails> insertList = new ArrayList<>();
            List<BasicBomdetails> updateList = new ArrayList<>();

            for (BasicBomdetails detail : list) {
                detail.setBom_id(basicBom.getBom_id());
                if (detail.getBomdetails_id() != null) {
                    // 更新
                    detail.setUpdate_by(SecurityUtils.getUsername());
                    detail.setUpdate_time(new Date());
                    updateList.add(detail);
                } else {
                    // 新增
                    detail.setCreate_by(SecurityUtils.getUsername());
                    detail.setCreate_time(new Date());
                    detail.setIs_detele("0");
                    insertList.add(detail);
                }
            }

            // 批量操作
            if (!insertList.isEmpty()) {
                basicBomdetailsService.saveBatch(insertList);
            }
            if (!updateList.isEmpty()) {
                basicBomdetailsService.updateBatchById(updateList);
            }

            // 删除未提交的ID
            Set<Long> newIds = list.stream()
                    .map(BasicBomdetails::getBomdetails_id)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            List<Long> toDeleteIds = existingIds.stream()
                    .filter(id -> !newIds.contains(id))
                    .collect(Collectors.toList());

            if (!toDeleteIds.isEmpty()) {
                basicBomdetailsService.deleteAllBasicBomdetails(toDeleteIds);
            }
        } else {
            basicBomdetailsService.deleteBasicBomdetails(basicBom.getBom_id());
        }

        return num;
    }


    @Override
    @Transactional
    public int deleteBasicBom(Long bom_id)
    {
        basicBomdetailsService.deleteBasicBomdetails(bom_id);

        return basicBomMapper.deleteBasicBom(bom_id);
    }

    @Override
    @Transactional
    public int deleteBasicBomByIds(List<Long> bom_ids) {
        basicBomdetailsService.deleteBasicBomdetailsByIds(bom_ids);
        return basicBomMapper.deleteBasicBomByIds(bom_ids);
    }

    @Override
    public List<BasicBom> getBasicBom(Long bom_id) {
        return basicBomMapper.getBasicBom(bom_id);
    }

    @Override
    public List<BasicBom> getBomIdByProductId(List<Long> product_ids) {
        return basicBomMapper.getBomIdByProductId(product_ids);
    }
}
