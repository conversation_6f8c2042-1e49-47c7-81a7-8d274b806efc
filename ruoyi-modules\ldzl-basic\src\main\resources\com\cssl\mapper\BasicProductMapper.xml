<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cssl.mapper.BasicProductMapper">
    <resultMap id="selectBasicProduct" type="com.cssl.pojo.BasicProduct">
        <id column="product_id" property="product_id"/>
        <association property="basicWlflz" javaType="com.cssl.pojo.BasicWlflz">
            <id column="material_subcategory_id" property="material_subcategory_id"/>
        </association>

        <association property="basicWlfl" javaType="com.cssl.pojo.BasicWlfl">
            <id column="material_classification_id" property="material_classification_id"/>
        </association>
    </resultMap>
    <insert id="addBasicProduct">
        INSERT into basic_products
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="product_code!= null and product_code!=''">product_code,</if>
            <if test="product_name != null and product_name != ''">product_name,</if>
            <if test="product_sfn != null and product_sfn !='' ">product_sfn,</if>
            <if test="product_unit != null and product_unit != ''" >product_unit,</if>
            <if test="product_type != null and product_type !=''">product_type,</if>
            <if test="product_status != null ">product_status,</if>
            <if test="expirydate != null ">expirydate,</if>
            <if test="purchaseprice != null ">purchaseprice,</if>
            <if test="saleprice != null ">saleprice,</if>
            <if test="remarks != null and remarks !='' ">remarks,</if>
            <if test="img != null and img !=''">img,</if>
            <if test="create_by != null ">create_by,</if>
            <if test="create_time != null ">create_time,</if>
            <if test="is_delete != null ">is_delete,</if>
            <if test="material_subcategory_id !=null">material_subcategory_id,</if>
            <if test="material_classification_id !=null">material_classification_id,</if>
            <if test="productcategory !=null">productcategory,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="product_code!= null and product_code!=''">#{product_code},</if>
            <if test="product_name != null and product_name != ''">#{product_name},</if>
            <if test="product_sfn != null and product_sfn !='' ">#{product_sfn},</if>
            <if test="product_unit != null and product_unit != ''" >#{product_unit},</if>
            <if test="product_type != null and product_type !=''">#{product_type},</if>
            <if test="product_status != null">#{product_status},</if>
            <if test="expirydate != null ">#{expirydate},</if>
            <if test="purchaseprice != null ">#{purchaseprice},</if>
            <if test="saleprice != null ">#{saleprice},</if>
            <if test="remarks != null and remarks !='' ">#{remarks},</if>
            <if test="img != null and img !=''">#{img},</if>
            <if test="create_by != null ">#{create_by},</if>
            <if test="create_time != null ">#{create_time},</if>
            <if test="is_delete != null ">#{is_delete},</if>
            <if test="material_subcategory_id !=null">#{material_subcategory_id},</if>
            <if test="material_classification_id !=null">#{material_classification_id},</if>
            <if test="productcategory !=null">#{productcategory},</if>
        </trim>
    </insert>
    <update id="delBasicProductByIds">
        update basic_products set is_delete = '1' where product_id in
        <foreach item="product_ids" collection="list" open="(" separator="," close=")">
            #{product_ids}
        </foreach>
    </update>

    <select id="listBasicProductMapper" resultType="com.cssl.pojo.BasicProduct">
        SELECT * from basic_products pr inner join basic_wlflz lz on pr.material_subcategory_id=lz.material_subcategory_id inner JOIN
        basic_wlfl fl on lz.material_classification_id=fl.material_classification_id WHERE pr.is_delete=0 and lz.is_delete=0 and fl.is_delete=0
        <if test="product_id != null">and pr.product_id=#{product_id}</if>
        <if test="product_name !=null and product_name !=''">and product_name like CONCAT('%',#{product_name},'%')</if>
        <if test="product_type !=null and product_type !=''">and product_type=#{product_type}</if>
    </select>



    <update id="updateBasicProduct">
        update basic_products
        <trim prefix="SET" suffixOverrides=",">
            <if test="product_name != null and product_name != ''">product_name=#{product_name},</if>
            <if test="product_sfn != null and product_sfn !='' ">product_sfn=#{product_sfn},</if>
            <if test="product_unit != null and product_unit != ''" >product_unit=#{product_unit},</if>
            <if test="product_type != null and product_type !=''">product_type=#{product_type},</if>
            <if test="product_status != null ">product_status=#{product_status},</if>
            <if test="expirydate != null ">expirydate=#{expirydate},</if>
            <if test="purchaseprice != null ">purchaseprice=#{purchaseprice},</if>
            <if test="saleprice != null ">saleprice=#{saleprice},</if>
            <if test="remarks != null and remarks !='' ">remarks=#{remarks},</if>
            <if test="img != null and img !=''">img=#{img},</if>
        </trim>
        where product_id = #{product_id}
    </update>

    <update id="delBasicProduct">
        update basic_products
        <trim prefix="SET" suffixOverrides=",">
            <if test="is_delete != null ">is_delete =1,</if>
        </trim>
        where product_id = #{product_id}
    </update>

</mapper>