package com.cssl.service.impl;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cssl.mapper.BasicNumbersMapper;
import com.cssl.pojo.BasicNumbers;
import com.cssl.service.BasicNumbersService;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

@Service
public class BasicNumbersServiceImpl extends ServiceImpl<BasicNumbersMapper, BasicNumbers> implements BasicNumbersService {
   @Resource
    private BasicNumbersMapper basicNumbersMapper;
    @Override
    public BasicNumbers selectBasicNumbersByEnCode(Long enCode)
    {
        return basicNumbersMapper.selectBasicNumbersByEnCode(enCode);
    }

    /**
     * 查询自动生成编号列表
     *
     * @param basicNumbers 自动生成编号
     * @return 自动生成编号
     */
    @Override
    public List<BasicNumbers> selectBasicNumbersList(BasicNumbers basicNumbers)
    {
        return basicNumbersMapper.selectBasicNumbersList(basicNumbers);
    }

    /**
     * 新增自动生成编号
     *
     * @param basicNumbers 自动生成编号
     * @return 结果
     */
    @Override
    public int insertBasicNumbers(BasicNumbers basicNumbers)
    {
        basicNumbers.setCreateTime(DateUtils.getNowDate());
        basicNumbers.setLastDate(DateUtils.getNowDate());
        basicNumbers.setEnNum(0L);
        basicNumbers.setEnRules(generateNumbers(basicNumbers));
        //System.out.println(basicNumbers);
        basicNumbers.setIsDel(0L);
        return basicNumbersMapper.insertBasicNumbers(basicNumbers);
    }

    /**
     * 修改自动生成编号
     *
     * @param basicNumbers 自动生成编号
     * @return 结果
     */
    @Override
    public int updateBasicNumbers(BasicNumbers basicNumbers)
    {
        basicNumbers.setEnRules(generateNumbers(basicNumbers));
        basicNumbers.setUpdateTime(new Date());
        return basicNumbersMapper.updateBasicNumbers(basicNumbers);
    }

    /**
     * 批量删除自动生成编号
     *
     * @param enCodes 需要删除的自动生成编号主键
     * @return 结果
     */
    @Override
    public int deleteBasicNumbersByEnCodes(Long[] enCodes)
    {
        return basicNumbersMapper.deleteBasicNumbersByEnCodes(enCodes);
    }

    /**
     * 删除自动生成编号信息
     *
     * @param enCode 自动生成编号主键
     * @return 结果
     */
    @Override
    public int deleteBasicNumbersByEnCode(Long enCode)
    {
        return basicNumbersMapper.deleteBasicNumbersByEnCode(enCode);
    }

    /**
     * 根据规则自动生成编号
     * @param enCode 主键
     * @return 编号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized String automaticallyNumbers(Long enCode) {
        //编码
        String serialNumber=null;
        //修改订单流水号
        updateBasicNumbersByEnNum(enCode);

        BasicNumbers basicNumbers = selectBasicNumbersByEnCode(enCode);

        SimpleDateFormat simpleFormatter=new SimpleDateFormat(basicNumbers.getEnTime());
        //当前时间格式
        String timeFormat = simpleFormatter.format(new Date());

        //流水号
        Long enNum = basicNumbers.getEnNum()+basicNumbers.getEnStep();

        if (enNum>basicNumbers.getMaxNum()){
            return null;
        }
        BasicNumbers numbers = new BasicNumbers();
        numbers.setEnCode(basicNumbers.getEnCode());
        numbers.setEnNum(enNum);
        numbers.setLastDate(new Date());
        basicNumbersMapper.updateBasicNumbers(numbers);

        serialNumber = basicNumbers.getEnPrefix()+timeFormat+String.format("%0"+basicNumbers.getMaxNum().toString().length()+"d",enNum);
        return serialNumber;
    }

    /**
     * 通过判断刷新规则，修改订单流水号
     *
     * @param enCode 编号ID  刷新格式  编号流水号
     *
     */
    public void updateBasicNumbersByEnNum(Long enCode){
        BasicNumbers basicNumbers=selectBasicNumbersByEnCode(enCode);
        Instant instant = basicNumbers.getLastDate().toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        LocalDate oldDate=instant.atZone(zoneId).toLocalDate();
        LocalDate newDate=LocalDate.now();
        //时间差
        Period period=Period.between(oldDate,newDate);

        BasicNumbers numbers=new BasicNumbers();
        numbers.setEnCode(basicNumbers.getEnCode());
        numbers.setLastDate(new Date());
        numbers.setEnNum(0L);

        if (basicNumbers.getEnFlushed() == 1) {
            if (period.getDays() > 0) {
                basicNumbersMapper.updateBasicNumbers(numbers);
            }
        } else if (basicNumbers.getEnFlushed() == 2) {
            if (period.getMonths() > 0) {
                basicNumbersMapper.updateBasicNumbers(numbers);
            }
        } else if (basicNumbers.getEnFlushed() == 3) {
            if (period.getYears() > 0) {
                basicNumbersMapper.updateBasicNumbers(numbers);
            }
        }
    }

    /**
     * 生成编号案例
     * @param basicNumbers 类
     * @return 返回编号
     */
    private String generateNumbers(BasicNumbers basicNumbers){
        //编码
        String serialNumber=null;
        SimpleDateFormat simpleFormatter=new SimpleDateFormat(basicNumbers.getEnTime());
        //当前时间格式
        String timeFormat = simpleFormatter.format(new Date());
        serialNumber= basicNumbers.getEnPrefix()+timeFormat+String.format("%0"+basicNumbers.getMaxNum().toString().length()+"d",1);
        return serialNumber;
    }
}
