package com.ruoyi.system.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("basic_units")
public class BasicUnits {
    @TableId(value ="unitid",type = IdType.AUTO)
    private Integer unitid;
    private String unitcode;
    private String unitname;
    private Integer isPrimaryunit;
    private BigDecimal conversionrate;
    private Integer isactive;
    private String remarks;
    private Integer un_del;
    private String create_by;
    private Date create_time;
    private String update_by;
    private Date update_time;
    private String mainunit;
}
