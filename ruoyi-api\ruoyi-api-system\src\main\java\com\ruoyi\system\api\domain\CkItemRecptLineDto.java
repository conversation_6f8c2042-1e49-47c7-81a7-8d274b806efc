package com.ruoyi.system.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * 物料入库单行表
 * @TableName ck_item_recpt_line
 */
@TableName(value ="ck_item_recpt_line")
@Data
public class CkItemRecptLineDto implements Serializable {
    /**
     * 行ID
     */
    @TableId(type = IdType.AUTO)
    private Long line_id;

    /**
     * 入库单ID
     */
    private Long recpt_id;

    /**
     * 采购详情表行ID
     */
    private Long order_line_id;

    /**
     * 货物编号
     */
    private String goods_code;

    /**
     * 货物名称
     */
    private String goods_name;

    /**
     * 规格型号
     */
    private String stock_sfn;

    /**
     * 单位名称
     */
    private String unit_name;

    /**
     * 入库数量
     */
    private BigDecimal recived_num;

    /**
     * 批次编号
     */
    private String batch_code;

    /**
     * 父分类id
     */
    private Long material_classification_id;

    /**
     * 子分类id
     */
    private Long material_subcategory_id;

    /**
     * 库区ID
     */
    private Long location_id;

    /**
     * 库区名称
     */
    private String location_name;

    /**
     * 库位ID
     */
    private Long area_id;

    /**
     * 库位名称
     */
    private String area_name;

    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date produce_date;

    /**
     * 有效期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expire_date;

    /**
     * 生产批号
     */
    private String lot_number;

    /**
     * 是否来料检验
     */
    private String iqc_check;

    /**
     * 来料检验单ID
     */
    private Long iqc_id;

    /**
     * 备注
     */
    private String remark;

    /**
     * 预留字段1
     */
    private String attr1;

    /**
     * 预留字段2
     */
    private String attr2;

    /**
     * 预留字段3
     */
    private Integer attr3;

    /**
     * 预留字段4
     */
    private Integer attr4;

    /**
     * 创建者
     */
    private String create_by;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date create_time;

    /**
     * 更新者
     */
    private String update_by;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date update_time;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    private String is_delete;


    /**
     * 到货数量
     */
    @TableField(exist = false)
    private BigDecimal arrived_num;

    /**
     * 待上架数量
     */
    @TableField(exist = false)
    private BigDecimal treat_list_num;     // 待上架数量

    /**
     * 已上架数量
     */
    @TableField(exist = false)
    private BigDecimal list_num;     // 以上架数量


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CkItemRecptLineDto that = (CkItemRecptLineDto) o;
        return recpt_id == that.recpt_id;
    }

    @Override
    public int hashCode() {
        return Objects.hash(recpt_id);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", line_id=").append(line_id);
        sb.append(", recpt_id=").append(recpt_id);
        sb.append(", order_line_id=").append(order_line_id);
        sb.append(", goods_code=").append(goods_code);
        sb.append(", goods_name=").append(goods_name);
        sb.append(", stock_sfn=").append(stock_sfn);
        sb.append(", unit_name=").append(unit_name);
        sb.append(", recived_num=").append(recived_num);
        sb.append(", batch_name=").append(batch_code);
        sb.append(", location_id=").append(location_id);
        sb.append(", location_name=").append(location_name);
        sb.append(", area_id=").append(area_id);
        sb.append(", area_name=").append(area_name);
        sb.append(", produce_date=").append(produce_date);
        sb.append(", expire_date=").append(expire_date);
        sb.append(", lot_number=").append(lot_number);
        sb.append(", iqc_check=").append(iqc_check);
        sb.append(", iqc_id=").append(iqc_id);
        sb.append(", remark=").append(remark);
        sb.append(", attr1=").append(attr1);
        sb.append(", attr2=").append(attr2);
        sb.append(", attr3=").append(attr3);
        sb.append(", attr4=").append(attr4);
        sb.append(", create_by=").append(create_by);
        sb.append(", create_time=").append(create_time);
        sb.append(", update_by=").append(update_by);
        sb.append(", update_time=").append(update_time);
        sb.append(", is_delete=").append(is_delete);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}