package com.cssl.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cssl.mapper.BasicOperationdetailsMapper;
import com.cssl.pojo.*;
import com.cssl.service.BasicBomService;
import com.cssl.service.BasicBomdetailsService;
import com.cssl.service.BasicOperatonaldetailsService;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.cssl.mapper.BasicOperationalMapper;
import com.cssl.service.BasicOperationalService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 工艺路线 Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class BasicOperationalServiceImpl extends ServiceImpl<BasicOperationalMapper, BasicOperational> implements BasicOperationalService{

    @Resource
    private BasicOperationalMapper SbasicOperationalMapper;
    @Resource
    private BasicOperatonaldetailsService basicOperatonaldetailsService;
    @Resource
    private BasicBomService basicBomService;
    @Resource
    private BasicBomdetailsService basicBomdetailsService;

    /**
     * 查询工艺路线
     *
     * @param operationalId 工艺路线主键
     * @return 工艺路线
     */
    @Override
    public BasicOperational selectBasicOperationalById(Long operationalId)
    {
        return SbasicOperationalMapper.selectById(operationalId);
    }

    /**
     * 查询工艺路线列表
     *
     * @param basicOperational 工艺路线
     * @return 工艺路线
     */
    @Override
    public List<BasicOperational> selectBasicOperationalList(BasicOperational basicOperational)
    {
        return SbasicOperationalMapper.selectBasicOperationalList(basicOperational);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertBasicOperational(BasicOperational basicOperational) {
        basicOperational.setCreate_by(SecurityUtils.getUsername());
        basicOperational.setCreate_time(new Date());
        basicOperational.setIs_delete("0");
        int num=SbasicOperationalMapper.insertBasicOperational(basicOperational);
        if(num>0) {
            List<BasicOperatonaldetails> list = basicOperational.getOperatonaldetails();
            if (list != null && !list.isEmpty()) {
                for (BasicOperatonaldetails b : list) {
                    b.setCreate_by(SecurityUtils.getUsername());
                    b.setCreate_time(new Date());
                    b.setIs_delete("0");
                    b.setOperational_id(basicOperational.getOperational_id());
                    System.out.println("sdd" + b.getOperational_id());
                    System.out.println("dwd:" + b);
                    basicOperatonaldetailsService.save(b);
                }
                System.out.println("sss:" + list);
            }
            List<BasicBomdetails> li=basicBomdetailsService.selectBasicBomdetailsVoByBom();
            List<BasicOperatonaldetails> list1=basicOperatonaldetailsService.getBasicOperationdetailsByOperationalId(basicOperational.getOperational_id());
            System.out.println("sdce："+list1);
            // 存放结果
            List<Long> proids = new ArrayList<>();
            for (BasicOperatonaldetails detail : list1) {
                Long proid = detail.getProduct_id();
                proids.add(proid);
            }
            System.out.println("dekf:"+proids);
            List<BasicBom> bi=basicBomService.getBomIdByProductId(proids);
            List<Long> bomids=new ArrayList<>();
            for (BasicBom n :bi){
                bomids.add(n.getBom_id());

            }
            System.out.println("bpmids:"+bomids);

            //Long bomid=basicBomService.getBomIdByProductId();
            //System.out.println("bom:"+bomid);
            for (int i = 0; i < li.size() && i < bomids.size(); i++) {
                BasicBomdetails n = li.get(i);
                Long bomId = bomids.get(i);

                n.setBom_id(bomId);                         // 把 bom_id 设进去
                n.setOperational_id(basicOperational.getOperational_id());
                n.setUpdate_by(SecurityUtils.getUsername());
                n.setUpdate_time(new Date());
                System.out.println("n:" + n.getBom_id());
                basicBomdetailsService.updateBasicBomdetailsByBomId(n);
            }
        }
        return num;

    }

    @Override
    @Transactional
    public int updateBasicOperational(BasicOperational basicOperational) {
        basicOperational.setUpdate_by(SecurityUtils.getUsername());
        basicOperational.setUpdate_time(new Date());
        int num=SbasicOperationalMapper.updateBasicOperational(basicOperational);
        if(num>0){
                List<BasicOperatonaldetails> list=basicOperational.getOperatonaldetails();
            if (list != null && !list.isEmpty()) {
                // 查询已有ID
                List<Long> existingIds = basicOperatonaldetailsService.getBasicOperationdetailsByOperationalId3(basicOperational.getOperational_id())
                        .stream()
                        .map(BasicOperatonaldetails::getOperationaldetails_id)
                        .collect(Collectors.toList());

                List<BasicOperatonaldetails> insertList = new ArrayList<>();
                List<BasicOperatonaldetails> updateList = new ArrayList<>();

                for (BasicOperatonaldetails detail : list) {
                    detail.setOperational_id(basicOperational.getOperational_id());
                    if (detail.getOperationaldetails_id() != null) {
                        // 更新
                        detail.setUpdate_by(SecurityUtils.getUsername());
                        detail.setUpdate_time(new Date());
                        updateList.add(detail);
                    } else {
                        // 新增
                        detail.setCreate_by(SecurityUtils.getUsername());
                        detail.setCreate_time(new Date());
                        detail.setIs_delete("0");
                        insertList.add(detail);
                    }
                }

                // 批量操作
                if (!insertList.isEmpty()) {
                    basicOperatonaldetailsService.saveBatch(insertList);
                }
                if (!updateList.isEmpty()) {
                    basicOperatonaldetailsService.updateBatchById(updateList);
                }

                // 删除未提交的ID
                Set<Long> newIds = list.stream()
                        .map(BasicOperatonaldetails::getOperationaldetails_id)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());

                List<Long> toDeleteIds = existingIds.stream()
                        .filter(id -> !newIds.contains(id))
                        .collect(Collectors.toList());

                if (!toDeleteIds.isEmpty()) {
                    basicOperatonaldetailsService.deleteBatchBasicOperationdetails(toDeleteIds);
                }
            } else {
                basicOperatonaldetailsService.deleteBasicOperationdetailsById(basicOperational.getOperational_id());
            }

            List<BasicOperatonaldetails> li=basicOperational.getOperatonaldetails();
            System.out.println("nnnmm:"+li);
            List<Long> pp=new ArrayList<>();
            if(li !=null && !li.isEmpty() ){
                for (BasicOperatonaldetails n:li){
                    Long pro=n.getDeleteProductIds();
                    pp.add(pro);
                }
                System.out.println("pp:"+pp);
                if(pp!=null && !pp.isEmpty()){
                    List<BasicBom> bi=basicBomService.getBomIdByProductId(pp);
                    List<Long> bomids=new ArrayList<>();
                    for (BasicBom n :bi){
                        bomids.add(n.getBom_id());

                    }
                    System.out.println("bpmids:"+bomids);
                   basicBomdetailsService.delByoperational(bomids);
                }
                List<BasicBomdetails> lis=basicBomdetailsService.selectBasicBomdetailsVoByBom();
                List<BasicOperatonaldetails> list1=basicOperatonaldetailsService.getBasicOperationdetailsByOperationalId(basicOperational.getOperational_id());
                System.out.println("sdce："+list1);
                // 存放结果
                if(list1!=null && !list1.isEmpty()){
                    List<Long> proids = new ArrayList<>();
                    for (BasicOperatonaldetails detail : list1) {
                        Long proid = detail.getProduct_id();
                        proids.add(proid);
                    }
                    System.out.println("dekf:"+proids);
                    List<BasicBom> bi=basicBomService.getBomIdByProductId(proids);
                    List<Long> bomids=new ArrayList<>();
                    for (BasicBom n :bi){
                        bomids.add(n.getBom_id());

                    }
                    System.out.println("bpmids:"+bomids);

                    //Long bomid=basicBomService.getBomIdByProductId();
                    //System.out.println("bom:"+bomid);
                    for (int i = 0; i < li.size() && i < bomids.size(); i++) {
                        BasicBomdetails n = lis.get(i);
                        Long bomId = bomids.get(i);

                        n.setBom_id(bomId);                         // 把 bom_id 设进去
                        n.setOperational_id(basicOperational.getOperational_id());
                        n.setUpdate_by(SecurityUtils.getUsername());
                        n.setUpdate_time(new Date());
                        System.out.println("n:" + n.getBom_id());
                        basicBomdetailsService.updateBasicBomdetailsByBomId(n);
                    }
                }



            }
        }
        return num;
    }

    @Override
    @Transactional
    public int deleteBasicOperationalById(Long operational_id) {
        basicOperatonaldetailsService.deleteBasicOperationdetailsById(operational_id);
        return SbasicOperationalMapper.deleteBasicOperationalById(operational_id);
    }

    @Override
    @Transactional
    public int deleteBatchBasicOperational(List<Long> operationalIds) {
        basicOperatonaldetailsService.delBatchBasicOperationdetails(operationalIds);
        return SbasicOperationalMapper.deleteBatchBasicOperational(operationalIds);
    }


    /**
     * 根据产品ID查询工艺路线列表
     *
     * @param productId 产品ID
     * @return 工艺路线
     */
    @Override
    public List<BasicOperational> selectBasicOperationalByProductId(Long productId) {
        return SbasicOperationalMapper.selectBasicOperationalByProductId(productId);
    }

    @Override
    public List<BasicOperational> listBasicOperational(BasicOperational basicOperational) {
        return SbasicOperationalMapper.listBasicOperational(basicOperational);
    }
}