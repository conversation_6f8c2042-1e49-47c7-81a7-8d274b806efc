package com.cssl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cssl.pojo.BasicBomdetails;
import com.cssl.pojo.BasicProcessdetails;
import com.cssl.pojo.vo.BasicProcessdetailsVo;

import java.util.List;

public interface BasicProcessdetailsMapper extends BaseMapper<BasicProcessdetails> {

    //查看工序中物料配料和工位信息
    public List<BasicProcessdetails> listBasicProcessdetails(BasicProcessdetails basicProcessdetails);

    //删除工序详情信息
    public int delBasicProcessdetails(Long processdetails_id);

    //批量删除
    public int delBatchBasicProcessdetails(List<Long> processdetailsIds);

    //根据工序id查询工序详情信息
    public List<BasicProcessdetails> listBasicProcessdetailsByProcessId(Long process_id);

    //根据物料id查询工序详情信息
    public List<BasicProcessdetailsVo> listBasicProcessdetailsvoBy(Long process_id);

    //根据工序id批量删除
    public int delBatchBasicProcessdetailsByProcessId(List<Long> processIds);

}
