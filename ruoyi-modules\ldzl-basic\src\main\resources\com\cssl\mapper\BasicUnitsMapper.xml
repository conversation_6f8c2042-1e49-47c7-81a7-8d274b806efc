<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cssl.mapper.BasicUnitsMapper">

    <insert id="addBasicUnits">
        INSERT into basic_units
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="unit_code != null and unit_code != ''">unit_code,</if>
            <if test="unit_name != null and unit_name != ''">unit_name,</if>
            <if test="isPrimary_unit != null ">isPrimary_unit,</if>
            <if test="conversionrate != null and conversionrate !='' ">conversionrate,</if>
            <if test="create_by != null and create_by != ''" >create_by,</if>
            <if test="create_time != null">create_time,</if>
            <if test="unit_status != null">unit_status,</if>
            <if test="is_delete != null">is_delete,</if>
            <if test="remarks != null and remarks != ''">remarks,</if>
            <if test="main_unit != null and main_unit != ''">main_unit,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="unit_code != null and unit_code != ''">#{unit_code},</if>
            <if test="unit_name != null and unit_name != ''">#{unit_name},</if>
            <if test="isPrimary_unit != null ">#{isPrimary_unit},</if>
            <if test="conversionrate != null and conversionrate !='' ">#{conversionrate},</if>
            <if test="create_by != null and create_by != ''" >#{create_by},</if>
            <if test="create_time != null">#{create_time},</if>
            <if test="unit_status != null">#{unit_status},</if>
            <if test="is_delete != null">#{is_delete},</if>
            <if test="remarks != null and remarks != ''">#{remarks},</if>
            <if test="main_unit != null and main_unit != ''">#{main_unit},</if>
        </trim>
    </insert>



    <update id="updateBasicUnits">
        update basic_units
        <trim prefix="SET" suffixOverrides=",">
            <if test="unit_name != null and unit_name != ''">unit_name = #{unit_name},</if>
            <if test="isPrimary_unit != null ">isPrimary_unit = #{isPrimary_unit},</if>
           conversionrate = #{conversionrate},
            <if test="unit_status != null ">unit_status = #{unit_status},</if>
            main_unit = #{main_unit},
            <if test="remarks != null and remarks !='' ">remarks = #{remarks},</if>
            <if test="update_by != null and update_by !='' ">update_by=#{update_by},</if>
            <if test="update_time!= null ">update_time=#{update_time},</if>
        </trim>
        where unit_id = #{unit_id}
    </update>

    <update id="delBasicUnits">
        update basic_units
        <trim prefix="SET" suffixOverrides=",">
            <if test="is_delete != null ">is_delete =1,</if>
        </trim>
        where unit_id = #{unit_id}
    </update>
    <update id="delBatchBasicUnits">
        update basic_units set is_delete = '1' where unit_id in
        <foreach item="unit_ids" collection="list" open="(" separator="," close=")">
            #{unit_ids}
        </foreach>
    </update>


    <select id="listBasicUnits" resultType="com.cssl.pojo.BasicUnits">
        SELECT * from basic_units WHERE is_delete=0
        <if test="unit_code !=null and unit_code !=''">
            and unit_code like CONCAT('%',#{unit_code},'%')
        </if>
        <if test="unit_name != null and unit_name !=''">
            and unit_name like CONCAT('%',#{unit_name},'%')
        </if>
        <if test="unit_id!= null and unit_id!=''">
            and unit_id = #{unit_id}
        </if>
    </select>
    <select id="findByBasicUnits" resultType="com.cssl.pojo.BasicUnits">
        SELECT * from basic_units WHERE is_delete=0 and unit_status=1

    </select>
</mapper>