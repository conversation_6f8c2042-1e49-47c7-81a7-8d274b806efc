package com.cssl;

import com.ruoyi.common.security.annotation.EnableCustomConfig;
import com.ruoyi.common.security.annotation.EnableRyFeignClients;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication
@MapperScan("com.cssl.mapper")
@EnableCustomConfig
@EnableRyFeignClients
public class LdzlBasicApplication {

    public static void main(String[] args) {
        try {
            SpringApplication.run(LdzlBasicApplication.class, args);
        } catch (Throwable  e) {
            e.printStackTrace();
        }
    }

}
