<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cssl.mapper.BasicSuppliersMapper">

    <insert id="addBasicSuppliers">
        INSERT into basic_suppliers
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="supplier_code!= null and supplier_code!=''">supplier_code,</if>
            <if test="supplier_name != null and supplier_name != ''">supplier_name,</if>
            <if test="supplier_level != null and supplier_level !='' ">supplier_level,</if>
            <if test="supplier_person != null and supplier_person != ''" >supplier_person,</if>
            <if test="supplier_phone != null and supplier_phone !=''">supplier_phone,</if>
            <if test="phone_status != null ">phone_status,</if>
            <if test="supplier_status != null ">supplier_status,</if>
            <if test="remarks != null and remarks !=''">remarks,</if>
            <if test="is_delete != null ">is_delete,</if>
            <if test="create_by != null and create_by !='' ">create_by,</if>
            <if test="create_time!= null ">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="supplier_code!= null and supplier_code!=''">#{supplier_code},</if>
            <if test="supplier_name != null and supplier_name != ''">#{supplier_name},</if>
            <if test="supplier_level != null and supplier_level !='' ">#{supplier_level},</if>
            <if test="supplier_person != null and supplier_person != ''" >#{supplier_person},</if>
            <if test="supplier_phone != null and supplier_phone !=''">#{supplier_phone},</if>
            <if test="phone_status != null ">#{phone_status},</if>
            <if test="supplier_status != null ">#{supplier_status},</if>
            <if test="remarks != null and remarks !=''">#{remarks},</if>
            <if test="is_delete != null ">#{is_delete},</if>
            <if test="create_by != null and create_by !='' ">#{create_by},</if>
            <if test="create_time!= null ">#{create_time},</if>
        </trim>
    </insert>

    <update id="updateBasicSuppliers">
        update basic_suppliers
        <trim prefix="SET" suffixOverrides=",">
            <if test="supplier_name != null and supplier_name != ''">supplier_name=#{supplier_name},</if>
            <if test="supplier_level != null and supplier_level !='' ">supplier_level=#{supplier_level},</if>
            <if test="supplier_person != null and supplier_person != ''" >supplier_person=#{supplier_person},</if>
            <if test="supplier_phone != null and supplier_phone !=''">supplier_phone=#{supplier_phone},</if>
            <if test="phone_status != null ">phone_status=#{phone_status},</if>
            <if test="supplier_status != null ">supplier_status=#{supplier_status},</if>
            <if test="remarks != null and remarks !=''">remarks=#{remarks},</if>
            <if test="update_by != null and update_by !='' ">update_by=#{update_by},</if>
            <if test="update_time!= null ">update_time=#{update_time},</if>
        </trim>
        where supplier_id = #{supplier_id}
    </update>

    <update id="delBasicSuppliers">
        update basic_suppliers
        <trim prefix="SET" suffixOverrides=",">
            <if test="is_delete != null ">is_delete =1,</if>
        </trim>
        where supplier_id = #{supplier_id}
    </update>
    <update id="delBatchBasicSuppliers">
        update basic_suppliers set is_delete = '1' where supplier_id in
        <foreach item="supplier_ids" collection="list" open="(" separator="," close=")">
            #{supplier_ids}
        </foreach>
    </update>


    <select id="listBasicSuppliers" resultType="com.cssl.pojo.BasicSuppliers">
        SELECT * from basic_suppliers where is_delete=0
        <if test="supplier_name!=null and supplier_name!=''">and supplier_name like CONCAT('%',#{supplier_name},'%')</if>
        <if test="supplier_level!=null and supplier_level!=''">and supplier_level=#{supplier_level}</if>
        <if test="supplier_status!=null ">and supplier_status=#{supplier_status}</if>
    </select>
</mapper>