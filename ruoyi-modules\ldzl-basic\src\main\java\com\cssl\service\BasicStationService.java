package com.cssl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cssl.pojo.BasicProduct;
import com.cssl.pojo.BasicStation;

import java.util.List;

public interface BasicStationService extends IService<BasicStation> {
    //查询工位信息
    public List<BasicStation> listBasicStation(BasicStation basicStation);

    //添加工位信息
    public int addBasicStation(BasicStation basicStation);

    //修改工位信息
    public int updateBasicStation(BasicStation basicStation);

    //删除工位信息
    public int delBasicStation(Long station_id);

    List<BasicStation> selectBasicStationByIds(List<Long> stationIds);

    List<BasicStation> selectBasicStationByProcessIds(List<Long> processIds);
}
