package com.cssl.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

import javax.naming.Name;
import java.io.Serializable;
import java.util.Date;

@Data
@TableName("basic_wlflz")
public class BasicWlflz  implements Serializable {
    @TableId(value ="material_subcategory_id",type = IdType.AUTO)
    private Integer material_subcategory_id;
    private String material_subcategory_code;
    private String material_subcategory_name;
    private String create_by;
    @JsonFormat(pattern = "yyyy-MM-dd" , timezone = "GMT+8")
    private Date create_time;
    private String update_by;
    @JsonFormat(pattern = "yyyy-MM-dd" , timezone = "GMT+8")
    private Date update_time;
    private String is_delete;
    private Integer material_classification_id;
    @TableField(exist = false)
    private BasicWlfl basicWlfl;
}
