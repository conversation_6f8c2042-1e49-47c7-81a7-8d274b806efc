package com.ruoyi.common.core.utils;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 多日期格式反序列化器
 * 支持多种日期格式的JSON反序列化
 * 
 * <AUTHOR>
 */
public class MultiDateFormatDeserializer extends JsonDeserializer<Date> {
    
    /**
     * 支持的日期格式数组
     */
    private static final String[] DATE_FORMATS = {
        "yyyy-MM-dd HH:mm:ss",
        "yyyy-MM-dd",
        "yyyy/MM/dd HH:mm:ss",
        "yyyy/MM/dd",
        "yyyy.MM.dd HH:mm:ss",
        "yyyy.MM.dd"
    };
    
    @Override
    public Date deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) 
            throws IOException {
        String dateString = jsonParser.getText();
        
        if (StringUtils.isBlank(dateString)) {
            return null;
        }
        
        // 尝试使用各种格式解析日期
        for (String format : DATE_FORMATS) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat(format);
                sdf.setLenient(false); // 严格模式
                return sdf.parse(dateString);
            } catch (ParseException e) {
                // 继续尝试下一个格式
            }
        }
        
        // 如果所有格式都失败，抛出异常
        throw new IOException("无法解析日期字符串: " + dateString + 
                            "，支持的格式: " + String.join(", ", DATE_FORMATS));
    }
}
