package com.cssl.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.web.domain.BaseEntity;
import com.ruoyi.system.api.domain.SysUser;
import lombok.Data;

import java.util.Date;

@Data
@TableName("basic_teamdetails")
public class BasicTeamdetails {
    @TableId(value ="teamdetails_id",type = IdType.AUTO)
    private Long teamdetails_id;
    private Long user_id;
    private String is_delete;
    private Long team_id;
    private String create_by;
    private Date create_time;
    private String update_by;
    private Date update_time;
    @TableField(exist = false)
    private BasicTeam basicTeam;
    @TableField(exist = false)
    private SysUser sysUser;
}
