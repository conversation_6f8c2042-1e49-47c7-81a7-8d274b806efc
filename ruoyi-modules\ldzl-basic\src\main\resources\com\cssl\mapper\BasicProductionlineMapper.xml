<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cssl.mapper.BasicProductionlineMapper">
    <resultMap id="selectBasicProductionline" type="com.cssl.pojo.BasicProductionline">
        <id column="production_line_id" property="production_line_id"/>
        <association property="basicWorkshop" javaType="com.cssl.pojo.BasicWorkshop">
            <id column="workshop_id" property="workshop_id"/>
        </association>
    </resultMap>

    <insert id="addBasicProductionline">
        insert into basic_productionline
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="factory_name !=null and factory_name !=''">factory_name,</if>
            <if test="factory_id !=null ">factory_id,</if>
            <if test="workshop_id !=null ">workshop_id,</if>
            <if test="workshop_name !=null and workshop_name !=''">workshop_name,</if>
            <if test="production_line_code !=null and production_line_code !=''">production_line_code,</if>
            <if test="production_line_name !=null and production_line_name !=''">production_line_name,</if>
            <if test="remarks !=null and remarks !=''">remarks,</if>
            <if test="is_delete!=null ">is_delete,</if>
            <if test="create_by!=null and create_by !='' ">create_by,</if>
            <if test="create_time!=null ">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="factory_name !=null and factory_name !=''">#{factory_name},</if>
            <if test="factory_id !=null ">#{factory_id},</if>
            <if test="workshop_id !=null ">#{workshop_id},</if>
            <if test="workshop_name !=null and workshop_name !=''">#{workshop_name},</if>
            <if test="production_line_code !=null and production_line_code !=''">#{production_line_code},</if>
            <if test="production_line_name !=null and production_line_name !=''">#{production_line_name},</if>
            <if test="remarks !=null and remarks !=''">#{remarks},</if>
            <if test="is_delete!=null ">#{is_delete},</if>
            <if test="create_by!=null and create_by !='' ">#{create_by},</if>
            <if test="create_time!=null ">#{create_time},</if>
        </trim>
    </insert>

    <update id="updateBasicProductionline">
        update basic_productionline
        <trim prefix="SET" suffixOverrides=",">
            <if test="factory_name !=null and factory_name !=''">factory_name=#{factory_name},</if>
            <if test="factory_id !=null ">factory_id=#{factory_id},</if>
            <if test="workshop_id !=null ">workshop_id=#{workshop_id},</if>
            <if test="workshop_name !=null and workshop_name !=''">workshop_name=#{workshop_name},</if>
            <if test="production_line_name !=null and production_line_name !=''">production_line_name=#{production_line_name},</if>
            <if test="remarks !=null and remarks !=''">remarks=#{remarks},</if>
            <if test="update_by!=null and update_by !='' ">update_by=#{update_by},</if>
            <if test="update_time!=null ">update_time=#{update_time},</if>
        </trim>
        where production_line_id=#{production_line_id}
    </update>

    <update id="delBasicProductionline">
        update basic_productionline
        <trim prefix="SET" suffixOverrides=",">
            <if test="is_delete !=null">is_delete=1</if>
        </trim>
        where production_line_id = #{production_line_id}
    </update>

    <select id="listBasicProductionline" resultType="com.cssl.pojo.BasicProductionline">
        SELECT * from basic_productionline bp inner join basic_workshop bw on bp.workshop_id=bw.workshop_id
                                              inner join basic_factory bf on bf.factory_id=bw.workshop_id where bp.is_delete=0 and bw.is_delete=0 and bf.is_delete=0
        <if test="basicWorkshop !=null and basicWorkshop.workshop_id !=null">and bw.workshop_id=#{basicWorkshop.workshop_id}</if>
        <if test="production_line_id !=null and production_line_id !=''">and bp.production_line_id=#{production_line_id}</if>
        <if test="production_line_name !=null and production_line_name !=''">and bp.production_line_name=#{production_line_name} </if>
    </select>
</mapper>