<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cssl.mapper.BasicNumbersMapper">

    <resultMap type="com.cssl.pojo.BasicNumbers" id="BasicNumbersResult">
        <result property="enCode"    column="en_code"    />
        <result property="enForm"    column="en_form"    />
        <result property="enPrefix"    column="en_prefix"    />
        <result property="enTime"    column="en_time"    />
        <result property="enNum"    column="en_num"    />
        <result property="enStep"    column="en_step"    />
        <result property="enFlushed"    column="en_flushed"    />
        <result property="enRules"    column="en_rules"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="isDel"    column="is_del"    />
        <result property="maxNum"    column="max_num"    />
        <result property="lastDate"    column="last_date"    />
    </resultMap>

    <sql id="selectBasicNumbersVo">
        select en_code, en_form, en_prefix, en_time, en_num, en_step, en_flushed, en_rules, create_by, create_time, is_del, max_num, last_date from basic_numbers
    </sql>

    <select id="selectBasicNumbersList" parameterType="com.cssl.pojo.BasicNumbers" resultMap="BasicNumbersResult">
        <include refid="selectBasicNumbersVo"/>
        <where>
            <if test="enForm != null  and enForm != ''"> and en_form = #{enForm}</if>
            <if test="enPrefix != null  and enPrefix != ''"> and en_prefix = #{enPrefix}</if>
            <if test="enTime != null  and enTime != ''"> and en_time = #{enTime}</if>
            <if test="enStep != null "> and en_step = #{enStep}</if>
            <if test="enFlushed != null "> and en_flushed = #{enFlushed}</if>
            <if test="maxNum != null "> and max_num = #{maxNum}</if>
            <if test="lastDate != null "> and last_date = #{lastDate}</if>
        </where>
    </select>

    <select id="selectBasicNumbersByEnCode" parameterType="Long" resultMap="BasicNumbersResult">
        <include refid="selectBasicNumbersVo"/>
        where en_code = #{enCode}
    </select>

    <insert id="insertBasicNumbers" parameterType="com.cssl.pojo.BasicNumbers" useGeneratedKeys="true" keyProperty="enCode">
        insert into basic_numbers
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="enForm != null and enForm != ''">en_form,</if>
            <if test="enPrefix != null and enPrefix != ''">en_prefix,</if>
            <if test="enTime != null and enTime != ''">en_time,</if>
            <if test="enNum != null">en_num,</if>
            <if test="enStep != null">en_step,</if>
            <if test="enFlushed != null">en_flushed,</if>
            <if test="enRules != null">en_rules,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="isDel != null">is_del,</if>
            <if test="maxNum != null">max_num,</if>
            <if test="lastDate != null">last_date,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="enForm != null and enForm != ''">#{enForm},</if>
            <if test="enPrefix != null and enPrefix != ''">#{enPrefix},</if>
            <if test="enTime != null and enTime != ''">#{enTime},</if>
            <if test="enNum != null">#{enNum},</if>
            <if test="enStep != null">#{enStep},</if>
            <if test="enFlushed != null">#{enFlushed},</if>
            <if test="enRules != null">#{enRules},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="maxNum != null">#{maxNum},</if>
            <if test="lastDate != null">#{lastDate},</if>
        </trim>
    </insert>

    <update id="updateBasicNumbers" parameterType="com.cssl.pojo.BasicNumbers">
        update basic_numbers
        <trim prefix="SET" suffixOverrides=",">
            <if test="enForm != null and enForm != ''">en_form = #{enForm},</if>
            <if test="enPrefix != null and enPrefix != ''">en_prefix = #{enPrefix},</if>
            <if test="enTime != null and enTime != ''">en_time = #{enTime},</if>
            <if test="enNum != null">en_num = #{enNum},</if>
            <if test="enStep != null">en_step = #{enStep},</if>
            <if test="enFlushed != null">en_flushed = #{enFlushed},</if>
            <if test="enRules != null">en_rules = #{enRules},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="maxNum != null">max_num = #{maxNum},</if>
            <if test="lastDate != null">last_date = #{lastDate},</if>
        </trim>
        where en_code = #{enCode}
    </update>


    <delete id="deleteBasicNumbersByEnCode" parameterType="Long">
        delete from basic_numbers where en_code = #{enCode}
    </delete>

    <delete id="deleteBasicNumbersByEnCodes" parameterType="String">
        delete from basic_numbers where en_code in
        <foreach item="enCode" collection="array" open="(" separator="," close=")">
            #{enCode}
        </foreach>
    </delete>
</mapper>