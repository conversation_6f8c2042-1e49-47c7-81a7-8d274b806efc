package com.cssl.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cssl.mapper.BasicCustomersMapper;
import com.cssl.mapper.BasicNumbersMapper;
import com.cssl.pojo.BasicCustomers;
import com.cssl.pojo.BasicNumbers;
import com.cssl.service.BasicCustomersService;
import com.cssl.service.BasicNumbersService;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
@Service
public class BasicCustomersServiceImpl extends ServiceImpl<BasicCustomersMapper, BasicCustomers> implements BasicCustomersService {
    @Resource
    private BasicCustomersMapper basicCustomersMapper;

    @Override
    public List<BasicCustomers> listBasicCustomers(BasicCustomers basicCustomers) {
        return basicCustomersMapper.listBasicCustomers(basicCustomers);
    }

    @Override
    public int addBasicCustomers(BasicCustomers basicCustomers) {
        basicCustomers.setCreate_by(SecurityUtils.getUsername());
        basicCustomers.setCreate_time(new Date());
        basicCustomers.setIs_detele("0");
        return basicCustomersMapper.addBasicCustomers(basicCustomers);
    }

    @Override
    public int updateCustomers(BasicCustomers basicCustomers) {
        basicCustomers.setUpdate_by(SecurityUtils.getUsername());
        basicCustomers.setUpdate_time(new Date());
        return basicCustomersMapper.updateCustomers(basicCustomers);
    }

    @Override
    public int delCustomers(Long customer_id) {
        return basicCustomersMapper.delCustomers(customer_id);
    }

    @Override
    public int delBatchCustomers(List<Long> customer_ids) {
        return basicCustomersMapper.delBatchCustomers(customer_ids);
    }
}
