package com.ruoyi.system.api;

import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.system.api.domain.BasicProcessDto;
import com.ruoyi.system.api.factory.RemoteProcessFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

@FeignClient(contextId = "remoteProcessService", value = ServiceNameConstants.LDZL_BASIC_SERVICE, fallbackFactory = RemoteProcessFallbackFactory.class)
public interface RemoteProcessService {
    
    /**
     * 根据工序ID列表查询工序列表
     * @param processIds 工序ID列表
     * @param source 请求来源
     * @return 结果
     */
    @PostMapping("/process/listByIds")
    public R<List<BasicProcessDto>> getProcessListByIds(@RequestBody List<Long> processIds, @RequestHeader(name = "from-source") String source);
} 