-- ============================================
-- 钢材MES系统 - 删除性能优化索引脚本
-- 删除之前创建的所有优化索引
-- ============================================

-- 1. 删除 basic_products 表的索引
DROP INDEX idx_products_subcategory ON basic_products;
DROP INDEX idx_products_delete ON basic_products;
DROP INDEX idx_products_delete_subcategory ON basic_products;
DROP INDEX idx_products_name ON basic_products;
DROP INDEX idx_products_type ON basic_products;

-- 2. 删除 basic_wlflz 表的索引
DROP INDEX idx_wlflz_subcategory ON basic_wlflz;
DROP INDEX idx_wlflz_classification ON basic_wlflz;
DROP INDEX idx_wlflz_delete ON basic_wlflz;
DROP INDEX idx_wlflz_delete_sub_class ON basic_wlflz;

-- 3. 删除 basic_wlfl 表的索引
DROP INDEX idx_wlfl_classification ON basic_wlfl;
DROP INDEX idx_wlfl_delete ON basic_wlfl;
DROP INDEX idx_wlfl_delete_classification ON basic_wlfl;

-- ============================================
-- 验证索引删除情况
-- ============================================

-- 查看 basic_products 表的当前索引
SHOW INDEX FROM basic_products;

-- 查看 basic_wlflz 表的当前索引
SHOW INDEX FROM basic_wlflz;

-- 查看 basic_wlfl 表的当前索引
SHOW INDEX FROM basic_wlfl; 