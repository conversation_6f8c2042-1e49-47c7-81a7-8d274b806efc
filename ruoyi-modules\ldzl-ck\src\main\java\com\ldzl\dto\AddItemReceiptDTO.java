package com.ldzl.dto;

import com.ldzl.pojo.CkItemRecpt;
import com.ldzl.pojo.CkItemRecptLine;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 添加采购入库单
 */
@Data
public class AddItemReceiptDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 接收采购入库单
     */
    private CkItemRecpt itemRecpt;

    /**
     * 接收采购入库单行
     */
    private List<CkItemRecptLine> listItemRecptLine;
}
