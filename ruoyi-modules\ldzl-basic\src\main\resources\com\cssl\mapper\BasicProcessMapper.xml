<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cssl.mapper.BasicProcessMapper">
    <resultMap id="selectBasicProcess" type="com.cssl.pojo.BasicProcess">
        <id column="process_id" property="process_id"/>
        <collection property="basicProcessdetailsList" ofType="com.cssl.pojo.BasicProcessdetails">
            <id column="processdetails_id" property="processdetails_id"/>
        </collection>

    </resultMap>
    <insert id="addBasicProcess" parameterType="com.cssl.pojo.BasicProcess" useGeneratedKeys="true" keyProperty="process_id">
        insert into basic_process
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="process_code !=null and process_code !=''">process_code,</if>
            <if test="process_name !=null and process_name !=''">process_name,</if>
            <if test="process_status !=null ">process_status,</if>
            <if test="process_description !=null and process_description !=''">process_description,</if>
            <if test="remarks !=null and remarks !=''">remarks,</if>
            <if test="is_delete!=null ">is_delete,</if>
            <if test="create_by!=null and create_by !='' ">create_by,</if>
            <if test="create_time!=null ">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="process_code !=null and process_code !=''">#{process_code},</if>
            <if test="process_name !=null and process_name !=''">#{process_name},</if>
            <if test="process_status !=null ">#{process_status},</if>
            <if test="process_description !=null and process_description !=''">#{process_description},</if>
            <if test="remarks !=null and remarks !=''">#{remarks},</if>
            <if test="is_delete!=null ">#{is_delete},</if>
            <if test="create_by!=null and create_by !='' ">#{create_by},</if>
            <if test="create_time!=null ">#{create_time},</if>
        </trim>
    </insert>

    <update id="updateBasicProcess">
        update basic_process
        <trim prefix="SET" suffixOverrides=",">
            <if test="process_name !=null and process_name !=''">process_name=#{process_name},</if>
            <if test="process_status !=null ">process_status=#{process_status},</if>
            <if test="process_description !=null and process_description !=''">process_description=#{process_description},</if>
            <if test="remarks !=null and remarks !=''">remarks=#{remarks},</if>
            <if test="is_delete!=null ">is_delete=#{is_delete},</if>
            <if test="update_by!=null and update_by !='' ">update_by=#{update_by},</if>
            <if test="update_time!=null ">update_time=#{update_time},</if>
        </trim>
        where process_id=#{process_id}
    </update>

    <update id="delBasicProcess">
        update basic_process
        <trim prefix="SET" suffixOverrides=",">
            <if test="is_delete !=null">is_delete=1</if>
        </trim>
        where process_id = #{process_id}
    </update>
    <update id="delBatchBasicProcess">
        update basic_process set is_delete = '1' where process_id in
        <foreach item="processIds" collection="list" open="(" separator="," close=")">
            #{processIds}
        </foreach>
    </update>

    <select id="listBasicProcess" resultType="com.cssl.pojo.BasicProcess">
        SELECT * from basic_process where is_delete=0
        <if test="process_name !=null and process_name != ''">and process_name like CONCAT('%',#{process_name},'%')</if>
        <if test="process_status !=null ">and process_status=#{process_status}</if>
        <if test="process_id !=null and process_id !=0">and process_id=#{process_id}</if>
    </select>
</mapper>