package com.cssl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cssl.pojo.BasicTeam;

import java.util.List;

public interface BasicTeamMapper extends BaseMapper<BasicTeam> {
    //查询班组信息
    public List<BasicTeam> listBasicTeam(BasicTeam basicTeam);

    //添加班组
    public int insertBasicTeam(BasicTeam basicTeam);

    //修改班组信息
    public int updateBasicTeam(BasicTeam basicTeam);

    //删除班组
    public int delBasicTeam(Long team_id);

    //批量删除
    public int delBatchBasicTeam(List<Long> team_ids);


}
