package com.cssl.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cssl.mapper.BasicWlglMapper;
import com.cssl.mapper.BasicWorkshopMapper;
import com.cssl.pojo.BasicWlgl;
import com.cssl.pojo.BasicWorkshop;
import com.cssl.service.BasicFactoryService;
import com.cssl.service.BasicWlglService;
import com.cssl.service.BasicWorkshopService;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class BasicWorkshopServiceImpl extends ServiceImpl<BasicWorkshopMapper, BasicWorkshop> implements BasicWorkshopService {
    @Resource
    private BasicWorkshopMapper basicWorkshopMapper;
    @Override
    public List<BasicWorkshop> listBasicWorkshop(BasicWorkshop basicWorkshop) {
        return basicWorkshopMapper.listBasicWorkshop(basicWorkshop);
    }

    @Override
    public int addBasicWorkshop(BasicWorkshop basicWorkshop) {
        basicWorkshop.setCreate_by(SecurityUtils.getUsername());
        basicWorkshop.setCreate_time(new Date());
        basicWorkshop.setIs_delete("0");
        return basicWorkshopMapper.addBasicWorkshop(basicWorkshop);
    }

    @Override
    public int updateBasicWorkshop(BasicWorkshop basicWorkshop) {
        basicWorkshop.setUpdate_by(SecurityUtils.getUsername());
        basicWorkshop.setUpdate_time(new Date());
        return basicWorkshopMapper.updateBasicWorkshop(basicWorkshop);
    }

    @Override
    public int delBasicWorkshop(Long workshop_id) {
        return basicWorkshopMapper.delBasicWorkshop(workshop_id);
    }
}
