{"remainingRequest": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\src\\views\\sc\\plan\\edit_plan.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\src\\views\\sc\\plan\\edit_plan.vue", "mtime": 1753787212944}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\babel.config.js", "mtime": 1749629472348}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751945356000}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751945361444}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751945356000}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751945364156}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_productionPlan", "require", "_product", "_numbers", "_Pagination", "_interopRequireDefault", "_DictTag", "name", "dicts", "components", "Pagination", "DictTag", "data", "title", "isEdit", "form", "planCode", "planName", "sourceType", "orderCode", "productId", "undefined", "productName", "productCode", "specification", "productType", "unit", "plannedQty", "planStartTime", "planEndTime", "requiredDate", "remark", "orderQty", "isSystemCode", "rules", "required", "message", "trigger", "max", "validator", "validatePlannedQty", "validateStartTime", "validateEndTime", "validateRequiredDate", "productOptions", "productLoading", "sourceTypeOptions", "dict<PERSON><PERSON>l", "dict<PERSON><PERSON>ue", "fileList", "productDialogVisible", "productQuery", "pageNum", "pageSize", "keyword", "type", "property", "productList", "productTotal", "selectedProduct", "bomDialogVisible", "b<PERSON><PERSON><PERSON><PERSON>", "bomList", "bomTotal", "bomLoading", "selectedBom", "selectedBomId", "bomDetailList", "created", "$route", "query", "getPlanData", "generatePlanCode", "methods", "_this", "getProductionPlan", "then", "response", "productionOrderId", "getOrderQtyInfo", "loadAssociatedBom", "_this2", "Promise", "resolve", "_interopRequireWildcard2", "default", "api", "getProductionOrderDetails", "code", "products", "product", "find", "p", "qtyNum", "catch", "console", "error", "_this3", "listBomsByProductId", "rows", "length", "activeBom", "b", "bom_status", "getBomDetail", "_this4", "getAutoNumbers", "msg", "$message", "handleSystemCodeChange", "val", "triggerUpload", "$refs", "upload", "$el", "click", "openProductSelection", "warning", "getProductList", "_this5", "listProducts", "productUnit", "productProperty", "total", "product_id", "product_code", "product_name", "product_sfn", "product_type", "product_unit", "product_property", "searchProducts", "resetProduct<PERSON>uery", "handleProductSelectionChange", "selection", "handleProductCurrentChange", "currentPage", "handleProductSizeChange", "size", "confirmProductSelect", "formatProductType", "row", "column", "option", "dict", "item", "selectBom", "getBomList", "_this6", "info", "defaultBom", "handleBomSelect", "bom_id", "confirmBomSelect", "_this7", "findBomDetails", "log", "clearSelectedBom", "beforeUpload", "file", "isValidType", "test", "isLt10M", "uploadFile", "options", "push", "url", "URL", "createObjectURL", "onSuccess", "handleRemove", "index", "indexOf", "splice", "submitForm", "_this8", "validate", "valid", "validateDateLogic", "apiCall", "updateProductionPlan", "addProductionPlan", "$modal", "msgSuccess", "cancel", "msgError", "$router", "path", "rule", "value", "callback", "today", "Date", "setHours", "startDate", "Error", "endDate", "triggerFieldValidation", "fieldName", "_this9", "$nextTick", "validateField", "Number", "concat"], "sources": ["src/views/sc/plan/edit_plan.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"form-container\">\r\n      <!-- 基础信息区 -->\r\n      <el-tabs type=\"border-card\">\r\n        <el-tab-pane>\r\n          <span slot=\"label\"><i class=\"el-icon-date\"></i> 基础信息</span>\r\n          <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"isEdit ? 12 : 8\">\r\n                <el-form-item label=\"计划编号\" prop=\"planCode\" required>\r\n                  <el-input v-model=\"form.planCode\" placeholder=\"请输入\" :disabled=\"isSystemCode || isEdit\"></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"4\" v-if=\"!isEdit\">\r\n                <el-switch\r\n                  v-model=\"isSystemCode\"\r\n                  active-text=\"系统编号\"\r\n                  inactive-text=\"\"\r\n                  style=\"margin-top: 13px;\"\r\n                  @change=\"handleSystemCodeChange\"\r\n                ></el-switch>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"计划名称\" prop=\"planName\" required>\r\n                  <el-input v-model=\"form.planName\" placeholder=\"请输入\"></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"来源类型\" prop=\"sourceType\" required>\r\n                  <el-select v-model=\"form.sourceType\" placeholder=\"生产订单\" style=\"width: 100%\">\r\n                    <el-option\r\n                      v-for=\"item in sourceTypeOptions\"\r\n                      :key=\"item.dictValue\"\r\n                      :label=\"item.dictLabel\"\r\n                      :value=\"item.dictValue\"\r\n                    ></el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"订单编号\" prop=\"orderCode\">\r\n                  <el-input v-model=\"form.orderCode\" placeholder=\"请输入\" :disabled=\"isEdit\"></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"成品名称\" prop=\"productName\">\r\n                  <el-input\r\n                    placeholder=\"请选择成品\"\r\n                    v-model=\"form.productName\"\r\n                    class=\"input-with-select\"\r\n                  >\r\n                    <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"openProductSelection\"></el-button>\r\n                  </el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"成品编号\" prop=\"productCode\">\r\n                  <el-input v-model=\"form.productCode\" placeholder=\"请输入\" disabled></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"规格型号\" prop=\"specification\">\r\n                  <el-input v-model=\"form.specification\" placeholder=\"请输入\" disabled></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"成品类型\" prop=\"productType\">\r\n                  <dict-tag :options=\"dict.type.product_type\" :value=\"form.productType\" />\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"单位\" prop=\"unit\">\r\n                  <el-input v-model=\"form.unit\" placeholder=\"请输入\" disabled></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"计划数量\" prop=\"plannedQty\" required>\r\n                  <el-input-number v-model=\"form.plannedQty\" :min=\"1\" controls-position=\"right\" style=\"width: 100%\"></el-input-number>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"开工时间\" prop=\"planStartTime\">\r\n                  <el-date-picker\r\n                    v-model=\"form.planStartTime\"\r\n                    type=\"date\"\r\n                    placeholder=\"请选择日期\"\r\n                    style=\"width: 100%\"\r\n                    value-format=\"yyyy-MM-dd\"\r\n                  ></el-date-picker>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"完工时间\" prop=\"planEndTime\">\r\n                  <el-date-picker\r\n                    v-model=\"form.planEndTime\"\r\n                    type=\"date\"\r\n                    placeholder=\"请选择日期\"\r\n                    style=\"width: 100%\"\r\n                    value-format=\"yyyy-MM-dd\"\r\n                  ></el-date-picker>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"需求日期\" prop=\"requiredDate\">\r\n                  <el-date-picker\r\n                    v-model=\"form.requiredDate\"\r\n                    type=\"date\"\r\n                    placeholder=\"请选择日期\"\r\n                    style=\"width: 100%\"\r\n                    value-format=\"yyyy-MM-dd\"\r\n                  ></el-date-picker>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"备注\" prop=\"remark\">\r\n                  <el-input\r\n                    type=\"textarea\"\r\n                    v-model=\"form.remark\"\r\n                    placeholder=\"请输入\"\r\n                    :rows=\"4\"\r\n                  ></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"附件\" prop=\"attachment\">\r\n                  <div class=\"upload-container\" @click=\"triggerUpload\">\r\n                    <el-upload\r\n                      ref=\"upload\"\r\n                      class=\"upload-hidden\"\r\n                      action=\"#\"\r\n                      :http-request=\"uploadFile\"\r\n                      :file-list=\"fileList\"\r\n                      :before-upload=\"beforeUpload\"\r\n                      :on-remove=\"handleRemove\"\r\n                      multiple\r\n                      drag\r\n                    >\r\n                      <div class=\"upload-area\">\r\n                        <i class=\"el-icon-upload\"></i>\r\n                        <div class=\"upload-text\">点击或者拖动文件到虚线框内上传</div>\r\n                        <div class=\"upload-hint\">支持 docx, xls, PDF, rar, zip, PNG, JPG 等类型的文件</div>\r\n                      </div>\r\n                    </el-upload>\r\n                  </div>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-divider>\r\n              <span class=\"bom-title\">BOM组成</span>\r\n            </el-divider>\r\n            \r\n            <el-row>\r\n              <el-col :span=\"24\">\r\n                <div class=\"bom-container\">\r\n                  <div class=\"folder-icon\" v-if=\"!selectedBom\">\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"60\" height=\"60\" viewBox=\"0 0 24 24\" fill=\"#DCDFE6\">\r\n                      <path d=\"M10 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2h-8l-2-2z\"/>\r\n                    </svg>\r\n                  </div>\r\n                  <div class=\"bom-text\" v-if=\"!selectedBom\">暂无数据</div>\r\n                  <div class=\"bom-info\" v-else>\r\n                    <div class=\"bom-header\">\r\n                      <div class=\"bom-title-info\">\r\n                        <span>BOM编号：{{ selectedBom.bom_code }}</span>\r\n                        <span>版本号：{{ selectedBom.bom_version }}</span>\r\n                      </div>\r\n                      <el-button type=\"text\" icon=\"el-icon-delete\" @click=\"clearSelectedBom\">清除</el-button>\r\n                    </div>\r\n                    <el-table\r\n                      :data=\"bomDetailList\"\r\n                      border\r\n                      size=\"small\"\r\n                      style=\"width: 100%\"\r\n                      class=\"bom-detail-table\"\r\n                    >\r\n                      <el-table-column label=\"序号\" type=\"index\" width=\"50\" align=\"center\"></el-table-column>\r\n                      <el-table-column prop=\"material_code\" label=\"物料编码\" min-width=\"120\" align=\"center\"></el-table-column>\r\n                      <el-table-column prop=\"material_name\" label=\"物料名称\" min-width=\"150\" align=\"center\"></el-table-column>\r\n                      <el-table-column prop=\"specification\" label=\"规格型号\" min-width=\"100\" align=\"center\"></el-table-column>\r\n                      <el-table-column prop=\"unit\" label=\"单位\" width=\"60\" align=\"center\"></el-table-column>\r\n                      <el-table-column prop=\"quantity\" label=\"用量\" width=\"80\" align=\"center\"></el-table-column>\r\n                      <el-table-column prop=\"remark\" label=\"备注\" min-width=\"120\" align=\"center\"></el-table-column>\r\n                    </el-table>\r\n                  </div>\r\n                  <div class=\"bom-action\">\r\n                    <el-tooltip :disabled=\"form.productId\" content=\"请先选择成品!\" placement=\"right\" effect=\"light\">\r\n                      <el-button type=\"primary\" class=\"select-bom-button\" @click=\"selectBom\">选择BOM</el-button>\r\n                    </el-tooltip>\r\n                  </div>\r\n                </div>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row>\r\n              <el-col :span=\"24\" style=\"text-align: center; margin-top: 20px;\">\r\n                <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n                <el-button @click=\"cancel\">取 消</el-button>\r\n              </el-col>\r\n            </el-row>\r\n          </el-form>\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n    </div>\r\n    \r\n    <!-- 产品选择对话框 -->\r\n    <el-dialog title=\"选择成品\" :visible.sync=\"productDialogVisible\" width=\"40%\" append-to-body>\r\n      <el-form :model=\"productQuery\" ref=\"productQueryForm\" :inline=\"true\" class=\"demo-form-inline\" size=\"small\">\r\n        <el-form-item>\r\n          <el-input v-model=\"productQuery.keyword\" placeholder=\"请输入产品编号/名称\" clearable style=\"width: 180px;\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-select v-model=\"productQuery.unit\" placeholder=\"请选择单位\" clearable style=\"width: 120px;\">\r\n            <el-option label=\"个\" value=\"个\"></el-option>\r\n            <el-option label=\"件\" value=\"件\"></el-option>\r\n            <el-option label=\"台\" value=\"台\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-select v-model=\"productQuery.type\" placeholder=\"请选择类型\" clearable style=\"width: 120px;\">\r\n            <el-option label=\"成品\" value=\"成品\"></el-option>\r\n            <el-option label=\"半成品\" value=\"半成品\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-select v-model=\"productQuery.property\" placeholder=\"请选择产品属性\" clearable style=\"width: 120px;\">\r\n            <el-option label=\"自制\" value=\"自制\"></el-option>\r\n            <el-option label=\"外购\" value=\"外购\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"small\" @click=\"searchProducts\">查询</el-button>\r\n          <el-button icon=\"el-icon-refresh\" size=\"small\" @click=\"resetProductQuery\">重置</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n      \r\n      <el-table\r\n        v-loading=\"productLoading\"\r\n        :data=\"productList\"\r\n        border\r\n        size=\"small\"\r\n        style=\"width: 100%\"\r\n        @selection-change=\"handleProductSelectionChange\"\r\n        height=\"300\"\r\n      >\r\n        <el-table-column type=\"selection\" width=\"40\" align=\"center\"></el-table-column>\r\n        <el-table-column label=\"序号\" type=\"index\" width=\"40\" align=\"center\"></el-table-column>\r\n        <el-table-column prop=\"product_code\" label=\"产品编号\" width=\"90\" align=\"center\"></el-table-column>\r\n        <el-table-column prop=\"product_name\" label=\"产品名称\" min-width=\"120\" align=\"center\"></el-table-column>\r\n        <el-table-column prop=\"product_sfn\" label=\"规格型号\" min-width=\"90\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <span :style=\"{color: '#1890ff'}\">{{ scope.row.product_sfn }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"product_unit\" label=\"单位\" width=\"60\" align=\"center\"></el-table-column>\r\n        <el-table-column prop=\"product_type\" label=\"产品类型\" width=\"70\" align=\"center\" :formatter=\"formatProductType\"></el-table-column>\r\n      </el-table>\r\n      \r\n      <div class=\"pagination-container\">\r\n        <span class=\"total-text\">共 {{ productTotal }} 条</span>\r\n        <div class=\"pagination-wrapper\">\r\n          <span class=\"page-size\">\r\n            <el-select v-model=\"productQuery.pageSize\" size=\"mini\" @change=\"handleProductSizeChange\" style=\"width: 80px;\">\r\n              <el-option\r\n                v-for=\"item in [10, 20, 30, 50]\"\r\n                :key=\"item\"\r\n                :label=\"`${item}条/页`\"\r\n                :value=\"item\">\r\n              </el-option>\r\n            </el-select>\r\n          </span>\r\n          <el-pagination\r\n            small\r\n            background\r\n            @current-change=\"handleProductCurrentChange\"\r\n            :current-page=\"productQuery.pageNum\"\r\n            :page-size=\"productQuery.pageSize\"\r\n            layout=\"prev, pager, next, jumper\"\r\n            :pager-count=\"5\"\r\n            :total=\"productTotal\">\r\n          </el-pagination>\r\n        </div>\r\n      </div>\r\n      \r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button size=\"small\" @click=\"productDialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" size=\"small\" @click=\"confirmProductSelect\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    \r\n    <!-- BOM选择对话框 -->\r\n    <el-dialog title=\"选择BOM\" :visible.sync=\"bomDialogVisible\" width=\"40%\" append-to-body>\r\n      <div class=\"bom-dialog-header\">\r\n        <div class=\"product-info\">\r\n          <span>产品名称：{{ form.productName }}</span>\r\n          <span>产品编号：{{ form.productCode }}</span>\r\n          <span>规格型号：{{ form.specification }}</span>\r\n          <span>单位：{{ form.unit }}</span>\r\n        </div>\r\n      </div>\r\n      \r\n      <el-table\r\n        v-loading=\"bomLoading\"\r\n        :data=\"bomList\"\r\n        border\r\n        style=\"width: 100%\"\r\n        @row-click=\"handleBomSelect\"\r\n        highlight-current-row\r\n        size=\"small\"\r\n        height=\"300\"\r\n      >\r\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-radio v-model=\"selectedBomId\" :label=\"scope.row.bom_id\" @change=\"handleBomSelect(scope.row)\">&nbsp;</el-radio>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"序号\" type=\"index\" width=\"55\" align=\"center\"></el-table-column>\r\n        <el-table-column prop=\"bom_code\" label=\"BOM编号\" min-width=\"150\" align=\"center\"></el-table-column>\r\n        <el-table-column prop=\"bom_version\" label=\"版本号\" width=\"100\" align=\"center\"></el-table-column>\r\n        <el-table-column prop=\"bom_status\" label=\"默认BOM\" width=\"100\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ scope.row.bom_status === '1' ? '是' : '否' }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"bom_output\" label=\"日产量\" width=\"100\" align=\"center\"></el-table-column>\r\n      </el-table>\r\n      \r\n      <pagination\r\n        v-show=\"bomTotal > 0\"\r\n        :total=\"bomTotal\"\r\n        :page.sync=\"bomQuery.pageNum\"\r\n        :limit.sync=\"bomQuery.pageSize\"\r\n        @pagination=\"getBomList\"\r\n      />\r\n      \r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"bomDialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmBomSelect\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getProductionPlan, updateProductionPlan, addProductionPlan } from \"@/api/sc/productionPlan\";\r\nimport { listProducts, listBomsByProductId, findBomDetails } from \"@/api/basic/product\";\r\nimport { getAutoNumbers } from \"@/api/basic/numbers\";\r\nimport Pagination from \"@/components/Pagination\";\r\nimport DictTag from \"@/components/DictTag\";\r\n\r\nexport default {\r\n  name: \"EditPlan\",\r\n  dicts: ['product_type'],\r\n  components: {\r\n    Pagination,\r\n    DictTag\r\n  },\r\n  data() {\r\n    return {\r\n      // 页面标题\r\n      title: \"修改生产计划\",\r\n      // 是否为修改模式\r\n      isEdit: true,\r\n      // 表单数据\r\n      form: {\r\n        planCode: \"\",\r\n        planName: \"\",\r\n        sourceType: \"PRODUCTION_ORDER\",\r\n        orderCode: \"\",\r\n        productId: undefined,\r\n        productName: \"\",\r\n        productCode: \"\",\r\n        specification: \"\",\r\n        productType: \"\",\r\n        unit: \"\",\r\n        plannedQty: 1,\r\n        planStartTime: \"\",\r\n        planEndTime: \"\",\r\n        requiredDate: \"\",\r\n        remark: \"\",\r\n        orderQty: 0  // 添加订单数量字段\r\n      },\r\n      // 是否使用系统编号\r\n      isSystemCode: true,\r\n      // 表单验证规则\r\n      rules: {\r\n        planName: [\r\n          { required: true, message: \"计划名称不能为空\", trigger: \"blur\" },\r\n          { max: 50, message: \"长度不能超过50个字符\", trigger: \"blur\" }\r\n        ],\r\n        sourceType: [\r\n          { required: true, message: \"来源类型不能为空\", trigger: \"change\" }\r\n        ],\r\n        productName: [\r\n          { required: true, message: \"请选择产品\", trigger: \"change\" }\r\n        ],\r\n        plannedQty: [\r\n          { required: true, message: \"计划数量不能为空\", trigger: \"blur\" },\r\n          { validator: this.validatePlannedQty, trigger: \"blur\" }\r\n        ],\r\n        planStartTime: [\r\n          { validator: this.validateStartTime, trigger: \"change\" }\r\n        ],\r\n        planEndTime: [\r\n          { validator: this.validateEndTime, trigger: \"change\" }\r\n        ],\r\n        requiredDate: [\r\n          { validator: this.validateRequiredDate, trigger: \"change\" }\r\n        ]\r\n      },\r\n      // 产品下拉选项\r\n      productOptions: [],\r\n      // 产品加载状态\r\n      productLoading: false,\r\n      // 来源类型选项\r\n      sourceTypeOptions: [\r\n        { dictLabel: \"生产订单\", dictValue: \"PRODUCTION_ORDER\" }\r\n      ],\r\n      // 上传文件列表\r\n      fileList: [],\r\n      \r\n      // 产品选择对话框\r\n      productDialogVisible: false,\r\n      productQuery: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        keyword: \"\",\r\n        unit: \"\",\r\n        type: \"\",\r\n        property: \"\"\r\n      },\r\n      productList: [],\r\n      productTotal: 0,\r\n      selectedProduct: null,\r\n      \r\n      // BOM选择对话框\r\n      bomDialogVisible: false,\r\n      bomQuery: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        productId: undefined\r\n      },\r\n      bomList: [],\r\n      bomTotal: 0,\r\n      bomLoading: false,\r\n      selectedBom: null,\r\n      selectedBomId: null,\r\n      bomDetailList: [],\r\n    };\r\n  },\r\n  created() {\r\n    const planCode = this.$route.query.planCode;\r\n    if (planCode) {\r\n      this.isEdit = true;\r\n      this.title = \"修改生产计划\";\r\n      this.getPlanData(planCode);\r\n    } else {\r\n      this.isEdit = false;\r\n      this.title = \"新增生产计划\";\r\n      this.generatePlanCode();\r\n    }\r\n  },\r\n  methods: {\r\n    // 获取计划数据\r\n    getPlanData(planCode) {\r\n      getProductionPlan(planCode).then(response => {\r\n        this.form = response.data;\r\n\r\n        // 如果是生产订单来源，获取订单数量信息\r\n        if (this.form.sourceType === 'PRODUCTION_ORDER' && this.form.productionOrderId) {\r\n          this.getOrderQtyInfo(this.form.productionOrderId, this.form.productId);\r\n        }\r\n\r\n        // 如果有关联的产品，则自动加载其BOM信息\r\n        if (this.form.productId) {\r\n          this.loadAssociatedBom();\r\n        }\r\n      });\r\n    },\r\n\r\n    // 获取订单数量信息\r\n    getOrderQtyInfo(productionOrderId, productId) {\r\n      import(\"@/api/sc/productionOrder\").then(api => {\r\n        api.getProductionOrderDetails(productionOrderId).then(response => {\r\n          if (response.code === 200 && response.data && response.data.products) {\r\n            const product = response.data.products.find(p => p.productId === productId);\r\n            if (product) {\r\n              this.form.orderQty = product.qtyNum || 0;\r\n            }\r\n          }\r\n        }).catch(() => {\r\n          console.error('获取订单数量信息失败');\r\n        });\r\n      });\r\n    },\r\n\r\n    // 加载关联的BOM\r\n    loadAssociatedBom() {\r\n      listBomsByProductId(this.form.productId).then(response => {\r\n        if (response.code === 200 && response.rows && response.rows.length > 0) {\r\n          // 查找默认的BOM (status '1')\r\n          const activeBom = response.rows.find(b => b.bom_status === '1');\r\n          if (activeBom) {\r\n            this.selectedBom = activeBom;\r\n            this.getBomDetail(); // 加载BOM详情\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    // 生成计划编号\r\n    generatePlanCode() {\r\n      getAutoNumbers(6).then(response => {\r\n        if (response.code === 200) {\r\n          this.form.planCode = response.msg;\r\n        } else {\r\n          this.$message.error('获取计划编号失败');\r\n        }\r\n      }).catch(() => {\r\n        this.$message.error('获取计划编号失败');\r\n      });\r\n    },\r\n    \r\n    // 处理系统编号开关变化\r\n    handleSystemCodeChange(val) {\r\n      if (val) {\r\n        // 如果开启系统编号，则生成编号\r\n        this.generatePlanCode();\r\n      } else {\r\n        // 如果关闭系统编号，则清空编号\r\n        this.form.planCode = '';\r\n      }\r\n    },\r\n    \r\n    // 触发上传\r\n    triggerUpload() {\r\n      this.$refs.upload.$el.click();\r\n    },\r\n    \r\n    // 打开产品选择弹窗\r\n    openProductSelection() {\r\n      // 修改模式下不允许更换产品\r\n      if (this.isEdit) {\r\n        this.$message.warning(\"修改模式下不允许更换产品。\");\r\n        return;\r\n      }\r\n      this.productDialogVisible = true;\r\n      this.getProductList();\r\n    },\r\n    \r\n    // 获取产品列表\r\n    getProductList() {\r\n      this.productLoading = true;\r\n      listProducts({\r\n        pageNum: this.productQuery.pageNum,\r\n        pageSize: this.productQuery.pageSize,\r\n        keyword: this.productQuery.keyword,\r\n        productUnit: this.productQuery.unit,\r\n        productType: this.productQuery.type,\r\n        productProperty: this.productQuery.property\r\n      }).then(response => {\r\n        this.productLoading = false;\r\n        if (response.code === 200) {\r\n          this.productList = response.rows;\r\n          this.productTotal = response.total;\r\n        }\r\n      }).catch(() => {\r\n        this.productLoading = false;\r\n        // 模拟数据\r\n        this.productList = [\r\n          { product_id: 1, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' },\r\n          { product_id: 2, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' },\r\n          { product_id: 3, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' },\r\n          { product_id: 4, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' },\r\n          { product_id: 5, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' }\r\n        ];\r\n        this.productTotal = 50;\r\n      });\r\n    },\r\n    \r\n    // 搜索产品\r\n    searchProducts() {\r\n      this.productQuery.pageNum = 1;\r\n      this.getProductList();\r\n    },\r\n    \r\n    // 重置产品查询条件\r\n    resetProductQuery() {\r\n      this.productQuery = {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        keyword: \"\",\r\n        unit: \"\",\r\n        type: \"\",\r\n        property: \"\"\r\n      };\r\n      this.getProductList();\r\n    },\r\n    \r\n    // 处理产品表格选择变化\r\n    handleProductSelectionChange(selection) {\r\n      if (selection.length > 0) {\r\n        this.selectedProduct = selection[0];\r\n      } else {\r\n        this.selectedProduct = null;\r\n      }\r\n    },\r\n    \r\n    // 处理产品页码变化\r\n    handleProductCurrentChange(currentPage) {\r\n      this.productQuery.pageNum = currentPage;\r\n      this.getProductList();\r\n    },\r\n    \r\n    // 处理产品每页条数变化\r\n    handleProductSizeChange(size) {\r\n      this.productQuery.pageSize = size;\r\n      this.productQuery.pageNum = 1;\r\n      this.getProductList();\r\n    },\r\n    \r\n    // 确认产品选择\r\n    confirmProductSelect() {\r\n      if (this.selectedProduct) {\r\n        this.form.productId = this.selectedProduct.product_id;\r\n        this.form.productName = this.selectedProduct.product_name;\r\n        this.form.productCode = this.selectedProduct.product_code;\r\n        this.form.specification = this.selectedProduct.product_sfn;\r\n        this.form.productType = this.formatProductType(this.selectedProduct);\r\n        this.form.unit = this.selectedProduct.product_unit;\r\n        this.productDialogVisible = false;\r\n        \r\n        // 清空已选BOM\r\n        this.selectedBom = null;\r\n        this.selectedBomId = null;\r\n      } else {\r\n        this.$message.warning('请选择一个产品！');\r\n      }\r\n    },\r\n    \r\n    // 格式化产品类型\r\n    formatProductType(row, column) {\r\n      const type = row.product_type;\r\n      const option = this.dict.type.product_type.find(item => item.dictValue == type);\r\n      return option ? option.dictLabel : type;\r\n    },\r\n    \r\n    // 选择BOM\r\n    selectBom() {\r\n      if (!this.form.productId) {\r\n        this.$message.warning('请先选择成品！');\r\n        return;\r\n      }\r\n      this.bomDialogVisible = true;\r\n      this.getBomList();\r\n    },\r\n    \r\n    // 获取BOM列表\r\n    getBomList() {\r\n      this.bomLoading = true;\r\n      listBomsByProductId(this.form.productId).then(response => {\r\n        this.bomLoading = false;\r\n        if (response.code === 200) {\r\n          this.bomList = response.rows;\r\n          this.bomTotal = response.total;\r\n          if (!this.bomList || this.bomList.length === 0) {\r\n            this.$message.info(\"未找到该产品的BOM信息\");\r\n          } else {\r\n            // 如果有默认BOM，则自动选中\r\n            const defaultBom = this.bomList.find(b => b.bom_status === '1');\r\n            if (defaultBom) {\r\n              this.handleBomSelect(defaultBom);\r\n            }\r\n          }\r\n        } else {\r\n          this.bomList = [];\r\n          this.bomTotal = 0;\r\n        }\r\n      }).catch(() => {\r\n        this.bomLoading = false;\r\n        this.$message.error('获取BOM列表失败');\r\n      });\r\n    },\r\n    \r\n    // 处理BOM行选择\r\n    handleBomSelect(row) {\r\n      this.selectedBom = row;\r\n      this.selectedBomId = row.bom_id;\r\n    },\r\n    \r\n    // 确认BOM选择\r\n    confirmBomSelect() {\r\n      if (this.selectedBom) {\r\n        this.bomDialogVisible = false;\r\n        // 获取BOM详情\r\n        this.getBomDetail();\r\n      } else {\r\n        this.$message.warning('请选择一个BOM！');\r\n      }\r\n    },\r\n    \r\n    // 获取BOM详情\r\n    getBomDetail() {\r\n      findBomDetails(this.selectedBom.bom_id).then(response => {\r\n        console.log(\"成功获取BOM详情响应:\", response);\r\n        if (response && response.code === 200) {\r\n          this.bomDetailList = response.rows;\r\n        } else {\r\n          this.bomDetailList = [];\r\n          this.$message.error(\"获取BOM详情失败: \" + (response ? response.msg : '无响应'));\r\n        }\r\n      }).catch(error => {\r\n        console.error(\"获取BOM详情接口调用失败:\", error);\r\n        this.$message.error(\"获取BOM详情接口调用失败\");\r\n        this.bomDetailList = [];\r\n      });\r\n    },\r\n    \r\n    // 清除已选BOM\r\n    clearSelectedBom() {\r\n      this.selectedBom = null;\r\n      this.selectedBomId = null;\r\n      this.bomDetailList = [];\r\n    },\r\n    \r\n    // 上传前检查文件类型和大小\r\n    beforeUpload(file) {\r\n      const isValidType = /\\.(doc|docx|xls|xlsx|pdf|rar|zip|png|jpg|jpeg)$/i.test(file.name);\r\n      const isLt10M = file.size / 1024 / 1024 < 10;\r\n\r\n      if (!isValidType) {\r\n        this.$message.error('上传文件格式不支持!');\r\n        return false;\r\n      }\r\n      if (!isLt10M) {\r\n        this.$message.error('上传文件大小不能超过 10MB!');\r\n        return false;\r\n      }\r\n      return true;\r\n    },\r\n    \r\n    // 上传文件处理\r\n    uploadFile(options) {\r\n      // 这里应该调用实际的文件上传API\r\n      console.log('文件上传:', options.file);\r\n      // 假设上传成功\r\n      this.fileList.push({\r\n        name: options.file.name,\r\n        url: URL.createObjectURL(options.file)\r\n      });\r\n      options.onSuccess();\r\n    },\r\n    \r\n    // 移除文件\r\n    handleRemove(file) {\r\n      const index = this.fileList.indexOf(file);\r\n      if (index !== -1) {\r\n        this.fileList.splice(index, 1);\r\n      }\r\n    },\r\n    \r\n    // 表单提交\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          // 额外的日期逻辑验证\r\n          if (!this.validateDateLogic()) {\r\n            return;\r\n          }\r\n\r\n          // 根据isEdit标志决定调用哪个API\r\n          const apiCall = this.isEdit ? updateProductionPlan : addProductionPlan;\r\n          \r\n          apiCall(this.form).then(response => {\r\n            if (response.code === 200) {\r\n              this.$modal.msgSuccess(this.isEdit ? \"修改成功\" : \"新增成功\");\r\n              this.cancel();\r\n            } else {\r\n              this.$modal.msgError(response.msg || (this.isEdit ? \"修改失败\" : \"新增失败\"));\r\n            }\r\n          }).catch(() => {\r\n            this.$modal.msgError(\"操作失败，请稍后重试\");\r\n          });\r\n        }\r\n      });\r\n    },\r\n    \r\n    // 取消按钮\r\n    cancel() {\r\n      this.$router.push({ path: \"/sc/plan\" });\r\n    },\r\n\r\n    // 验证开工时间\r\n    validateStartTime(rule, value, callback) {\r\n      if (!value) {\r\n        callback();\r\n        return;\r\n      }\r\n\r\n      const today = new Date();\r\n      today.setHours(0, 0, 0, 0);\r\n      const startDate = new Date(value);\r\n\r\n      // 开工日期不能早于当前日期\r\n      if (startDate < today) {\r\n        callback(new Error('开工日期不能早于当前日期'));\r\n        return;\r\n      }\r\n\r\n      // 如果完工时间已选择，开工时间不能晚于完工时间\r\n      if (this.form.planEndTime) {\r\n        const endDate = new Date(this.form.planEndTime);\r\n        if (startDate > endDate) {\r\n          callback(new Error('开工日期不能晚于完工日期'));\r\n          return;\r\n        }\r\n      }\r\n\r\n      callback();\r\n\r\n      // 触发相关字段的重新验证\r\n      if (this.form.planEndTime) {\r\n        this.triggerFieldValidation('planEndTime');\r\n      }\r\n      if (this.form.requiredDate) {\r\n        this.triggerFieldValidation('requiredDate');\r\n      }\r\n    },\r\n\r\n    // 验证完工时间\r\n    validateEndTime(rule, value, callback) {\r\n      if (!value) {\r\n        callback();\r\n        return;\r\n      }\r\n\r\n      const endDate = new Date(value);\r\n\r\n      // 如果开工时间已选择，完工时间不能早于开工时间\r\n      if (this.form.planStartTime) {\r\n        const startDate = new Date(this.form.planStartTime);\r\n        if (endDate < startDate) {\r\n          callback(new Error('完工日期不能早于开工日期'));\r\n          return;\r\n        }\r\n      }\r\n\r\n      callback();\r\n\r\n      // 触发相关字段的重新验证\r\n      if (this.form.planStartTime) {\r\n        this.triggerFieldValidation('planStartTime');\r\n      }\r\n      if (this.form.requiredDate) {\r\n        this.triggerFieldValidation('requiredDate');\r\n      }\r\n    },\r\n\r\n    // 触发指定字段的验证\r\n    triggerFieldValidation(fieldName) {\r\n      this.$nextTick(() => {\r\n        if (this.$refs.form) {\r\n          this.$refs.form.validateField(fieldName);\r\n        }\r\n      });\r\n    },\r\n\r\n    // 验证计划数量\r\n    validatePlannedQty(rule, value, callback) {\r\n      if (!value) {\r\n        callback();\r\n        return;\r\n      }\r\n\r\n      const plannedQty = Number(value);\r\n      const orderQty = Number(this.form.orderQty);\r\n\r\n      if (plannedQty <= 0) {\r\n        callback(new Error('计划数量必须大于0'));\r\n        return;\r\n      }\r\n\r\n      if (orderQty > 0 && plannedQty > orderQty) {\r\n        callback(new Error(`计划数量不能大于订单数量(${orderQty})`));\r\n        return;\r\n      }\r\n\r\n      callback();\r\n    },\r\n\r\n    // 验证需求日期\r\n    validateRequiredDate(rule, value, callback) {\r\n      if (!value) {\r\n        callback();\r\n        return;\r\n      }\r\n\r\n      const requiredDate = new Date(value);\r\n\r\n      // 需求日期不能早于当前日期\r\n      const today = new Date();\r\n      today.setHours(0, 0, 0, 0);\r\n      if (requiredDate < today) {\r\n        callback(new Error('需求日期不能早于当前日期'));\r\n        return;\r\n      }\r\n\r\n      // 如果完工时间已选择，需求日期不能早于完工时间\r\n      if (this.form.planEndTime) {\r\n        const endDate = new Date(this.form.planEndTime);\r\n        if (requiredDate < endDate) {\r\n          callback(new Error('需求日期不能早于完工日期'));\r\n          return;\r\n        }\r\n      }\r\n\r\n      callback();\r\n\r\n      // 触发相关字段的重新验证\r\n      if (this.form.planEndTime) {\r\n        this.triggerFieldValidation('planEndTime');\r\n      }\r\n    },\r\n\r\n    // 综合日期逻辑验证\r\n    validateDateLogic() {\r\n      const today = new Date();\r\n      today.setHours(0, 0, 0, 0);\r\n\r\n      // 检查开工日期\r\n      if (this.form.planStartTime) {\r\n        const startDate = new Date(this.form.planStartTime);\r\n        if (startDate < today) {\r\n          this.$modal.msgError(\"开工日期不能早于当前日期\");\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 检查开工日期和完工日期的关系\r\n      if (this.form.planStartTime && this.form.planEndTime) {\r\n        const startDate = new Date(this.form.planStartTime);\r\n        const endDate = new Date(this.form.planEndTime);\r\n        if (startDate > endDate) {\r\n          this.$modal.msgError(\"开工日期不能晚于完工日期\");\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 检查需求日期\r\n      if (this.form.requiredDate) {\r\n        const requiredDate = new Date(this.form.requiredDate);\r\n        if (requiredDate < today) {\r\n          this.$modal.msgError(\"需求日期不能早于当前日期\");\r\n          return false;\r\n        }\r\n\r\n        // 需求日期不能早于完工日期\r\n        if (this.form.planEndTime) {\r\n          const endDate = new Date(this.form.planEndTime);\r\n          if (requiredDate < endDate) {\r\n            this.$modal.msgError(\"需求日期不能早于完工日期\");\r\n            return false;\r\n          }\r\n        }\r\n      }\r\n\r\n      return true;\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.form-container {\r\n  background-color: #fff;\r\n  padding: 10px;\r\n}\r\n\r\n.el-tabs--border-card {\r\n  box-shadow: none;\r\n}\r\n\r\n.upload-container {\r\n  width: 100%;\r\n  border: 1px dashed #d9d9d9;\r\n  border-radius: 6px;\r\n  text-align: center;\r\n  padding: 20px 0;\r\n  cursor: pointer;\r\n}\r\n\r\n.upload-container:hover {\r\n  border-color: #409EFF;\r\n}\r\n\r\n.upload-area {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.upload-area .el-icon-upload {\r\n  font-size: 48px;\r\n  color: #c0c4cc;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.upload-text {\r\n  font-size: 14px;\r\n  color: #606266;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.upload-hint {\r\n  font-size: 12px;\r\n  color: #909399;\r\n}\r\n\r\n.input-with-select .el-input-group__append {\r\n  background-color: #fff;\r\n}\r\n\r\n.bom-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.bom-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin: 20px 0;\r\n  padding: 30px 0;\r\n}\r\n\r\n.folder-icon {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.bom-text {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.warning-text {\r\n  color: #E6A23C;\r\n  font-size: 12px;\r\n  margin-left: 10px;\r\n}\r\n\r\n.warning-text i {\r\n  margin-right: 5px;\r\n}\r\n\r\n.upload-hidden {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.upload-hidden >>> .el-upload {\r\n  width: 100%;\r\n}\r\n\r\n.upload-hidden >>> .el-upload-dragger {\r\n  width: 100%;\r\n  height: 100%;\r\n  border: none;\r\n  padding: 0;\r\n  margin: 0;\r\n}\r\n\r\n.bom-dialog-header {\r\n  margin-bottom: 15px;\r\n  padding: 10px;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n}\r\n\r\n.product-info {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.product-info span {\r\n  margin-right: 20px;\r\n  line-height: 30px;\r\n}\r\n\r\n.el-radio {\r\n  margin-right: 0;\r\n}\r\n\r\n.pagination-container {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 10px 0;\r\n  font-size: 12px;\r\n}\r\n\r\n.pagination-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.page-size {\r\n  margin-right: 10px;\r\n}\r\n\r\n.total-text {\r\n  color: #606266;\r\n  font-size: 12px;\r\n}\r\n\r\n.bom-info {\r\n  width: 100%;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.bom-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n  padding: 8px;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n}\r\n\r\n.bom-title-info {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.bom-title-info span {\r\n  margin-right: 20px;\r\n  font-weight: bold;\r\n}\r\n\r\n.bom-detail-table {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.bom-action {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.select-bom-button {\r\n  margin-right: 10px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAiXA,IAAAA,eAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AACA,IAAAG,WAAA,GAAAC,sBAAA,CAAAJ,OAAA;AACA,IAAAK,QAAA,GAAAD,sBAAA,CAAAJ,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAM,IAAA;EACAC,KAAA;EACAC,UAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,OAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,KAAA;MACA;MACAC,MAAA;MACA;MACAC,IAAA;QACAC,QAAA;QACAC,QAAA;QACAC,UAAA;QACAC,SAAA;QACAC,SAAA,EAAAC,SAAA;QACAC,WAAA;QACAC,WAAA;QACAC,aAAA;QACAC,WAAA;QACAC,IAAA;QACAC,UAAA;QACAC,aAAA;QACAC,WAAA;QACAC,YAAA;QACAC,MAAA;QACAC,QAAA;MACA;MACA;MACAC,YAAA;MACA;MACAC,KAAA;QACAjB,QAAA,GACA;UAAAkB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA,EACA;QACAnB,UAAA,GACA;UAAAiB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAf,WAAA,GACA;UAAAa,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAV,UAAA,GACA;UAAAQ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAE,SAAA,OAAAC,kBAAA;UAAAH,OAAA;QAAA,EACA;QACAT,aAAA,GACA;UAAAW,SAAA,OAAAE,iBAAA;UAAAJ,OAAA;QAAA,EACA;QACAR,WAAA,GACA;UAAAU,SAAA,OAAAG,eAAA;UAAAL,OAAA;QAAA,EACA;QACAP,YAAA,GACA;UAAAS,SAAA,OAAAI,oBAAA;UAAAN,OAAA;QAAA;MAEA;MACA;MACAO,cAAA;MACA;MACAC,cAAA;MACA;MACAC,iBAAA,GACA;QAAAC,SAAA;QAAAC,SAAA;MAAA,EACA;MACA;MACAC,QAAA;MAEA;MACAC,oBAAA;MACAC,YAAA;QACAC,OAAA;QACAC,QAAA;QACAC,OAAA;QACA5B,IAAA;QACA6B,IAAA;QACAC,QAAA;MACA;MACAC,WAAA;MACAC,YAAA;MACAC,eAAA;MAEA;MACAC,gBAAA;MACAC,QAAA;QACAT,OAAA;QACAC,QAAA;QACAjC,SAAA,EAAAC;MACA;MACAyC,OAAA;MACAC,QAAA;MACAC,UAAA;MACAC,WAAA;MACAC,aAAA;MACAC,aAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,IAAApD,QAAA,QAAAqD,MAAA,CAAAC,KAAA,CAAAtD,QAAA;IACA,IAAAA,QAAA;MACA,KAAAF,MAAA;MACA,KAAAD,KAAA;MACA,KAAA0D,WAAA,CAAAvD,QAAA;IACA;MACA,KAAAF,MAAA;MACA,KAAAD,KAAA;MACA,KAAA2D,gBAAA;IACA;EACA;EACAC,OAAA;IACA;IACAF,WAAA,WAAAA,YAAAvD,QAAA;MAAA,IAAA0D,KAAA;MACA,IAAAC,iCAAA,EAAA3D,QAAA,EAAA4D,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAA3D,IAAA,GAAA8D,QAAA,CAAAjE,IAAA;;QAEA;QACA,IAAA8D,KAAA,CAAA3D,IAAA,CAAAG,UAAA,2BAAAwD,KAAA,CAAA3D,IAAA,CAAA+D,iBAAA;UACAJ,KAAA,CAAAK,eAAA,CAAAL,KAAA,CAAA3D,IAAA,CAAA+D,iBAAA,EAAAJ,KAAA,CAAA3D,IAAA,CAAAK,SAAA;QACA;;QAEA;QACA,IAAAsD,KAAA,CAAA3D,IAAA,CAAAK,SAAA;UACAsD,KAAA,CAAAM,iBAAA;QACA;MACA;IACA;IAEA;IACAD,eAAA,WAAAA,gBAAAD,iBAAA,EAAA1D,SAAA;MAAA,IAAA6D,MAAA;MACAC,OAAA,CAAAC,OAAA,GAAAP,IAAA;QAAA,WAAAQ,wBAAA,CAAAC,OAAA,EAAApF,OAAA;MAAA,GAAA2E,IAAA,WAAAU,GAAA;QACAA,GAAA,CAAAC,yBAAA,CAAAT,iBAAA,EAAAF,IAAA,WAAAC,QAAA;UACA,IAAAA,QAAA,CAAAW,IAAA,YAAAX,QAAA,CAAAjE,IAAA,IAAAiE,QAAA,CAAAjE,IAAA,CAAA6E,QAAA;YACA,IAAAC,OAAA,GAAAb,QAAA,CAAAjE,IAAA,CAAA6E,QAAA,CAAAE,IAAA,WAAAC,CAAA;cAAA,OAAAA,CAAA,CAAAxE,SAAA,KAAAA,SAAA;YAAA;YACA,IAAAsE,OAAA;cACAT,MAAA,CAAAlE,IAAA,CAAAiB,QAAA,GAAA0D,OAAA,CAAAG,MAAA;YACA;UACA;QACA,GAAAC,KAAA;UACAC,OAAA,CAAAC,KAAA;QACA;MACA;IACA;IAEA;IACAhB,iBAAA,WAAAA,kBAAA;MAAA,IAAAiB,MAAA;MACA,IAAAC,4BAAA,OAAAnF,IAAA,CAAAK,SAAA,EAAAwD,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAW,IAAA,YAAAX,QAAA,CAAAsB,IAAA,IAAAtB,QAAA,CAAAsB,IAAA,CAAAC,MAAA;UACA;UACA,IAAAC,SAAA,GAAAxB,QAAA,CAAAsB,IAAA,CAAAR,IAAA,WAAAW,CAAA;YAAA,OAAAA,CAAA,CAAAC,UAAA;UAAA;UACA,IAAAF,SAAA;YACAJ,MAAA,CAAAhC,WAAA,GAAAoC,SAAA;YACAJ,MAAA,CAAAO,YAAA;UACA;QACA;MACA;IACA;IAEA;IACAhC,gBAAA,WAAAA,iBAAA;MAAA,IAAAiC,MAAA;MACA,IAAAC,uBAAA,KAAA9B,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAW,IAAA;UACAiB,MAAA,CAAA1F,IAAA,CAAAC,QAAA,GAAA6D,QAAA,CAAA8B,GAAA;QACA;UACAF,MAAA,CAAAG,QAAA,CAAAZ,KAAA;QACA;MACA,GAAAF,KAAA;QACAW,MAAA,CAAAG,QAAA,CAAAZ,KAAA;MACA;IACA;IAEA;IACAa,sBAAA,WAAAA,uBAAAC,GAAA;MACA,IAAAA,GAAA;QACA;QACA,KAAAtC,gBAAA;MACA;QACA;QACA,KAAAzD,IAAA,CAAAC,QAAA;MACA;IACA;IAEA;IACA+F,aAAA,WAAAA,cAAA;MACA,KAAAC,KAAA,CAAAC,MAAA,CAAAC,GAAA,CAAAC,KAAA;IACA;IAEA;IACAC,oBAAA,WAAAA,qBAAA;MACA;MACA,SAAAtG,MAAA;QACA,KAAA8F,QAAA,CAAAS,OAAA;QACA;MACA;MACA,KAAAnE,oBAAA;MACA,KAAAoE,cAAA;IACA;IAEA;IACAA,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,KAAA1E,cAAA;MACA,IAAA2E,qBAAA;QACApE,OAAA,OAAAD,YAAA,CAAAC,OAAA;QACAC,QAAA,OAAAF,YAAA,CAAAE,QAAA;QACAC,OAAA,OAAAH,YAAA,CAAAG,OAAA;QACAmE,WAAA,OAAAtE,YAAA,CAAAzB,IAAA;QACAD,WAAA,OAAA0B,YAAA,CAAAI,IAAA;QACAmE,eAAA,OAAAvE,YAAA,CAAAK;MACA,GAAAoB,IAAA,WAAAC,QAAA;QACA0C,MAAA,CAAA1E,cAAA;QACA,IAAAgC,QAAA,CAAAW,IAAA;UACA+B,MAAA,CAAA9D,WAAA,GAAAoB,QAAA,CAAAsB,IAAA;UACAoB,MAAA,CAAA7D,YAAA,GAAAmB,QAAA,CAAA8C,KAAA;QACA;MACA,GAAA7B,KAAA;QACAyB,MAAA,CAAA1E,cAAA;QACA;QACA0E,MAAA,CAAA9D,WAAA,IACA;UAAAmE,UAAA;UAAAC,YAAA;UAAAC,YAAA;UAAAC,WAAA;UAAAC,YAAA;UAAAC,YAAA;UAAAC,gBAAA;QAAA,GACA;UAAAN,UAAA;UAAAC,YAAA;UAAAC,YAAA;UAAAC,WAAA;UAAAC,YAAA;UAAAC,YAAA;UAAAC,gBAAA;QAAA,GACA;UAAAN,UAAA;UAAAC,YAAA;UAAAC,YAAA;UAAAC,WAAA;UAAAC,YAAA;UAAAC,YAAA;UAAAC,gBAAA;QAAA,GACA;UAAAN,UAAA;UAAAC,YAAA;UAAAC,YAAA;UAAAC,WAAA;UAAAC,YAAA;UAAAC,YAAA;UAAAC,gBAAA;QAAA,GACA;UAAAN,UAAA;UAAAC,YAAA;UAAAC,YAAA;UAAAC,WAAA;UAAAC,YAAA;UAAAC,YAAA;UAAAC,gBAAA;QAAA,EACA;QACAX,MAAA,CAAA7D,YAAA;MACA;IACA;IAEA;IACAyE,cAAA,WAAAA,eAAA;MACA,KAAAhF,YAAA,CAAAC,OAAA;MACA,KAAAkE,cAAA;IACA;IAEA;IACAc,iBAAA,WAAAA,kBAAA;MACA,KAAAjF,YAAA;QACAC,OAAA;QACAC,QAAA;QACAC,OAAA;QACA5B,IAAA;QACA6B,IAAA;QACAC,QAAA;MACA;MACA,KAAA8D,cAAA;IACA;IAEA;IACAe,4BAAA,WAAAA,6BAAAC,SAAA;MACA,IAAAA,SAAA,CAAAlC,MAAA;QACA,KAAAzC,eAAA,GAAA2E,SAAA;MACA;QACA,KAAA3E,eAAA;MACA;IACA;IAEA;IACA4E,0BAAA,WAAAA,2BAAAC,WAAA;MACA,KAAArF,YAAA,CAAAC,OAAA,GAAAoF,WAAA;MACA,KAAAlB,cAAA;IACA;IAEA;IACAmB,uBAAA,WAAAA,wBAAAC,IAAA;MACA,KAAAvF,YAAA,CAAAE,QAAA,GAAAqF,IAAA;MACA,KAAAvF,YAAA,CAAAC,OAAA;MACA,KAAAkE,cAAA;IACA;IAEA;IACAqB,oBAAA,WAAAA,qBAAA;MACA,SAAAhF,eAAA;QACA,KAAA5C,IAAA,CAAAK,SAAA,QAAAuC,eAAA,CAAAiE,UAAA;QACA,KAAA7G,IAAA,CAAAO,WAAA,QAAAqC,eAAA,CAAAmE,YAAA;QACA,KAAA/G,IAAA,CAAAQ,WAAA,QAAAoC,eAAA,CAAAkE,YAAA;QACA,KAAA9G,IAAA,CAAAS,aAAA,QAAAmC,eAAA,CAAAoE,WAAA;QACA,KAAAhH,IAAA,CAAAU,WAAA,QAAAmH,iBAAA,MAAAjF,eAAA;QACA,KAAA5C,IAAA,CAAAW,IAAA,QAAAiC,eAAA,CAAAsE,YAAA;QACA,KAAA/E,oBAAA;;QAEA;QACA,KAAAe,WAAA;QACA,KAAAC,aAAA;MACA;QACA,KAAA0C,QAAA,CAAAS,OAAA;MACA;IACA;IAEA;IACAuB,iBAAA,WAAAA,kBAAAC,GAAA,EAAAC,MAAA;MACA,IAAAvF,IAAA,GAAAsF,GAAA,CAAAb,YAAA;MACA,IAAAe,MAAA,QAAAC,IAAA,CAAAzF,IAAA,CAAAyE,YAAA,CAAArC,IAAA,WAAAsD,IAAA;QAAA,OAAAA,IAAA,CAAAjG,SAAA,IAAAO,IAAA;MAAA;MACA,OAAAwF,MAAA,GAAAA,MAAA,CAAAhG,SAAA,GAAAQ,IAAA;IACA;IAEA;IACA2F,SAAA,WAAAA,UAAA;MACA,UAAAnI,IAAA,CAAAK,SAAA;QACA,KAAAwF,QAAA,CAAAS,OAAA;QACA;MACA;MACA,KAAAzD,gBAAA;MACA,KAAAuF,UAAA;IACA;IAEA;IACAA,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAApF,UAAA;MACA,IAAAkC,4BAAA,OAAAnF,IAAA,CAAAK,SAAA,EAAAwD,IAAA,WAAAC,QAAA;QACAuE,MAAA,CAAApF,UAAA;QACA,IAAAa,QAAA,CAAAW,IAAA;UACA4D,MAAA,CAAAtF,OAAA,GAAAe,QAAA,CAAAsB,IAAA;UACAiD,MAAA,CAAArF,QAAA,GAAAc,QAAA,CAAA8C,KAAA;UACA,KAAAyB,MAAA,CAAAtF,OAAA,IAAAsF,MAAA,CAAAtF,OAAA,CAAAsC,MAAA;YACAgD,MAAA,CAAAxC,QAAA,CAAAyC,IAAA;UACA;YACA;YACA,IAAAC,UAAA,GAAAF,MAAA,CAAAtF,OAAA,CAAA6B,IAAA,WAAAW,CAAA;cAAA,OAAAA,CAAA,CAAAC,UAAA;YAAA;YACA,IAAA+C,UAAA;cACAF,MAAA,CAAAG,eAAA,CAAAD,UAAA;YACA;UACA;QACA;UACAF,MAAA,CAAAtF,OAAA;UACAsF,MAAA,CAAArF,QAAA;QACA;MACA,GAAA+B,KAAA;QACAsD,MAAA,CAAApF,UAAA;QACAoF,MAAA,CAAAxC,QAAA,CAAAZ,KAAA;MACA;IACA;IAEA;IACAuD,eAAA,WAAAA,gBAAAV,GAAA;MACA,KAAA5E,WAAA,GAAA4E,GAAA;MACA,KAAA3E,aAAA,GAAA2E,GAAA,CAAAW,MAAA;IACA;IAEA;IACAC,gBAAA,WAAAA,iBAAA;MACA,SAAAxF,WAAA;QACA,KAAAL,gBAAA;QACA;QACA,KAAA4C,YAAA;MACA;QACA,KAAAI,QAAA,CAAAS,OAAA;MACA;IACA;IAEA;IACAb,YAAA,WAAAA,aAAA;MAAA,IAAAkD,MAAA;MACA,IAAAC,uBAAA,OAAA1F,WAAA,CAAAuF,MAAA,EAAA5E,IAAA,WAAAC,QAAA;QACAkB,OAAA,CAAA6D,GAAA,iBAAA/E,QAAA;QACA,IAAAA,QAAA,IAAAA,QAAA,CAAAW,IAAA;UACAkE,MAAA,CAAAvF,aAAA,GAAAU,QAAA,CAAAsB,IAAA;QACA;UACAuD,MAAA,CAAAvF,aAAA;UACAuF,MAAA,CAAA9C,QAAA,CAAAZ,KAAA,kBAAAnB,QAAA,GAAAA,QAAA,CAAA8B,GAAA;QACA;MACA,GAAAb,KAAA,WAAAE,KAAA;QACAD,OAAA,CAAAC,KAAA,mBAAAA,KAAA;QACA0D,MAAA,CAAA9C,QAAA,CAAAZ,KAAA;QACA0D,MAAA,CAAAvF,aAAA;MACA;IACA;IAEA;IACA0F,gBAAA,WAAAA,iBAAA;MACA,KAAA5F,WAAA;MACA,KAAAC,aAAA;MACA,KAAAC,aAAA;IACA;IAEA;IACA2F,YAAA,WAAAA,aAAAC,IAAA;MACA,IAAAC,WAAA,sDAAAC,IAAA,CAAAF,IAAA,CAAAxJ,IAAA;MACA,IAAA2J,OAAA,GAAAH,IAAA,CAAArB,IAAA;MAEA,KAAAsB,WAAA;QACA,KAAApD,QAAA,CAAAZ,KAAA;QACA;MACA;MACA,KAAAkE,OAAA;QACA,KAAAtD,QAAA,CAAAZ,KAAA;QACA;MACA;MACA;IACA;IAEA;IACAmE,UAAA,WAAAA,WAAAC,OAAA;MACA;MACArE,OAAA,CAAA6D,GAAA,UAAAQ,OAAA,CAAAL,IAAA;MACA;MACA,KAAA9G,QAAA,CAAAoH,IAAA;QACA9J,IAAA,EAAA6J,OAAA,CAAAL,IAAA,CAAAxJ,IAAA;QACA+J,GAAA,EAAAC,GAAA,CAAAC,eAAA,CAAAJ,OAAA,CAAAL,IAAA;MACA;MACAK,OAAA,CAAAK,SAAA;IACA;IAEA;IACAC,YAAA,WAAAA,aAAAX,IAAA;MACA,IAAAY,KAAA,QAAA1H,QAAA,CAAA2H,OAAA,CAAAb,IAAA;MACA,IAAAY,KAAA;QACA,KAAA1H,QAAA,CAAA4H,MAAA,CAAAF,KAAA;MACA;IACA;IAEA;IACAG,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAA/D,KAAA,SAAAgE,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACA,KAAAF,MAAA,CAAAG,iBAAA;YACA;UACA;;UAEA;UACA,IAAAC,OAAA,GAAAJ,MAAA,CAAAjK,MAAA,GAAAsK,oCAAA,GAAAC,iCAAA;UAEAF,OAAA,CAAAJ,MAAA,CAAAhK,IAAA,EAAA6D,IAAA,WAAAC,QAAA;YACA,IAAAA,QAAA,CAAAW,IAAA;cACAuF,MAAA,CAAAO,MAAA,CAAAC,UAAA,CAAAR,MAAA,CAAAjK,MAAA;cACAiK,MAAA,CAAAS,MAAA;YACA;cACAT,MAAA,CAAAO,MAAA,CAAAG,QAAA,CAAA5G,QAAA,CAAA8B,GAAA,KAAAoE,MAAA,CAAAjK,MAAA;YACA;UACA,GAAAgF,KAAA;YACAiF,MAAA,CAAAO,MAAA,CAAAG,QAAA;UACA;QACA;MACA;IACA;IAEA;IACAD,MAAA,WAAAA,OAAA;MACA,KAAAE,OAAA,CAAArB,IAAA;QAAAsB,IAAA;MAAA;IACA;IAEA;IACAlJ,iBAAA,WAAAA,kBAAAmJ,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,KAAAD,KAAA;QACAC,QAAA;QACA;MACA;MAEA,IAAAC,KAAA,OAAAC,IAAA;MACAD,KAAA,CAAAE,QAAA;MACA,IAAAC,SAAA,OAAAF,IAAA,CAAAH,KAAA;;MAEA;MACA,IAAAK,SAAA,GAAAH,KAAA;QACAD,QAAA,KAAAK,KAAA;QACA;MACA;;MAEA;MACA,SAAApL,IAAA,CAAAc,WAAA;QACA,IAAAuK,OAAA,OAAAJ,IAAA,MAAAjL,IAAA,CAAAc,WAAA;QACA,IAAAqK,SAAA,GAAAE,OAAA;UACAN,QAAA,KAAAK,KAAA;UACA;QACA;MACA;MAEAL,QAAA;;MAEA;MACA,SAAA/K,IAAA,CAAAc,WAAA;QACA,KAAAwK,sBAAA;MACA;MACA,SAAAtL,IAAA,CAAAe,YAAA;QACA,KAAAuK,sBAAA;MACA;IACA;IAEA;IACA3J,eAAA,WAAAA,gBAAAkJ,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,KAAAD,KAAA;QACAC,QAAA;QACA;MACA;MAEA,IAAAM,OAAA,OAAAJ,IAAA,CAAAH,KAAA;;MAEA;MACA,SAAA9K,IAAA,CAAAa,aAAA;QACA,IAAAsK,SAAA,OAAAF,IAAA,MAAAjL,IAAA,CAAAa,aAAA;QACA,IAAAwK,OAAA,GAAAF,SAAA;UACAJ,QAAA,KAAAK,KAAA;UACA;QACA;MACA;MAEAL,QAAA;;MAEA;MACA,SAAA/K,IAAA,CAAAa,aAAA;QACA,KAAAyK,sBAAA;MACA;MACA,SAAAtL,IAAA,CAAAe,YAAA;QACA,KAAAuK,sBAAA;MACA;IACA;IAEA;IACAA,sBAAA,WAAAA,uBAAAC,SAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,SAAA;QACA,IAAAD,MAAA,CAAAvF,KAAA,CAAAjG,IAAA;UACAwL,MAAA,CAAAvF,KAAA,CAAAjG,IAAA,CAAA0L,aAAA,CAAAH,SAAA;QACA;MACA;IACA;IAEA;IACA9J,kBAAA,WAAAA,mBAAAoJ,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,KAAAD,KAAA;QACAC,QAAA;QACA;MACA;MAEA,IAAAnK,UAAA,GAAA+K,MAAA,CAAAb,KAAA;MACA,IAAA7J,QAAA,GAAA0K,MAAA,MAAA3L,IAAA,CAAAiB,QAAA;MAEA,IAAAL,UAAA;QACAmK,QAAA,KAAAK,KAAA;QACA;MACA;MAEA,IAAAnK,QAAA,QAAAL,UAAA,GAAAK,QAAA;QACA8J,QAAA,KAAAK,KAAA,6EAAAQ,MAAA,CAAA3K,QAAA;QACA;MACA;MAEA8J,QAAA;IACA;IAEA;IACAnJ,oBAAA,WAAAA,qBAAAiJ,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,KAAAD,KAAA;QACAC,QAAA;QACA;MACA;MAEA,IAAAhK,YAAA,OAAAkK,IAAA,CAAAH,KAAA;;MAEA;MACA,IAAAE,KAAA,OAAAC,IAAA;MACAD,KAAA,CAAAE,QAAA;MACA,IAAAnK,YAAA,GAAAiK,KAAA;QACAD,QAAA,KAAAK,KAAA;QACA;MACA;;MAEA;MACA,SAAApL,IAAA,CAAAc,WAAA;QACA,IAAAuK,OAAA,OAAAJ,IAAA,MAAAjL,IAAA,CAAAc,WAAA;QACA,IAAAC,YAAA,GAAAsK,OAAA;UACAN,QAAA,KAAAK,KAAA;UACA;QACA;MACA;MAEAL,QAAA;;MAEA;MACA,SAAA/K,IAAA,CAAAc,WAAA;QACA,KAAAwK,sBAAA;MACA;IACA;IAEA;IACAnB,iBAAA,WAAAA,kBAAA;MACA,IAAAa,KAAA,OAAAC,IAAA;MACAD,KAAA,CAAAE,QAAA;;MAEA;MACA,SAAAlL,IAAA,CAAAa,aAAA;QACA,IAAAsK,SAAA,OAAAF,IAAA,MAAAjL,IAAA,CAAAa,aAAA;QACA,IAAAsK,SAAA,GAAAH,KAAA;UACA,KAAAT,MAAA,CAAAG,QAAA;UACA;QACA;MACA;;MAEA;MACA,SAAA1K,IAAA,CAAAa,aAAA,SAAAb,IAAA,CAAAc,WAAA;QACA,IAAAqK,UAAA,OAAAF,IAAA,MAAAjL,IAAA,CAAAa,aAAA;QACA,IAAAwK,OAAA,OAAAJ,IAAA,MAAAjL,IAAA,CAAAc,WAAA;QACA,IAAAqK,UAAA,GAAAE,OAAA;UACA,KAAAd,MAAA,CAAAG,QAAA;UACA;QACA;MACA;;MAEA;MACA,SAAA1K,IAAA,CAAAe,YAAA;QACA,IAAAA,YAAA,OAAAkK,IAAA,MAAAjL,IAAA,CAAAe,YAAA;QACA,IAAAA,YAAA,GAAAiK,KAAA;UACA,KAAAT,MAAA,CAAAG,QAAA;UACA;QACA;;QAEA;QACA,SAAA1K,IAAA,CAAAc,WAAA;UACA,IAAAuK,QAAA,OAAAJ,IAAA,MAAAjL,IAAA,CAAAc,WAAA;UACA,IAAAC,YAAA,GAAAsK,QAAA;YACA,KAAAd,MAAA,CAAAG,QAAA;YACA;UACA;QACA;MACA;MAEA;IACA;EACA;AACA", "ignoreList": []}]}