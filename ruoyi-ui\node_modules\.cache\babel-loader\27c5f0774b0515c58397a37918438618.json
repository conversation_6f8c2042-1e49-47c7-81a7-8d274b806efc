{"remainingRequest": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\src\\views\\sc\\plan\\edit_plan.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\src\\views\\sc\\plan\\edit_plan.vue", "mtime": 1753786042780}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\babel.config.js", "mtime": 1749629472348}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751945356000}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751945361444}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751945356000}, {"path": "D:\\y1\\idea\\Agile_Manufacturing\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751945364156}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_productionPlan", "require", "_product", "_numbers", "_Pagination", "_interopRequireDefault", "_DictTag", "name", "dicts", "components", "Pagination", "DictTag", "data", "title", "isEdit", "form", "planCode", "planName", "sourceType", "orderCode", "productId", "undefined", "productName", "productCode", "specification", "productType", "unit", "plannedQty", "planStartTime", "planEndTime", "requiredDate", "remark", "isSystemCode", "rules", "required", "message", "trigger", "max", "validator", "validateStartTime", "validateEndTime", "validateRequiredDate", "productOptions", "productLoading", "sourceTypeOptions", "dict<PERSON><PERSON>l", "dict<PERSON><PERSON>ue", "fileList", "productDialogVisible", "productQuery", "pageNum", "pageSize", "keyword", "type", "property", "productList", "productTotal", "selectedProduct", "bomDialogVisible", "b<PERSON><PERSON><PERSON><PERSON>", "bomList", "bomTotal", "bomLoading", "selectedBom", "selectedBomId", "bomDetailList", "created", "$route", "query", "getPlanData", "generatePlanCode", "methods", "_this", "getProductionPlan", "then", "response", "loadAssociatedBom", "_this2", "listBomsByProductId", "code", "rows", "length", "activeBom", "find", "b", "bom_status", "getBomDetail", "_this3", "getAutoNumbers", "msg", "$message", "error", "catch", "handleSystemCodeChange", "val", "triggerUpload", "$refs", "upload", "$el", "click", "openProductSelection", "warning", "getProductList", "_this4", "listProducts", "productUnit", "productProperty", "total", "product_id", "product_code", "product_name", "product_sfn", "product_type", "product_unit", "product_property", "searchProducts", "resetProduct<PERSON>uery", "handleProductSelectionChange", "selection", "handleProductCurrentChange", "currentPage", "handleProductSizeChange", "size", "confirmProductSelect", "formatProductType", "row", "column", "option", "productTypeOptions", "item", "selectBom", "getBomList", "_this5", "info", "defaultBom", "handleBomSelect", "bom_id", "confirmBomSelect", "_this6", "findBomDetails", "console", "log", "clearSelectedBom", "beforeUpload", "file", "isValidType", "test", "isLt10M", "uploadFile", "options", "push", "url", "URL", "createObjectURL", "onSuccess", "handleRemove", "index", "indexOf", "splice", "submitForm", "_this7", "validate", "valid", "validateDateLogic", "apiCall", "updateProductionPlan", "addProductionPlan", "$modal", "msgSuccess", "cancel", "msgError", "$router", "path", "rule", "value", "callback", "today", "Date", "setHours", "startDate", "Error", "endDate"], "sources": ["src/views/sc/plan/edit_plan.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"form-container\">\r\n      <!-- 基础信息区 -->\r\n      <el-tabs type=\"border-card\">\r\n        <el-tab-pane>\r\n          <span slot=\"label\"><i class=\"el-icon-date\"></i> 基础信息</span>\r\n          <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"8\">\r\n                <el-form-item label=\"计划编号\" prop=\"planCode\" required>\r\n                  <el-input v-model=\"form.planCode\" placeholder=\"请输入\" :disabled=\"isSystemCode\"></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"4\">\r\n                <el-switch\r\n                  v-model=\"isSystemCode\"\r\n                  active-text=\"系统编号\"\r\n                  inactive-text=\"\"\r\n                  style=\"margin-top: 13px;\"\r\n                  @change=\"handleSystemCodeChange\"\r\n                ></el-switch>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"计划名称\" prop=\"planName\" required>\r\n                  <el-input v-model=\"form.planName\" placeholder=\"请输入\"></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"来源类型\" prop=\"sourceType\" required>\r\n                  <el-select v-model=\"form.sourceType\" placeholder=\"销售订单\" style=\"width: 100%\">\r\n                    <el-option\r\n                      v-for=\"item in sourceTypeOptions\"\r\n                      :key=\"item.dictValue\"\r\n                      :label=\"item.dictLabel\"\r\n                      :value=\"item.dictValue\"\r\n                    ></el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"订单编号\" prop=\"orderCode\">\r\n                  <el-input v-model=\"form.orderCode\" placeholder=\"请输入\"></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"成品名称\" prop=\"productName\">\r\n                  <el-input\r\n                    placeholder=\"请选择成品\"\r\n                    v-model=\"form.productName\"\r\n                    class=\"input-with-select\"\r\n                  >\r\n                    <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"openProductSelection\"></el-button>\r\n                  </el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"成品编号\" prop=\"productCode\">\r\n                  <el-input v-model=\"form.productCode\" placeholder=\"请输入\" disabled></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"规格型号\" prop=\"specification\">\r\n                  <el-input v-model=\"form.specification\" placeholder=\"请输入\" disabled></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"成品类型\" prop=\"productType\">\r\n                  <dict-tag :options=\"dict.type.product_type\" :value=\"form.productType\" />\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"单位\" prop=\"unit\">\r\n                  <el-input v-model=\"form.unit\" placeholder=\"请输入\" disabled></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"计划数量\" prop=\"plannedQty\" required>\r\n                  <el-input-number v-model=\"form.plannedQty\" :min=\"1\" controls-position=\"right\" style=\"width: 100%\"></el-input-number>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"开工时间\" prop=\"planStartTime\">\r\n                  <el-date-picker\r\n                    v-model=\"form.planStartTime\"\r\n                    type=\"date\"\r\n                    placeholder=\"请选择日期\"\r\n                    style=\"width: 100%\"\r\n                    value-format=\"yyyy-MM-dd\"\r\n                  ></el-date-picker>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"完工时间\" prop=\"planEndTime\">\r\n                  <el-date-picker\r\n                    v-model=\"form.planEndTime\"\r\n                    type=\"date\"\r\n                    placeholder=\"请选择日期\"\r\n                    style=\"width: 100%\"\r\n                    value-format=\"yyyy-MM-dd\"\r\n                  ></el-date-picker>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"需求日期\" prop=\"requiredDate\">\r\n                  <el-date-picker\r\n                    v-model=\"form.requiredDate\"\r\n                    type=\"date\"\r\n                    placeholder=\"请选择日期\"\r\n                    style=\"width: 100%\"\r\n                    value-format=\"yyyy-MM-dd\"\r\n                  ></el-date-picker>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"备注\" prop=\"remark\">\r\n                  <el-input\r\n                    type=\"textarea\"\r\n                    v-model=\"form.remark\"\r\n                    placeholder=\"请输入\"\r\n                    :rows=\"4\"\r\n                  ></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"附件\" prop=\"attachment\">\r\n                  <div class=\"upload-container\" @click=\"triggerUpload\">\r\n                    <el-upload\r\n                      ref=\"upload\"\r\n                      class=\"upload-hidden\"\r\n                      action=\"#\"\r\n                      :http-request=\"uploadFile\"\r\n                      :file-list=\"fileList\"\r\n                      :before-upload=\"beforeUpload\"\r\n                      :on-remove=\"handleRemove\"\r\n                      multiple\r\n                      drag\r\n                    >\r\n                      <div class=\"upload-area\">\r\n                        <i class=\"el-icon-upload\"></i>\r\n                        <div class=\"upload-text\">点击或者拖动文件到虚线框内上传</div>\r\n                        <div class=\"upload-hint\">支持 docx, xls, PDF, rar, zip, PNG, JPG 等类型的文件</div>\r\n                      </div>\r\n                    </el-upload>\r\n                  </div>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-divider>\r\n              <span class=\"bom-title\">BOM组成</span>\r\n            </el-divider>\r\n            \r\n            <el-row>\r\n              <el-col :span=\"24\">\r\n                <div class=\"bom-container\">\r\n                  <div class=\"folder-icon\" v-if=\"!selectedBom\">\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"60\" height=\"60\" viewBox=\"0 0 24 24\" fill=\"#DCDFE6\">\r\n                      <path d=\"M10 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2h-8l-2-2z\"/>\r\n                    </svg>\r\n                  </div>\r\n                  <div class=\"bom-text\" v-if=\"!selectedBom\">暂无数据</div>\r\n                  <div class=\"bom-info\" v-else>\r\n                    <div class=\"bom-header\">\r\n                      <div class=\"bom-title-info\">\r\n                        <span>BOM编号：{{ selectedBom.bom_code }}</span>\r\n                        <span>版本号：{{ selectedBom.bom_version }}</span>\r\n                      </div>\r\n                      <el-button type=\"text\" icon=\"el-icon-delete\" @click=\"clearSelectedBom\">清除</el-button>\r\n                    </div>\r\n                    <el-table\r\n                      :data=\"bomDetailList\"\r\n                      border\r\n                      size=\"small\"\r\n                      style=\"width: 100%\"\r\n                      class=\"bom-detail-table\"\r\n                    >\r\n                      <el-table-column label=\"序号\" type=\"index\" width=\"50\" align=\"center\"></el-table-column>\r\n                      <el-table-column prop=\"material_code\" label=\"物料编码\" min-width=\"120\" align=\"center\"></el-table-column>\r\n                      <el-table-column prop=\"material_name\" label=\"物料名称\" min-width=\"150\" align=\"center\"></el-table-column>\r\n                      <el-table-column prop=\"specification\" label=\"规格型号\" min-width=\"100\" align=\"center\"></el-table-column>\r\n                      <el-table-column prop=\"unit\" label=\"单位\" width=\"60\" align=\"center\"></el-table-column>\r\n                      <el-table-column prop=\"quantity\" label=\"用量\" width=\"80\" align=\"center\"></el-table-column>\r\n                      <el-table-column prop=\"remark\" label=\"备注\" min-width=\"120\" align=\"center\"></el-table-column>\r\n                    </el-table>\r\n                  </div>\r\n                  <div class=\"bom-action\">\r\n                    <el-tooltip :disabled=\"form.productId\" content=\"请先选择成品!\" placement=\"right\" effect=\"light\">\r\n                      <el-button type=\"primary\" class=\"select-bom-button\" @click=\"selectBom\">选择BOM</el-button>\r\n                    </el-tooltip>\r\n                  </div>\r\n                </div>\r\n              </el-col>\r\n            </el-row>\r\n            \r\n            <el-row>\r\n              <el-col :span=\"24\" style=\"text-align: center; margin-top: 20px;\">\r\n                <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n                <el-button @click=\"cancel\">取 消</el-button>\r\n              </el-col>\r\n            </el-row>\r\n          </el-form>\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n    </div>\r\n    \r\n    <!-- 产品选择对话框 -->\r\n    <el-dialog title=\"选择成品\" :visible.sync=\"productDialogVisible\" width=\"40%\" append-to-body>\r\n      <el-form :model=\"productQuery\" ref=\"productQueryForm\" :inline=\"true\" class=\"demo-form-inline\" size=\"small\">\r\n        <el-form-item>\r\n          <el-input v-model=\"productQuery.keyword\" placeholder=\"请输入产品编号/名称\" clearable style=\"width: 180px;\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-select v-model=\"productQuery.unit\" placeholder=\"请选择单位\" clearable style=\"width: 120px;\">\r\n            <el-option label=\"个\" value=\"个\"></el-option>\r\n            <el-option label=\"件\" value=\"件\"></el-option>\r\n            <el-option label=\"台\" value=\"台\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-select v-model=\"productQuery.type\" placeholder=\"请选择类型\" clearable style=\"width: 120px;\">\r\n            <el-option label=\"成品\" value=\"成品\"></el-option>\r\n            <el-option label=\"半成品\" value=\"半成品\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-select v-model=\"productQuery.property\" placeholder=\"请选择产品属性\" clearable style=\"width: 120px;\">\r\n            <el-option label=\"自制\" value=\"自制\"></el-option>\r\n            <el-option label=\"外购\" value=\"外购\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"small\" @click=\"searchProducts\">查询</el-button>\r\n          <el-button icon=\"el-icon-refresh\" size=\"small\" @click=\"resetProductQuery\">重置</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n      \r\n      <el-table\r\n        v-loading=\"productLoading\"\r\n        :data=\"productList\"\r\n        border\r\n        size=\"small\"\r\n        style=\"width: 100%\"\r\n        @selection-change=\"handleProductSelectionChange\"\r\n        height=\"300\"\r\n      >\r\n        <el-table-column type=\"selection\" width=\"40\" align=\"center\"></el-table-column>\r\n        <el-table-column label=\"序号\" type=\"index\" width=\"40\" align=\"center\"></el-table-column>\r\n        <el-table-column prop=\"product_code\" label=\"产品编号\" width=\"90\" align=\"center\"></el-table-column>\r\n        <el-table-column prop=\"product_name\" label=\"产品名称\" min-width=\"120\" align=\"center\"></el-table-column>\r\n        <el-table-column prop=\"product_sfn\" label=\"规格型号\" min-width=\"90\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <span :style=\"{color: '#1890ff'}\">{{ scope.row.product_sfn }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"product_unit\" label=\"单位\" width=\"60\" align=\"center\"></el-table-column>\r\n        <el-table-column prop=\"product_type\" label=\"产品类型\" width=\"70\" align=\"center\" :formatter=\"formatProductType\"></el-table-column>\r\n      </el-table>\r\n      \r\n      <div class=\"pagination-container\">\r\n        <span class=\"total-text\">共 {{ productTotal }} 条</span>\r\n        <div class=\"pagination-wrapper\">\r\n          <span class=\"page-size\">\r\n            <el-select v-model=\"productQuery.pageSize\" size=\"mini\" @change=\"handleProductSizeChange\" style=\"width: 80px;\">\r\n              <el-option\r\n                v-for=\"item in [10, 20, 30, 50]\"\r\n                :key=\"item\"\r\n                :label=\"`${item}条/页`\"\r\n                :value=\"item\">\r\n              </el-option>\r\n            </el-select>\r\n          </span>\r\n          <el-pagination\r\n            small\r\n            background\r\n            @current-change=\"handleProductCurrentChange\"\r\n            :current-page=\"productQuery.pageNum\"\r\n            :page-size=\"productQuery.pageSize\"\r\n            layout=\"prev, pager, next, jumper\"\r\n            :pager-count=\"5\"\r\n            :total=\"productTotal\">\r\n          </el-pagination>\r\n        </div>\r\n      </div>\r\n      \r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button size=\"small\" @click=\"productDialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" size=\"small\" @click=\"confirmProductSelect\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    \r\n    <!-- BOM选择对话框 -->\r\n    <el-dialog title=\"选择BOM\" :visible.sync=\"bomDialogVisible\" width=\"40%\" append-to-body>\r\n      <div class=\"bom-dialog-header\">\r\n        <div class=\"product-info\">\r\n          <span>产品名称：{{ form.productName }}</span>\r\n          <span>产品编号：{{ form.productCode }}</span>\r\n          <span>规格型号：{{ form.specification }}</span>\r\n          <span>单位：{{ form.unit }}</span>\r\n        </div>\r\n      </div>\r\n      \r\n      <el-table\r\n        v-loading=\"bomLoading\"\r\n        :data=\"bomList\"\r\n        border\r\n        style=\"width: 100%\"\r\n        @row-click=\"handleBomSelect\"\r\n        highlight-current-row\r\n        size=\"small\"\r\n        height=\"300\"\r\n      >\r\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-radio v-model=\"selectedBomId\" :label=\"scope.row.bom_id\" @change=\"handleBomSelect(scope.row)\">&nbsp;</el-radio>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"序号\" type=\"index\" width=\"55\" align=\"center\"></el-table-column>\r\n        <el-table-column prop=\"bom_code\" label=\"BOM编号\" min-width=\"150\" align=\"center\"></el-table-column>\r\n        <el-table-column prop=\"bom_version\" label=\"版本号\" width=\"100\" align=\"center\"></el-table-column>\r\n        <el-table-column prop=\"bom_status\" label=\"默认BOM\" width=\"100\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ scope.row.bom_status === '1' ? '是' : '否' }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"bom_output\" label=\"日产量\" width=\"100\" align=\"center\"></el-table-column>\r\n      </el-table>\r\n      \r\n      <pagination\r\n        v-show=\"bomTotal > 0\"\r\n        :total=\"bomTotal\"\r\n        :page.sync=\"bomQuery.pageNum\"\r\n        :limit.sync=\"bomQuery.pageSize\"\r\n        @pagination=\"getBomList\"\r\n      />\r\n      \r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"bomDialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmBomSelect\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getProductionPlan, updateProductionPlan, addProductionPlan } from \"@/api/sc/productionPlan\";\r\nimport { listProducts, listBomsByProductId, findBomDetails } from \"@/api/basic/product\";\r\nimport { getAutoNumbers } from \"@/api/basic/numbers\";\r\nimport Pagination from \"@/components/Pagination\";\r\nimport DictTag from \"@/components/DictTag\";\r\n\r\nexport default {\r\n  name: \"EditPlan\",\r\n  dicts: ['product_type'],\r\n  components: {\r\n    Pagination,\r\n    DictTag\r\n  },\r\n  data() {\r\n    return {\r\n      // 页面标题\r\n      title: \"修改生产计划\",\r\n      // 是否为修改模式\r\n      isEdit: true,\r\n      // 表单数据\r\n      form: {\r\n        planCode: \"\",\r\n        planName: \"\",\r\n        sourceType: \"销售订单\",\r\n        orderCode: \"\",\r\n        productId: undefined,\r\n        productName: \"\",\r\n        productCode: \"\",\r\n        specification: \"\",\r\n        productType: \"\",\r\n        unit: \"\",\r\n        plannedQty: 1,\r\n        planStartTime: \"\",\r\n        planEndTime: \"\",\r\n        requiredDate: \"\",\r\n        remark: \"\"\r\n      },\r\n      // 是否使用系统编号\r\n      isSystemCode: true,\r\n      // 表单验证规则\r\n      rules: {\r\n        planName: [\r\n          { required: true, message: \"计划名称不能为空\", trigger: \"blur\" },\r\n          { max: 50, message: \"长度不能超过50个字符\", trigger: \"blur\" }\r\n        ],\r\n        sourceType: [\r\n          { required: true, message: \"来源类型不能为空\", trigger: \"change\" }\r\n        ],\r\n        productName: [\r\n          { required: true, message: \"请选择产品\", trigger: \"change\" }\r\n        ],\r\n        plannedQty: [\r\n          { required: true, message: \"计划数量不能为空\", trigger: \"blur\" }\r\n        ],\r\n        planStartTime: [\r\n          { validator: this.validateStartTime, trigger: \"change\" }\r\n        ],\r\n        planEndTime: [\r\n          { validator: this.validateEndTime, trigger: \"change\" }\r\n        ],\r\n        requiredDate: [\r\n          { validator: this.validateRequiredDate, trigger: \"change\" }\r\n        ]\r\n      },\r\n      // 产品下拉选项\r\n      productOptions: [],\r\n      // 产品加载状态\r\n      productLoading: false,\r\n      // 来源类型选项\r\n      sourceTypeOptions: [\r\n        { dictLabel: \"销售订单\", dictValue: \"销售订单\" },\r\n        { dictLabel: \"库存备货\", dictValue: \"库存备货\" }\r\n      ],\r\n      // 上传文件列表\r\n      fileList: [],\r\n      \r\n      // 产品选择对话框\r\n      productDialogVisible: false,\r\n      productQuery: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        keyword: \"\",\r\n        unit: \"\",\r\n        type: \"\",\r\n        property: \"\"\r\n      },\r\n      productList: [],\r\n      productTotal: 0,\r\n      selectedProduct: null,\r\n      \r\n      // BOM选择对话框\r\n      bomDialogVisible: false,\r\n      bomQuery: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        productId: undefined\r\n      },\r\n      bomList: [],\r\n      bomTotal: 0,\r\n      bomLoading: false,\r\n      selectedBom: null,\r\n      selectedBomId: null,\r\n      bomDetailList: [],\r\n    };\r\n  },\r\n  created() {\r\n    const planCode = this.$route.query.planCode;\r\n    if (planCode) {\r\n      this.isEdit = true;\r\n      this.title = \"修改生产计划\";\r\n      this.getPlanData(planCode);\r\n    } else {\r\n      this.isEdit = false;\r\n      this.title = \"新增生产计划\";\r\n      this.generatePlanCode();\r\n    }\r\n  },\r\n  methods: {\r\n    // 获取计划数据\r\n    getPlanData(planCode) {\r\n      getProductionPlan(planCode).then(response => {\r\n        this.form = response.data;\r\n        // 如果有关联的产品，则自动加载其BOM信息\r\n        if (this.form.productId) {\r\n          this.loadAssociatedBom();\r\n        }\r\n      });\r\n    },\r\n\r\n    // 加载关联的BOM\r\n    loadAssociatedBom() {\r\n      listBomsByProductId(this.form.productId).then(response => {\r\n        if (response.code === 200 && response.rows && response.rows.length > 0) {\r\n          // 查找默认的BOM (status '1')\r\n          const activeBom = response.rows.find(b => b.bom_status === '1');\r\n          if (activeBom) {\r\n            this.selectedBom = activeBom;\r\n            this.getBomDetail(); // 加载BOM详情\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    // 生成计划编号\r\n    generatePlanCode() {\r\n      getAutoNumbers(6).then(response => {\r\n        if (response.code === 200) {\r\n          this.form.planCode = response.msg;\r\n        } else {\r\n          this.$message.error('获取计划编号失败');\r\n        }\r\n      }).catch(() => {\r\n        this.$message.error('获取计划编号失败');\r\n      });\r\n    },\r\n    \r\n    // 处理系统编号开关变化\r\n    handleSystemCodeChange(val) {\r\n      if (val) {\r\n        // 如果开启系统编号，则生成编号\r\n        this.generatePlanCode();\r\n      } else {\r\n        // 如果关闭系统编号，则清空编号\r\n        this.form.planCode = '';\r\n      }\r\n    },\r\n    \r\n    // 触发上传\r\n    triggerUpload() {\r\n      this.$refs.upload.$el.click();\r\n    },\r\n    \r\n    // 打开产品选择弹窗\r\n    openProductSelection() {\r\n      // 修改模式下不允许更换产品\r\n      if (this.isEdit) {\r\n        this.$message.warning(\"修改模式下不允许更换产品。\");\r\n        return;\r\n      }\r\n      this.productDialogVisible = true;\r\n      this.getProductList();\r\n    },\r\n    \r\n    // 获取产品列表\r\n    getProductList() {\r\n      this.productLoading = true;\r\n      listProducts({\r\n        pageNum: this.productQuery.pageNum,\r\n        pageSize: this.productQuery.pageSize,\r\n        keyword: this.productQuery.keyword,\r\n        productUnit: this.productQuery.unit,\r\n        productType: this.productQuery.type,\r\n        productProperty: this.productQuery.property\r\n      }).then(response => {\r\n        this.productLoading = false;\r\n        if (response.code === 200) {\r\n          this.productList = response.rows;\r\n          this.productTotal = response.total;\r\n        }\r\n      }).catch(() => {\r\n        this.productLoading = false;\r\n        // 模拟数据\r\n        this.productList = [\r\n          { product_id: 1, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' },\r\n          { product_id: 2, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' },\r\n          { product_id: 3, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' },\r\n          { product_id: 4, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' },\r\n          { product_id: 5, product_code: 'CP025', product_name: '产品一', product_sfn: '蓝色', product_type: '成品', product_unit: '个', product_property: '自制' }\r\n        ];\r\n        this.productTotal = 50;\r\n      });\r\n    },\r\n    \r\n    // 搜索产品\r\n    searchProducts() {\r\n      this.productQuery.pageNum = 1;\r\n      this.getProductList();\r\n    },\r\n    \r\n    // 重置产品查询条件\r\n    resetProductQuery() {\r\n      this.productQuery = {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        keyword: \"\",\r\n        unit: \"\",\r\n        type: \"\",\r\n        property: \"\"\r\n      };\r\n      this.getProductList();\r\n    },\r\n    \r\n    // 处理产品表格选择变化\r\n    handleProductSelectionChange(selection) {\r\n      if (selection.length > 0) {\r\n        this.selectedProduct = selection[0];\r\n      } else {\r\n        this.selectedProduct = null;\r\n      }\r\n    },\r\n    \r\n    // 处理产品页码变化\r\n    handleProductCurrentChange(currentPage) {\r\n      this.productQuery.pageNum = currentPage;\r\n      this.getProductList();\r\n    },\r\n    \r\n    // 处理产品每页条数变化\r\n    handleProductSizeChange(size) {\r\n      this.productQuery.pageSize = size;\r\n      this.productQuery.pageNum = 1;\r\n      this.getProductList();\r\n    },\r\n    \r\n    // 确认产品选择\r\n    confirmProductSelect() {\r\n      if (this.selectedProduct) {\r\n        this.form.productId = this.selectedProduct.product_id;\r\n        this.form.productName = this.selectedProduct.product_name;\r\n        this.form.productCode = this.selectedProduct.product_code;\r\n        this.form.specification = this.selectedProduct.product_sfn;\r\n        this.form.productType = this.formatProductType(this.selectedProduct);\r\n        this.form.unit = this.selectedProduct.product_unit;\r\n        this.productDialogVisible = false;\r\n        \r\n        // 清空已选BOM\r\n        this.selectedBom = null;\r\n        this.selectedBomId = null;\r\n      } else {\r\n        this.$message.warning('请选择一个产品！');\r\n      }\r\n    },\r\n    \r\n    // 格式化产品类型\r\n    formatProductType(row, column) {\r\n      const type = row.product_type;\r\n      const option = this.productTypeOptions.find(item => item.dictValue == type);\r\n      return option ? option.dictLabel : type;\r\n    },\r\n    \r\n    // 选择BOM\r\n    selectBom() {\r\n      if (!this.form.productId) {\r\n        this.$message.warning('请先选择成品！');\r\n        return;\r\n      }\r\n      this.bomDialogVisible = true;\r\n      this.getBomList();\r\n    },\r\n    \r\n    // 获取BOM列表\r\n    getBomList() {\r\n      this.bomLoading = true;\r\n      listBomsByProductId(this.form.productId).then(response => {\r\n        this.bomLoading = false;\r\n        if (response.code === 200) {\r\n          this.bomList = response.rows;\r\n          this.bomTotal = response.total;\r\n          if (!this.bomList || this.bomList.length === 0) {\r\n            this.$message.info(\"未找到该产品的BOM信息\");\r\n          } else {\r\n            // 如果有默认BOM，则自动选中\r\n            const defaultBom = this.bomList.find(b => b.bom_status === '1');\r\n            if (defaultBom) {\r\n              this.handleBomSelect(defaultBom);\r\n            }\r\n          }\r\n        } else {\r\n          this.bomList = [];\r\n          this.bomTotal = 0;\r\n        }\r\n      }).catch(() => {\r\n        this.bomLoading = false;\r\n        this.$message.error('获取BOM列表失败');\r\n      });\r\n    },\r\n    \r\n    // 处理BOM行选择\r\n    handleBomSelect(row) {\r\n      this.selectedBom = row;\r\n      this.selectedBomId = row.bom_id;\r\n    },\r\n    \r\n    // 确认BOM选择\r\n    confirmBomSelect() {\r\n      if (this.selectedBom) {\r\n        this.bomDialogVisible = false;\r\n        // 获取BOM详情\r\n        this.getBomDetail();\r\n      } else {\r\n        this.$message.warning('请选择一个BOM！');\r\n      }\r\n    },\r\n    \r\n    // 获取BOM详情\r\n    getBomDetail() {\r\n      findBomDetails(this.selectedBom.bom_id).then(response => {\r\n        console.log(\"成功获取BOM详情响应:\", response);\r\n        if (response && response.code === 200) {\r\n          this.bomDetailList = response.rows;\r\n        } else {\r\n          this.bomDetailList = [];\r\n          this.$message.error(\"获取BOM详情失败: \" + (response ? response.msg : '无响应'));\r\n        }\r\n      }).catch(error => {\r\n        console.error(\"获取BOM详情接口调用失败:\", error);\r\n        this.$message.error(\"获取BOM详情接口调用失败\");\r\n        this.bomDetailList = [];\r\n      });\r\n    },\r\n    \r\n    // 清除已选BOM\r\n    clearSelectedBom() {\r\n      this.selectedBom = null;\r\n      this.selectedBomId = null;\r\n      this.bomDetailList = [];\r\n    },\r\n    \r\n    // 上传前检查文件类型和大小\r\n    beforeUpload(file) {\r\n      const isValidType = /\\.(doc|docx|xls|xlsx|pdf|rar|zip|png|jpg|jpeg)$/i.test(file.name);\r\n      const isLt10M = file.size / 1024 / 1024 < 10;\r\n\r\n      if (!isValidType) {\r\n        this.$message.error('上传文件格式不支持!');\r\n        return false;\r\n      }\r\n      if (!isLt10M) {\r\n        this.$message.error('上传文件大小不能超过 10MB!');\r\n        return false;\r\n      }\r\n      return true;\r\n    },\r\n    \r\n    // 上传文件处理\r\n    uploadFile(options) {\r\n      // 这里应该调用实际的文件上传API\r\n      console.log('文件上传:', options.file);\r\n      // 假设上传成功\r\n      this.fileList.push({\r\n        name: options.file.name,\r\n        url: URL.createObjectURL(options.file)\r\n      });\r\n      options.onSuccess();\r\n    },\r\n    \r\n    // 移除文件\r\n    handleRemove(file) {\r\n      const index = this.fileList.indexOf(file);\r\n      if (index !== -1) {\r\n        this.fileList.splice(index, 1);\r\n      }\r\n    },\r\n    \r\n    // 表单提交\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          // 额外的日期逻辑验证\r\n          if (!this.validateDateLogic()) {\r\n            return;\r\n          }\r\n\r\n          // 根据isEdit标志决定调用哪个API\r\n          const apiCall = this.isEdit ? updateProductionPlan : addProductionPlan;\r\n          \r\n          apiCall(this.form).then(response => {\r\n            if (response.code === 200) {\r\n              this.$modal.msgSuccess(this.isEdit ? \"修改成功\" : \"新增成功\");\r\n              this.cancel();\r\n            } else {\r\n              this.$modal.msgError(response.msg || (this.isEdit ? \"修改失败\" : \"新增失败\"));\r\n            }\r\n          }).catch(() => {\r\n            this.$modal.msgError(\"操作失败，请稍后重试\");\r\n          });\r\n        }\r\n      });\r\n    },\r\n    \r\n    // 取消按钮\r\n    cancel() {\r\n      this.$router.push({ path: \"/sc/plan\" });\r\n    },\r\n\r\n    // 验证开工时间\r\n    validateStartTime(rule, value, callback) {\r\n      if (!value) {\r\n        callback();\r\n        return;\r\n      }\r\n\r\n      const today = new Date();\r\n      today.setHours(0, 0, 0, 0);\r\n      const startDate = new Date(value);\r\n\r\n      // 开工日期不能早于当前日期\r\n      if (startDate < today) {\r\n        callback(new Error('开工日期不能早于当前日期'));\r\n        return;\r\n      }\r\n\r\n      // 如果完工时间已选择，开工时间不能晚于完工时间\r\n      if (this.form.planEndTime) {\r\n        const endDate = new Date(this.form.planEndTime);\r\n        if (startDate > endDate) {\r\n          callback(new Error('开工日期不能晚于完工日期'));\r\n          return;\r\n        }\r\n      }\r\n\r\n      callback();\r\n    },\r\n\r\n    // 验证完工时间\r\n    validateEndTime(rule, value, callback) {\r\n      if (!value) {\r\n        callback();\r\n        return;\r\n      }\r\n\r\n      const endDate = new Date(value);\r\n\r\n      // 如果开工时间已选择，完工时间不能早于开工时间\r\n      if (this.form.planStartTime) {\r\n        const startDate = new Date(this.form.planStartTime);\r\n        if (endDate < startDate) {\r\n          callback(new Error('完工日期不能早于开工日期'));\r\n          return;\r\n        }\r\n      }\r\n\r\n      callback();\r\n    },\r\n\r\n    // 验证需求日期\r\n    validateRequiredDate(rule, value, callback) {\r\n      if (!value) {\r\n        callback();\r\n        return;\r\n      }\r\n\r\n      const requiredDate = new Date(value);\r\n\r\n      // 需求日期不能早于当前日期\r\n      const today = new Date();\r\n      today.setHours(0, 0, 0, 0);\r\n      if (requiredDate < today) {\r\n        callback(new Error('需求日期不能早于当前日期'));\r\n        return;\r\n      }\r\n\r\n      // 如果完工时间已选择，需求日期不能早于完工时间\r\n      if (this.form.planEndTime) {\r\n        const endDate = new Date(this.form.planEndTime);\r\n        if (requiredDate < endDate) {\r\n          callback(new Error('需求日期不能早于完工日期'));\r\n          return;\r\n        }\r\n      }\r\n\r\n      callback();\r\n    },\r\n\r\n    // 综合日期逻辑验证\r\n    validateDateLogic() {\r\n      const today = new Date();\r\n      today.setHours(0, 0, 0, 0);\r\n\r\n      // 检查开工日期\r\n      if (this.form.planStartTime) {\r\n        const startDate = new Date(this.form.planStartTime);\r\n        if (startDate < today) {\r\n          this.$modal.msgError(\"开工日期不能早于当前日期\");\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 检查开工日期和完工日期的关系\r\n      if (this.form.planStartTime && this.form.planEndTime) {\r\n        const startDate = new Date(this.form.planStartTime);\r\n        const endDate = new Date(this.form.planEndTime);\r\n        if (startDate > endDate) {\r\n          this.$modal.msgError(\"开工日期不能晚于完工日期\");\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 检查需求日期\r\n      if (this.form.requiredDate) {\r\n        const requiredDate = new Date(this.form.requiredDate);\r\n        if (requiredDate < today) {\r\n          this.$modal.msgError(\"需求日期不能早于当前日期\");\r\n          return false;\r\n        }\r\n\r\n        // 需求日期不能早于完工日期\r\n        if (this.form.planEndTime) {\r\n          const endDate = new Date(this.form.planEndTime);\r\n          if (requiredDate < endDate) {\r\n            this.$modal.msgError(\"需求日期不能早于完工日期\");\r\n            return false;\r\n          }\r\n        }\r\n      }\r\n\r\n      return true;\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.form-container {\r\n  background-color: #fff;\r\n  padding: 10px;\r\n}\r\n\r\n.el-tabs--border-card {\r\n  box-shadow: none;\r\n}\r\n\r\n.upload-container {\r\n  width: 100%;\r\n  border: 1px dashed #d9d9d9;\r\n  border-radius: 6px;\r\n  text-align: center;\r\n  padding: 20px 0;\r\n  cursor: pointer;\r\n}\r\n\r\n.upload-container:hover {\r\n  border-color: #409EFF;\r\n}\r\n\r\n.upload-area {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.upload-area .el-icon-upload {\r\n  font-size: 48px;\r\n  color: #c0c4cc;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.upload-text {\r\n  font-size: 14px;\r\n  color: #606266;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.upload-hint {\r\n  font-size: 12px;\r\n  color: #909399;\r\n}\r\n\r\n.input-with-select .el-input-group__append {\r\n  background-color: #fff;\r\n}\r\n\r\n.bom-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.bom-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin: 20px 0;\r\n  padding: 30px 0;\r\n}\r\n\r\n.folder-icon {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.bom-text {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.warning-text {\r\n  color: #E6A23C;\r\n  font-size: 12px;\r\n  margin-left: 10px;\r\n}\r\n\r\n.warning-text i {\r\n  margin-right: 5px;\r\n}\r\n\r\n.upload-hidden {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.upload-hidden >>> .el-upload {\r\n  width: 100%;\r\n}\r\n\r\n.upload-hidden >>> .el-upload-dragger {\r\n  width: 100%;\r\n  height: 100%;\r\n  border: none;\r\n  padding: 0;\r\n  margin: 0;\r\n}\r\n\r\n.bom-dialog-header {\r\n  margin-bottom: 15px;\r\n  padding: 10px;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n}\r\n\r\n.product-info {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.product-info span {\r\n  margin-right: 20px;\r\n  line-height: 30px;\r\n}\r\n\r\n.el-radio {\r\n  margin-right: 0;\r\n}\r\n\r\n.pagination-container {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 10px 0;\r\n  font-size: 12px;\r\n}\r\n\r\n.pagination-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.page-size {\r\n  margin-right: 10px;\r\n}\r\n\r\n.total-text {\r\n  color: #606266;\r\n  font-size: 12px;\r\n}\r\n\r\n.bom-info {\r\n  width: 100%;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.bom-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n  padding: 8px;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n}\r\n\r\n.bom-title-info {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.bom-title-info span {\r\n  margin-right: 20px;\r\n  font-weight: bold;\r\n}\r\n\r\n.bom-detail-table {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.bom-action {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.select-bom-button {\r\n  margin-right: 10px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAiXA,IAAAA,eAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AACA,IAAAG,WAAA,GAAAC,sBAAA,CAAAJ,OAAA;AACA,IAAAK,QAAA,GAAAD,sBAAA,CAAAJ,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAM,IAAA;EACAC,KAAA;EACAC,UAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,OAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,KAAA;MACA;MACAC,MAAA;MACA;MACAC,IAAA;QACAC,QAAA;QACAC,QAAA;QACAC,UAAA;QACAC,SAAA;QACAC,SAAA,EAAAC,SAAA;QACAC,WAAA;QACAC,WAAA;QACAC,aAAA;QACAC,WAAA;QACAC,IAAA;QACAC,UAAA;QACAC,aAAA;QACAC,WAAA;QACAC,YAAA;QACAC,MAAA;MACA;MACA;MACAC,YAAA;MACA;MACAC,KAAA;QACAhB,QAAA,GACA;UAAAiB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA,EACA;QACAlB,UAAA,GACA;UAAAgB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAd,WAAA,GACA;UAAAY,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAT,UAAA,GACA;UAAAO,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAR,aAAA,GACA;UAAAU,SAAA,OAAAC,iBAAA;UAAAH,OAAA;QAAA,EACA;QACAP,WAAA,GACA;UAAAS,SAAA,OAAAE,eAAA;UAAAJ,OAAA;QAAA,EACA;QACAN,YAAA,GACA;UAAAQ,SAAA,OAAAG,oBAAA;UAAAL,OAAA;QAAA;MAEA;MACA;MACAM,cAAA;MACA;MACAC,cAAA;MACA;MACAC,iBAAA,GACA;QAAAC,SAAA;QAAAC,SAAA;MAAA,GACA;QAAAD,SAAA;QAAAC,SAAA;MAAA,EACA;MACA;MACAC,QAAA;MAEA;MACAC,oBAAA;MACAC,YAAA;QACAC,OAAA;QACAC,QAAA;QACAC,OAAA;QACA1B,IAAA;QACA2B,IAAA;QACAC,QAAA;MACA;MACAC,WAAA;MACAC,YAAA;MACAC,eAAA;MAEA;MACAC,gBAAA;MACAC,QAAA;QACAT,OAAA;QACAC,QAAA;QACA/B,SAAA,EAAAC;MACA;MACAuC,OAAA;MACAC,QAAA;MACAC,UAAA;MACAC,WAAA;MACAC,aAAA;MACAC,aAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,IAAAlD,QAAA,QAAAmD,MAAA,CAAAC,KAAA,CAAApD,QAAA;IACA,IAAAA,QAAA;MACA,KAAAF,MAAA;MACA,KAAAD,KAAA;MACA,KAAAwD,WAAA,CAAArD,QAAA;IACA;MACA,KAAAF,MAAA;MACA,KAAAD,KAAA;MACA,KAAAyD,gBAAA;IACA;EACA;EACAC,OAAA;IACA;IACAF,WAAA,WAAAA,YAAArD,QAAA;MAAA,IAAAwD,KAAA;MACA,IAAAC,iCAAA,EAAAzD,QAAA,EAAA0D,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAzD,IAAA,GAAA4D,QAAA,CAAA/D,IAAA;QACA;QACA,IAAA4D,KAAA,CAAAzD,IAAA,CAAAK,SAAA;UACAoD,KAAA,CAAAI,iBAAA;QACA;MACA;IACA;IAEA;IACAA,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,4BAAA,OAAA/D,IAAA,CAAAK,SAAA,EAAAsD,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAI,IAAA,YAAAJ,QAAA,CAAAK,IAAA,IAAAL,QAAA,CAAAK,IAAA,CAAAC,MAAA;UACA;UACA,IAAAC,SAAA,GAAAP,QAAA,CAAAK,IAAA,CAAAG,IAAA,WAAAC,CAAA;YAAA,OAAAA,CAAA,CAAAC,UAAA;UAAA;UACA,IAAAH,SAAA;YACAL,MAAA,CAAAd,WAAA,GAAAmB,SAAA;YACAL,MAAA,CAAAS,YAAA;UACA;QACA;MACA;IACA;IAEA;IACAhB,gBAAA,WAAAA,iBAAA;MAAA,IAAAiB,MAAA;MACA,IAAAC,uBAAA,KAAAd,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAI,IAAA;UACAQ,MAAA,CAAAxE,IAAA,CAAAC,QAAA,GAAA2D,QAAA,CAAAc,GAAA;QACA;UACAF,MAAA,CAAAG,QAAA,CAAAC,KAAA;QACA;MACA,GAAAC,KAAA;QACAL,MAAA,CAAAG,QAAA,CAAAC,KAAA;MACA;IACA;IAEA;IACAE,sBAAA,WAAAA,uBAAAC,GAAA;MACA,IAAAA,GAAA;QACA;QACA,KAAAxB,gBAAA;MACA;QACA;QACA,KAAAvD,IAAA,CAAAC,QAAA;MACA;IACA;IAEA;IACA+E,aAAA,WAAAA,cAAA;MACA,KAAAC,KAAA,CAAAC,MAAA,CAAAC,GAAA,CAAAC,KAAA;IACA;IAEA;IACAC,oBAAA,WAAAA,qBAAA;MACA;MACA,SAAAtF,MAAA;QACA,KAAA4E,QAAA,CAAAW,OAAA;QACA;MACA;MACA,KAAArD,oBAAA;MACA,KAAAsD,cAAA;IACA;IAEA;IACAA,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,KAAA5D,cAAA;MACA,IAAA6D,qBAAA;QACAtD,OAAA,OAAAD,YAAA,CAAAC,OAAA;QACAC,QAAA,OAAAF,YAAA,CAAAE,QAAA;QACAC,OAAA,OAAAH,YAAA,CAAAG,OAAA;QACAqD,WAAA,OAAAxD,YAAA,CAAAvB,IAAA;QACAD,WAAA,OAAAwB,YAAA,CAAAI,IAAA;QACAqD,eAAA,OAAAzD,YAAA,CAAAK;MACA,GAAAoB,IAAA,WAAAC,QAAA;QACA4B,MAAA,CAAA5D,cAAA;QACA,IAAAgC,QAAA,CAAAI,IAAA;UACAwB,MAAA,CAAAhD,WAAA,GAAAoB,QAAA,CAAAK,IAAA;UACAuB,MAAA,CAAA/C,YAAA,GAAAmB,QAAA,CAAAgC,KAAA;QACA;MACA,GAAAf,KAAA;QACAW,MAAA,CAAA5D,cAAA;QACA;QACA4D,MAAA,CAAAhD,WAAA,IACA;UAAAqD,UAAA;UAAAC,YAAA;UAAAC,YAAA;UAAAC,WAAA;UAAAC,YAAA;UAAAC,YAAA;UAAAC,gBAAA;QAAA,GACA;UAAAN,UAAA;UAAAC,YAAA;UAAAC,YAAA;UAAAC,WAAA;UAAAC,YAAA;UAAAC,YAAA;UAAAC,gBAAA;QAAA,GACA;UAAAN,UAAA;UAAAC,YAAA;UAAAC,YAAA;UAAAC,WAAA;UAAAC,YAAA;UAAAC,YAAA;UAAAC,gBAAA;QAAA,GACA;UAAAN,UAAA;UAAAC,YAAA;UAAAC,YAAA;UAAAC,WAAA;UAAAC,YAAA;UAAAC,YAAA;UAAAC,gBAAA;QAAA,GACA;UAAAN,UAAA;UAAAC,YAAA;UAAAC,YAAA;UAAAC,WAAA;UAAAC,YAAA;UAAAC,YAAA;UAAAC,gBAAA;QAAA,EACA;QACAX,MAAA,CAAA/C,YAAA;MACA;IACA;IAEA;IACA2D,cAAA,WAAAA,eAAA;MACA,KAAAlE,YAAA,CAAAC,OAAA;MACA,KAAAoD,cAAA;IACA;IAEA;IACAc,iBAAA,WAAAA,kBAAA;MACA,KAAAnE,YAAA;QACAC,OAAA;QACAC,QAAA;QACAC,OAAA;QACA1B,IAAA;QACA2B,IAAA;QACAC,QAAA;MACA;MACA,KAAAgD,cAAA;IACA;IAEA;IACAe,4BAAA,WAAAA,6BAAAC,SAAA;MACA,IAAAA,SAAA,CAAArC,MAAA;QACA,KAAAxB,eAAA,GAAA6D,SAAA;MACA;QACA,KAAA7D,eAAA;MACA;IACA;IAEA;IACA8D,0BAAA,WAAAA,2BAAAC,WAAA;MACA,KAAAvE,YAAA,CAAAC,OAAA,GAAAsE,WAAA;MACA,KAAAlB,cAAA;IACA;IAEA;IACAmB,uBAAA,WAAAA,wBAAAC,IAAA;MACA,KAAAzE,YAAA,CAAAE,QAAA,GAAAuE,IAAA;MACA,KAAAzE,YAAA,CAAAC,OAAA;MACA,KAAAoD,cAAA;IACA;IAEA;IACAqB,oBAAA,WAAAA,qBAAA;MACA,SAAAlE,eAAA;QACA,KAAA1C,IAAA,CAAAK,SAAA,QAAAqC,eAAA,CAAAmD,UAAA;QACA,KAAA7F,IAAA,CAAAO,WAAA,QAAAmC,eAAA,CAAAqD,YAAA;QACA,KAAA/F,IAAA,CAAAQ,WAAA,QAAAkC,eAAA,CAAAoD,YAAA;QACA,KAAA9F,IAAA,CAAAS,aAAA,QAAAiC,eAAA,CAAAsD,WAAA;QACA,KAAAhG,IAAA,CAAAU,WAAA,QAAAmG,iBAAA,MAAAnE,eAAA;QACA,KAAA1C,IAAA,CAAAW,IAAA,QAAA+B,eAAA,CAAAwD,YAAA;QACA,KAAAjE,oBAAA;;QAEA;QACA,KAAAe,WAAA;QACA,KAAAC,aAAA;MACA;QACA,KAAA0B,QAAA,CAAAW,OAAA;MACA;IACA;IAEA;IACAuB,iBAAA,WAAAA,kBAAAC,GAAA,EAAAC,MAAA;MACA,IAAAzE,IAAA,GAAAwE,GAAA,CAAAb,YAAA;MACA,IAAAe,MAAA,QAAAC,kBAAA,CAAA7C,IAAA,WAAA8C,IAAA;QAAA,OAAAA,IAAA,CAAAnF,SAAA,IAAAO,IAAA;MAAA;MACA,OAAA0E,MAAA,GAAAA,MAAA,CAAAlF,SAAA,GAAAQ,IAAA;IACA;IAEA;IACA6E,SAAA,WAAAA,UAAA;MACA,UAAAnH,IAAA,CAAAK,SAAA;QACA,KAAAsE,QAAA,CAAAW,OAAA;QACA;MACA;MACA,KAAA3C,gBAAA;MACA,KAAAyE,UAAA;IACA;IAEA;IACAA,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAtE,UAAA;MACA,IAAAgB,4BAAA,OAAA/D,IAAA,CAAAK,SAAA,EAAAsD,IAAA,WAAAC,QAAA;QACAyD,MAAA,CAAAtE,UAAA;QACA,IAAAa,QAAA,CAAAI,IAAA;UACAqD,MAAA,CAAAxE,OAAA,GAAAe,QAAA,CAAAK,IAAA;UACAoD,MAAA,CAAAvE,QAAA,GAAAc,QAAA,CAAAgC,KAAA;UACA,KAAAyB,MAAA,CAAAxE,OAAA,IAAAwE,MAAA,CAAAxE,OAAA,CAAAqB,MAAA;YACAmD,MAAA,CAAA1C,QAAA,CAAA2C,IAAA;UACA;YACA;YACA,IAAAC,UAAA,GAAAF,MAAA,CAAAxE,OAAA,CAAAuB,IAAA,WAAAC,CAAA;cAAA,OAAAA,CAAA,CAAAC,UAAA;YAAA;YACA,IAAAiD,UAAA;cACAF,MAAA,CAAAG,eAAA,CAAAD,UAAA;YACA;UACA;QACA;UACAF,MAAA,CAAAxE,OAAA;UACAwE,MAAA,CAAAvE,QAAA;QACA;MACA,GAAA+B,KAAA;QACAwC,MAAA,CAAAtE,UAAA;QACAsE,MAAA,CAAA1C,QAAA,CAAAC,KAAA;MACA;IACA;IAEA;IACA4C,eAAA,WAAAA,gBAAAV,GAAA;MACA,KAAA9D,WAAA,GAAA8D,GAAA;MACA,KAAA7D,aAAA,GAAA6D,GAAA,CAAAW,MAAA;IACA;IAEA;IACAC,gBAAA,WAAAA,iBAAA;MACA,SAAA1E,WAAA;QACA,KAAAL,gBAAA;QACA;QACA,KAAA4B,YAAA;MACA;QACA,KAAAI,QAAA,CAAAW,OAAA;MACA;IACA;IAEA;IACAf,YAAA,WAAAA,aAAA;MAAA,IAAAoD,MAAA;MACA,IAAAC,uBAAA,OAAA5E,WAAA,CAAAyE,MAAA,EAAA9D,IAAA,WAAAC,QAAA;QACAiE,OAAA,CAAAC,GAAA,iBAAAlE,QAAA;QACA,IAAAA,QAAA,IAAAA,QAAA,CAAAI,IAAA;UACA2D,MAAA,CAAAzE,aAAA,GAAAU,QAAA,CAAAK,IAAA;QACA;UACA0D,MAAA,CAAAzE,aAAA;UACAyE,MAAA,CAAAhD,QAAA,CAAAC,KAAA,kBAAAhB,QAAA,GAAAA,QAAA,CAAAc,GAAA;QACA;MACA,GAAAG,KAAA,WAAAD,KAAA;QACAiD,OAAA,CAAAjD,KAAA,mBAAAA,KAAA;QACA+C,MAAA,CAAAhD,QAAA,CAAAC,KAAA;QACA+C,MAAA,CAAAzE,aAAA;MACA;IACA;IAEA;IACA6E,gBAAA,WAAAA,iBAAA;MACA,KAAA/E,WAAA;MACA,KAAAC,aAAA;MACA,KAAAC,aAAA;IACA;IAEA;IACA8E,YAAA,WAAAA,aAAAC,IAAA;MACA,IAAAC,WAAA,sDAAAC,IAAA,CAAAF,IAAA,CAAAzI,IAAA;MACA,IAAA4I,OAAA,GAAAH,IAAA,CAAAtB,IAAA;MAEA,KAAAuB,WAAA;QACA,KAAAvD,QAAA,CAAAC,KAAA;QACA;MACA;MACA,KAAAwD,OAAA;QACA,KAAAzD,QAAA,CAAAC,KAAA;QACA;MACA;MACA;IACA;IAEA;IACAyD,UAAA,WAAAA,WAAAC,OAAA;MACA;MACAT,OAAA,CAAAC,GAAA,UAAAQ,OAAA,CAAAL,IAAA;MACA;MACA,KAAAjG,QAAA,CAAAuG,IAAA;QACA/I,IAAA,EAAA8I,OAAA,CAAAL,IAAA,CAAAzI,IAAA;QACAgJ,GAAA,EAAAC,GAAA,CAAAC,eAAA,CAAAJ,OAAA,CAAAL,IAAA;MACA;MACAK,OAAA,CAAAK,SAAA;IACA;IAEA;IACAC,YAAA,WAAAA,aAAAX,IAAA;MACA,IAAAY,KAAA,QAAA7G,QAAA,CAAA8G,OAAA,CAAAb,IAAA;MACA,IAAAY,KAAA;QACA,KAAA7G,QAAA,CAAA+G,MAAA,CAAAF,KAAA;MACA;IACA;IAEA;IACAG,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAhE,KAAA,SAAAiE,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACA,KAAAF,MAAA,CAAAG,iBAAA;YACA;UACA;;UAEA;UACA,IAAAC,OAAA,GAAAJ,MAAA,CAAAlJ,MAAA,GAAAuJ,oCAAA,GAAAC,iCAAA;UAEAF,OAAA,CAAAJ,MAAA,CAAAjJ,IAAA,EAAA2D,IAAA,WAAAC,QAAA;YACA,IAAAA,QAAA,CAAAI,IAAA;cACAiF,MAAA,CAAAO,MAAA,CAAAC,UAAA,CAAAR,MAAA,CAAAlJ,MAAA;cACAkJ,MAAA,CAAAS,MAAA;YACA;cACAT,MAAA,CAAAO,MAAA,CAAAG,QAAA,CAAA/F,QAAA,CAAAc,GAAA,KAAAuE,MAAA,CAAAlJ,MAAA;YACA;UACA,GAAA8E,KAAA;YACAoE,MAAA,CAAAO,MAAA,CAAAG,QAAA;UACA;QACA;MACA;IACA;IAEA;IACAD,MAAA,WAAAA,OAAA;MACA,KAAAE,OAAA,CAAArB,IAAA;QAAAsB,IAAA;MAAA;IACA;IAEA;IACArI,iBAAA,WAAAA,kBAAAsI,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,KAAAD,KAAA;QACAC,QAAA;QACA;MACA;MAEA,IAAAC,KAAA,OAAAC,IAAA;MACAD,KAAA,CAAAE,QAAA;MACA,IAAAC,SAAA,OAAAF,IAAA,CAAAH,KAAA;;MAEA;MACA,IAAAK,SAAA,GAAAH,KAAA;QACAD,QAAA,KAAAK,KAAA;QACA;MACA;;MAEA;MACA,SAAArK,IAAA,CAAAc,WAAA;QACA,IAAAwJ,OAAA,OAAAJ,IAAA,MAAAlK,IAAA,CAAAc,WAAA;QACA,IAAAsJ,SAAA,GAAAE,OAAA;UACAN,QAAA,KAAAK,KAAA;UACA;QACA;MACA;MAEAL,QAAA;IACA;IAEA;IACAvI,eAAA,WAAAA,gBAAAqI,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,KAAAD,KAAA;QACAC,QAAA;QACA;MACA;MAEA,IAAAM,OAAA,OAAAJ,IAAA,CAAAH,KAAA;;MAEA;MACA,SAAA/J,IAAA,CAAAa,aAAA;QACA,IAAAuJ,SAAA,OAAAF,IAAA,MAAAlK,IAAA,CAAAa,aAAA;QACA,IAAAyJ,OAAA,GAAAF,SAAA;UACAJ,QAAA,KAAAK,KAAA;UACA;QACA;MACA;MAEAL,QAAA;IACA;IAEA;IACAtI,oBAAA,WAAAA,qBAAAoI,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,KAAAD,KAAA;QACAC,QAAA;QACA;MACA;MAEA,IAAAjJ,YAAA,OAAAmJ,IAAA,CAAAH,KAAA;;MAEA;MACA,IAAAE,KAAA,OAAAC,IAAA;MACAD,KAAA,CAAAE,QAAA;MACA,IAAApJ,YAAA,GAAAkJ,KAAA;QACAD,QAAA,KAAAK,KAAA;QACA;MACA;;MAEA;MACA,SAAArK,IAAA,CAAAc,WAAA;QACA,IAAAwJ,OAAA,OAAAJ,IAAA,MAAAlK,IAAA,CAAAc,WAAA;QACA,IAAAC,YAAA,GAAAuJ,OAAA;UACAN,QAAA,KAAAK,KAAA;UACA;QACA;MACA;MAEAL,QAAA;IACA;IAEA;IACAZ,iBAAA,WAAAA,kBAAA;MACA,IAAAa,KAAA,OAAAC,IAAA;MACAD,KAAA,CAAAE,QAAA;;MAEA;MACA,SAAAnK,IAAA,CAAAa,aAAA;QACA,IAAAuJ,SAAA,OAAAF,IAAA,MAAAlK,IAAA,CAAAa,aAAA;QACA,IAAAuJ,SAAA,GAAAH,KAAA;UACA,KAAAT,MAAA,CAAAG,QAAA;UACA;QACA;MACA;;MAEA;MACA,SAAA3J,IAAA,CAAAa,aAAA,SAAAb,IAAA,CAAAc,WAAA;QACA,IAAAsJ,UAAA,OAAAF,IAAA,MAAAlK,IAAA,CAAAa,aAAA;QACA,IAAAyJ,OAAA,OAAAJ,IAAA,MAAAlK,IAAA,CAAAc,WAAA;QACA,IAAAsJ,UAAA,GAAAE,OAAA;UACA,KAAAd,MAAA,CAAAG,QAAA;UACA;QACA;MACA;;MAEA;MACA,SAAA3J,IAAA,CAAAe,YAAA;QACA,IAAAA,YAAA,OAAAmJ,IAAA,MAAAlK,IAAA,CAAAe,YAAA;QACA,IAAAA,YAAA,GAAAkJ,KAAA;UACA,KAAAT,MAAA,CAAAG,QAAA;UACA;QACA;;QAEA;QACA,SAAA3J,IAAA,CAAAc,WAAA;UACA,IAAAwJ,QAAA,OAAAJ,IAAA,MAAAlK,IAAA,CAAAc,WAAA;UACA,IAAAC,YAAA,GAAAuJ,QAAA;YACA,KAAAd,MAAA,CAAAG,QAAA;YACA;UACA;QACA;MACA;MAEA;IACA;EACA;AACA", "ignoreList": []}]}