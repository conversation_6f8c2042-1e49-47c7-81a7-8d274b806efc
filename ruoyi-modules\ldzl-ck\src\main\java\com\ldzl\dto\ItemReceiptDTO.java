package com.ldzl.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ldzl.pojo.CkItemRecpt;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 添加采购入库
 */
@Data
public class ItemReceiptDTO implements Serializable {
    /**
     * 采购单信息
     */
    private CkItemRecpt itemRecpt;

    /**
     * 采购商品详情信息
     */
    List<ItemDetailDTO> listItemDetail;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
