package com.ldzl.mapper;

import com.ldzl.pojo.CkItemRecpt;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ck_item_recpt(物料入库单表)】的数据库操作Mapper
* @createDate 2025-07-18 16:44:31
* @Entity com.ldzl.pojo.CkItemRecpt
*/
public interface CkItemRecptMapper extends BaseMapper<CkItemRecpt> {

    /**
     * 查询入库单
     * @param itemRecpt
     * @return
     */
    List<CkItemRecpt> selectRecpt(CkItemRecpt itemRecpt);

    /**
     * 查询 待质检的入库单
     * @param recpt_code
     * @param recpt_name
     * @return
     */
    List<CkItemRecpt> selectCkProductRecptDetectSingle(@Param("recpt_code") String recpt_code,
                                                       @Param("recpt_name") String recpt_name);

    /**
     * 修改入库单状态 合格或不合格
     * @param recpt_id
     * @param status
     * @return
     */
    int updateCkProductRecptStatusStatus(@Param("recpt_id") Long recpt_id,
                                         @Param("status") Long status);
}




