package com.cssl.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;
import org.apache.poi.ss.formula.ptg.Ptg;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("basic_units")
public class BasicUnits extends BaseEntity implements Serializable {
    @TableId(value ="unit_id",type = IdType.AUTO)
    private Long unit_id; // 计量单位id
    private String unit_code; // 单位编码
    private String unit_name; // 单位名称
    private Integer isPrimary_unit; // 是否为主单位 (0: 不是主单位; 1: 是主单位)
    private BigDecimal conversionrate; // 与主单位的换算比例
    private String unit_status; // 是否启用 (1: 启用; 0: 不启用)
    private String remarks; // 备注
    private String is_delete; // 逻辑删除
    private String create_by;
    private Date create_time;
    private String update_by;
    private Date update_time;
    private String main_unit;
}
