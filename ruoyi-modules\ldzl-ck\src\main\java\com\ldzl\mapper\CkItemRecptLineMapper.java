package com.ldzl.mapper;

import com.ldzl.pojo.CkItemRecptLine;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ck_item_recpt_line(物料入库单行表)】的数据库操作Mapper
* @createDate 2025-07-18 16:44:31
* @Entity com.ldzl.pojo.CkItemRecptLine
*/
public interface CkItemRecptLineMapper extends BaseMapper<CkItemRecptLine> {

    /**
     * 查询采购入库单行
     * @param recpt_id
     * @return
     */
    List<CkItemRecptLine> selectRecpt_id(@Param("recpt_id") Long recpt_id);

    /**
     * 查询采购入库单行 修改订单时回显入库商品
     * @param recpt_id
     * @return
     */
    List<CkItemRecptLine> selectRecptLine_feedback( @Param("recpt_id") Long recpt_id);

    int updateIs_delete(@Param("line_id") Long line_id);
}




