package com.cssl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cssl.pojo.BasicOperational;
import java.util.List;

/**
 * 工艺路线 Service接口
 *
 * <AUTHOR>
 */
public interface BasicOperationalService extends IService<BasicOperational> {
    /**
     * 查询工艺路线
     *
     * @param operationalId 工艺路线主键
     * @return 工艺路线
     */
    public BasicOperational selectBasicOperationalById(Long operationalId);

    /**
     * 查询工艺路线列表
     *
     * @param basicOperational 工艺路线
     * @return 工艺路线集合
     */
    public List<BasicOperational> selectBasicOperationalList(BasicOperational basicOperational);

    //新增工艺路线
    public int insertBasicOperational(BasicOperational basicOperational);

    //更新工艺路线
    public int updateBasicOperational(BasicOperational basicOperational);

    //删除工艺路线
    public int deleteBasicOperationalById(Long operational_id);



    //批量删除工艺路线
    public int deleteBatchBasicOperational(List<Long> operationalIds);

    /**
     * 根据产品ID查询工艺路线列表
     *
     * @param productId 产品ID
     * @return 工艺路线集合
     */
    public List<BasicOperational> selectBasicOperationalByProductId(Long productId);

    //查询工艺路线
    public List<BasicOperational> listBasicOperational(BasicOperational basicOperational);
}





