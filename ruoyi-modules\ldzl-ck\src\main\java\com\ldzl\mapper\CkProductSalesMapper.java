package com.ldzl.mapper;

import com.ldzl.pojo.CkProductSales;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ldzl.pojo.CkProductSalesLine;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ck_product_sales(销售出库单表)】的数据库操作Mapper
* @createDate 2025-07-24 17:24:38
* @Entity com.ldzl.pojo.CkProductSales
*/
public interface CkProductSalesMapper extends BaseMapper<CkProductSales> {

    /**
     * 查询待质检的出库单
     * @param sales_code
     * @param sales_name
     * @return
     */
    List<CkProductSales> selectCkProductSales(@Param("sales_code") String sales_code,
                                              @Param("sales_name") String sales_name);


    /**
     * 修改出库单状态
     * @param status
     * @param sales_id
     * @return
     */
    int updateCkProductSalesStatusStatus(@Param("status") String status,
                                         @Param("sales_id") Long sales_id);

}




