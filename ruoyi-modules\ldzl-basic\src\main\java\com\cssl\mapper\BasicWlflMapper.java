package com.cssl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cssl.pojo.BasicWlfl;

import java.util.List;

public interface BasicWlflMapper extends BaseMapper<BasicWlfl> {


    //新增父分类
    public int insertBasicWlfl(BasicWlfl basicWlfl);

    //修改父分类
    public int updateBasicWlflz(BasicWlfl basicWlfl);

    //删除父分类
    public int delByBasicWlflzInt(Long material_classification_id);

    //查询父分类
    public List<BasicWlfl> findBasicWlflz();


    List<BasicWlfl> selectBasicWlflWithChildren();
}
