package com.cssl.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cssl.mapper.BasicTeamMapper;
import com.cssl.mapper.BasicTeamdetailsMapper;
import com.cssl.pojo.BasicTeam;
import com.cssl.pojo.BasicTeamdetails;
import com.cssl.pojo.vo.BasicTeamVo;
import com.cssl.service.BasicTeamService;
import com.cssl.service.BasicTeamdetailsService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class BasicTeamdetailsServiceImpl extends ServiceImpl<BasicTeamdetailsMapper, BasicTeamdetails> implements BasicTeamdetailsService {
    @Resource
    private BasicTeamdetailsMapper basicTeamdetailsMapper;
    @Override
    public List<BasicTeamVo> selectTeamdetailsById(Long team_id) {
        return basicTeamdetailsMapper.selectTeamdetailsById(team_id);
    }

    @Override
    public List<BasicTeamdetails> selectTeamdetailsById1(Long team_id) {
        return basicTeamdetailsMapper.selectTeamdetailsById1(team_id);
    }

    @Override
    public int delBatchTeamdetails(List<Long> teamdetails_ids) {
        return basicTeamdetailsMapper.delBatchTeamdetails(teamdetails_ids);
    }

    @Override
    public int delTeamdetailsById(Long team_id) {
        return basicTeamdetailsMapper.delTeamdetailsById(team_id);
    }

    @Override
    public int delBatchTeamdetails1(List<Long> team_ids) {
        return basicTeamdetailsMapper.delBatchTeamdetails1(team_ids);
    }
}
