package com.cssl.mapper;

import com.cssl.pojo.BasicNumbers;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

public interface BasicNumbersMapper extends BaseMapper<BasicNumbers>{
    /**
     * 查询自动生成编号
     *
     * @param enCode 自动生成编号主键
     * @return 自动生成编号
     */
    public BasicNumbers selectBasicNumbersByEnCode(Long enCode);

    /**
     * 查询自动生成编号列表
     *
     * @param basicNumbers 自动生成编号
     * @return 自动生成编号集合
     */
    public List<BasicNumbers> selectBasicNumbersList(BasicNumbers basicNumbers);

    /**
     * 新增自动生成编号
     *
     * @param basicNumbers 自动生成编号
     * @return 结果
     */
    public int insertBasicNumbers(BasicNumbers basicNumbers);

    /**
     * 修改自动生成编号
     *
     * @param basicNumbers 自动生成编号
     * @return 结果
     */
    public int updateBasicNumbers(BasicNumbers basicNumbers);

    /**
     * 删除自动生成编号
     *
     * @param enCode 自动生成编号主键
     * @return 结果
     */
    public int deleteBasicNumbersByEnCode(Long enCode);

    /**
     * 批量删除自动生成编号
     *
     * @param enCodes 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBasicNumbersByEnCodes(Long[] enCodes);
}

