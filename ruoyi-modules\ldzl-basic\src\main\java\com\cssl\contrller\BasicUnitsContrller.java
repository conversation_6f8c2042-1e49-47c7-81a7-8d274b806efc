package com.cssl.contrller;

import com.cssl.pojo.BasicUnits;
import com.cssl.pojo.BasicWlfl;
import com.cssl.pojo.BasicWlflz;
import com.cssl.service.BasicUnitsService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/units")
public class BasicUnitsContrller extends BaseController {

    @Resource
    private BasicUnitsService basicUnitsService;

    //查询所有单位信息
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody BasicUnits basicUnits)
    {
        startPage();
        List<BasicUnits> list =basicUnitsService.listBasicUnits(basicUnits);
        return getDataTable(list);
    }

    //添加主单位
    @Log(title = "添加主单位", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasicUnits basicUnits)
    {
        return toAjax(basicUnitsService.addBasicUnits(basicUnits));
    }

    //查询正在启用单位信息
    @GetMapping("/find")
    public TableDataInfo find()
    {
        startPage();
        List<BasicUnits> list =basicUnitsService.findByBasicUnits();
        return getDataTable(list);
    }



    //修改单位
    @Log(title = "修改单位", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasicUnits basicUnits)
    {
        return toAjax(basicUnitsService.updateBasicUnits(basicUnits));
    }

    //删除单位
    @Log(title = "删除单位", businessType = BusinessType.UPDATE)
    @PutMapping("/dp/{unit_id}")
    public AjaxResult edit1(@PathVariable Long unit_id)
    {
        return toAjax(basicUnitsService.delBasicUnits(unit_id));
    }

    //批量删除单位
    @Log(title = "批量删除单位", businessType = BusinessType.UPDATE)
    @PutMapping("/batch/{unit_ids}")
    public AjaxResult edit(@PathVariable List<Long> unit_ids)
    {
        return toAjax(basicUnitsService.delBatchBasicUnits(unit_ids));
    }

}
