package com.ruoyi.system.api.domain;

import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class BasicOperationalDto extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private Long operationalId;

    private String operationalCode;

    private String operationalName;

    private String operationalStatus;

    private String operationalDescription;

    private Long processId;

    private Long productId;
    
    private String isDelete;
} 