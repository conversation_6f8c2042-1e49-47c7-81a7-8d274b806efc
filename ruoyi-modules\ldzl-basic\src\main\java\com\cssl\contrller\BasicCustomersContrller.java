package com.cssl.contrller;

import com.cssl.pojo.BasicCustomers;
import com.cssl.pojo.BasicProduct;
import com.cssl.service.BasicCustomersService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/customers")
public class BasicCustomersContrller extends BaseController {
    @Resource
    private BasicCustomersService basicCustomersService;


    //查询所有客户信息
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody BasicCustomers basicCustomers)
    {
        startPage();
        List<BasicCustomers> list =basicCustomersService.listBasicCustomers(basicCustomers);
        return getDataTable(list);
    }

    //添加客户信息
    @Log(title = "添加客户信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasicCustomers basicCustomers)
    {

        return toAjax(basicCustomersService.addBasicCustomers(basicCustomers));
    }

    //修改客户信息
    @Log(title = "修改客户信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasicCustomers basicCustomers)
    {
        return toAjax(basicCustomersService.updateCustomers(basicCustomers));
    }

    //删除客户信息
    @Log(title = "删除客户信息", businessType = BusinessType.UPDATE)
    @PutMapping("/dp/{customer_id}")
    public AjaxResult edit1(@PathVariable Long customer_id)
    {
        return toAjax(basicCustomersService.delCustomers(customer_id));
    }

    //批量删除单位
    @Log(title = "批量删除单位", businessType = BusinessType.UPDATE)
    @PutMapping("/batch/{customer_ids}")
    public AjaxResult edit(@PathVariable List<Long> customer_ids)
    {
        return toAjax(basicCustomersService.delBatchCustomers(customer_ids));
    }
}
