package com.cssl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cssl.pojo.BasicProcess;

import java.util.List;

public interface BasicProcessMapper extends BaseMapper<BasicProcess> {
    //查询所有工序信息
    public List<BasicProcess> listBasicProcess(BasicProcess basicProcess);

    //添加工序信息
    public int addBasicProcess(BasicProcess basicProcess);


    //修改工序信息
    public int updateBasicProcess(BasicProcess basicProcess);

    //删除工序信息
    public int delBasicProcess(Long process_id);

    //批量删除工序信息
    public int delBatchBasicProcess(List<Long> processIds);
}
