package com.cssl.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

@Data
@TableName("basic_factory")
public class BasicFactory extends BaseEntity {
    @TableId(value ="factory_id",type = IdType.AUTO)
    private Long factory_id; // 工厂id
    private String factory_code; // 工厂编号
    private String factory_name; // 工厂名称
    private String factory_address; // 工厂地址
    private String remarks; // 备注
    private String is_delete; // 逻辑删除
    private String create_by; // 创建人
    private Date create_time; // 创建时间
    private String update_by; // 更新人
    private Date update_time; // 更新时间
}
