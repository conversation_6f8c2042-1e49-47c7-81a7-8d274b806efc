package com.cssl.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cssl.mapper.BasicWlflzMapper;
import com.cssl.mapper.BasicWlglMapper;
import com.cssl.pojo.BasicWlflz;
import com.cssl.pojo.BasicWlgl;
import com.cssl.service.BasicWlflzService;
import com.cssl.service.BasicWlglService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.web.page.PageDomain;
import com.ruoyi.common.core.web.page.TableSupport;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.RemoteFileService;
import com.ruoyi.system.api.domain.SysFile;

import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Service
public class BasicWlglServiceImpl extends ServiceImpl<BasicWlglMapper, BasicWlgl> implements BasicWlglService {
    @Resource
    private BasicWlglMapper basicWlglMapper;
    @Resource
    private RemoteFileService remoteFileService;
    @Override
    public List<BasicWlgl> listBasicWlgl(BasicWlgl basicWlgl) {

        return basicWlglMapper.listBasicWlgl(basicWlgl);
    }

    @Override
    public int addBasicWlgl(BasicWlgl basicWlgl) {
        basicWlgl.setCreate_time(DateUtils.getNowDate());
        basicWlgl.setIs_detele("0");
        basicWlgl.setCreate_by(SecurityUtils.getUsername());
        System.out.println("shh:"+basicWlgl);
        if(basicWlgl.getFile()!=null){
            R<SysFile> upload = remoteFileService.upload(basicWlgl.getFile());
            SysFile data = upload.getData();
            String url = data.getUrl();
            basicWlgl.setImg(url);
            return basicWlglMapper.addBasicWlgl(basicWlgl);
        }
        return basicWlglMapper.addBasicWlgl(basicWlgl);
    }

    @Override
    public int updateBasicwlgl(BasicWlgl basicWlgl) {
        basicWlgl.setUpdate_time(new Date());
        basicWlgl.setUpdate_by(SecurityUtils.getUsername());
        return basicWlglMapper.updateBasicwlgl(basicWlgl);
    }

    @Override
    public int delBasicWlgl(Long material_id) {
        return basicWlglMapper.delBasicWlgl(material_id);
    }
}
