package com.ruoyi.system.api.factory;

import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.system.api.domain.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.system.api.BasicService;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.ArrayList;
import java.util.List;

/**
 * 基础服务降级处理
 *
 * <AUTHOR>
 */
@Component
public class BasicServiceFallbackFactory implements FallbackFactory<BasicService>
{
    private static final Logger log = LoggerFactory.getLogger(BasicServiceFallbackFactory.class);

    @Override
    public BasicService create(Throwable throwable)
    {
        log.error("基础服务调用失败:{}", throwable.getMessage());
        return new BasicService()
        {




            @Override
            public TableDataInfo find(BasicWlflz basicWlflz) {
                return null;
            }

            @Override
            public TableDataInfo list(BasicWlgl basicWlgl)
            {
                log.error("获取物料信息失败:{}", throwable.getMessage());
                return new TableDataInfo();
            }

            @Override
            public TableDataInfo find(BasicUnits basicUnits)
            {
                log.error("获取单位信息失败:{}", throwable.getMessage());
                return new TableDataInfo();
            }

            @Override
            public TableDataInfo list(BasicProduct basicProduct)
            {
                log.error("获取产品信息失败:{}", throwable.getMessage());
                return new TableDataInfo();
            }

            @Override
            public TableDataInfo listAll(BasicProduct basicProduct, Integer pageNum, Integer pageSize) {
                log.error("获取所有产品信息失败:{}", throwable.getMessage());
                return new TableDataInfo();
            }

            @Override
            public AjaxResult automaticallyNumbers(Long encode) {
                log.error("自动生成编号失败:{}", throwable.getMessage());
                return new AjaxResult();
            }

            @Override
            public TableDataInfo list(BasicSuppliers basicSuppliers) {
                log.error("获取所有供应商信息失败:{}", throwable.getMessage());
                return new TableDataInfo();
            }

            @Override
            public List<BasicWlfl> selectBasicWlflWithChildren() {
                log.error("获取一对多分类信息失败:{}", throwable.getMessage());
                return new ArrayList<>();
            }


        };
    }
}