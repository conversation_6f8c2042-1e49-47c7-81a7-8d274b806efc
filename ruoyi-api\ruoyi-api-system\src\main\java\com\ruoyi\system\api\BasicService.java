package com.ruoyi.system.api;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.system.api.domain.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(contextId = "basicService", value = "ldzl-basic", fallbackFactory = com.ruoyi.system.api.factory.BasicServiceFallbackFactory.class)
public interface BasicService {

    @PostMapping("/wlflz/find")
    public TableDataInfo find( BasicWlflz basicWlflz);

    //查询所有物料信息
    @PostMapping("/wlgl/list")
    public TableDataInfo list(@RequestBody BasicWlgl basicWlgl);

    //查询正在启用单位信息
    @PostMapping("/units/find")
    public TableDataInfo find(@RequestBody BasicUnits basicUnits);
    //查询产品
    @PostMapping("/pro/list")
    public TableDataInfo list(@RequestBody BasicProduct basicProduct);

    //查询所有产品（使用BasicProductMapperlist方法）
    @PostMapping("/pro/listAll")
    public TableDataInfo listAll(@RequestBody BasicProduct basicProduct, @RequestParam(value = "pageNum", required = false) Integer pageNum, @RequestParam(value = "pageSize", required = false) Integer pageSize);

    //自动编码
    @GetMapping("/basic/numbers/auto/{encode}")
    public AjaxResult automaticallyNumbers(@PathVariable("encode") Long encode);

    //查询供应商信息
    @PostMapping("/sup/list")
    public TableDataInfo list(@RequestBody BasicSuppliers basicSuppliers);


    /**
     * 查询所有分类 一对多
     * @return
     */
    @PostMapping("/wlfl/selectBasicWlflWithChildren")
    List<BasicWlfl> selectBasicWlflWithChildren();

}