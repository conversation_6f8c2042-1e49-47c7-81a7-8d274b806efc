package com.cssl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cssl.pojo.BasicWlfl;
import com.cssl.pojo.BasicWlflz;

import java.util.List;

public interface BasicWlflzService extends IService<BasicWlflz> {
    //添加子分类
    public int insertBasicWlflz(BasicWlflz basicWlflz) ;
    //查询所有分类
    public List<BasicWlflz> selectBasicWlflzList(BasicWlflz basicWlflz);

    //修改子分类

    public int updateBasicWlflz(BasicWlflz basicWlflz);

    //删除子分类

    public int delBasicWlflz(Long material_subcategory_id);

}
