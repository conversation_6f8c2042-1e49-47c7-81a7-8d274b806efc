package com.ruoyi.system.api.factory;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.system.api.RemoteProcessService;
import com.ruoyi.system.api.domain.BasicProcessDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class RemoteProcessFallbackFactory implements FallbackFactory<RemoteProcessService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteProcessFallbackFactory.class);

    @Override
    public RemoteProcessService create(Throwable cause) {
        log.error("工序服务调用失败:{}", cause.getMessage());
        return new RemoteProcessService() {
            @Override
            public R<List<BasicProcessDto>> getProcessListByIds(List<Long> processIds, String source) {
                return R.fail("获取工序列表失败:" + cause.getMessage());
            }
        };
    }
} 