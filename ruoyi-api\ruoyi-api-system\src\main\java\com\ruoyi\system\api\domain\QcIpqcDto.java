package com.ruoyi.system.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


@Data
@TableName("qc_ipqc")
public class QcIpqcDto {
    private static final long serialVersionUID = 1L;

    /** 检验单ID */
    @TableId(value ="ipqcId",type = IdType.AUTO)
    private Long ipqcId;

    /** 检验单编号 */
    @Excel(name = "检验单编号")
    private String ipqcCode;

    /** 检验单名称 */
    @Excel(name = "检验单名称")
    private String ipqcName;

    /** 检验类型 */
    @Excel(name = "检验类型")
    private String ipqcType;

    /** 检验模板ID */
    private Long templateId;

    /** 来源单据ID */
    private Long sourceDocId;

    /** 来源单据类型 */
    @Excel(name = "来源单据类型")
    private String sourceDocType;

    /** 来源单据编号 */
    @Excel(name = "来源单据编号")
    private String sourceDocCode;

    /** 来源单据行ID */
    private Long sourceLineId;

    /** 工单ID */
    private Long workOrderId;

    /** 工单编码 */
    @Excel(name = "工单编码")
    private String workOrderCode;

    /** 工单名称 */
    private String workOrderName;

    /** 任务ID */
    private Long taskId;

    /** 任务编号 */
    @Excel(name = "任务编号")
    private String taskCode;

    /** 任务名称 */
    private String taskName;

    /** 工厂ID */
    private Long factoryId;

    /** 工厂编号 */
    @Excel(name = "工厂编号")
    private String factoryCode;

    /** 工厂名称 */
    private String factoryName;

    /** 工序ID */
    private Long processId;

    /** 工序编码 */
    @Excel(name = "工序编码")
    private String processCode;

    /** 工序名称 */
    private String processName;

    /** 物料ID */
    private Long productId;

    /** 物料编码 */
    @Excel(name = "物料编码")
    private String productCode;

    /** 物料名称 */
    @Excel(name = "物料名称")
    private String productName;

    /** 规格型号 */
    @Excel(name = "规格型号")
    private String productSfn;

    /** 单位 */
    @Excel(name = "单位")
    private String unit;

    /** 检测数量 */
    @Excel(name = "检测数量")
    private BigDecimal quantityCheckNum;

    /** 不合格数 */
    @Excel(name = "不合格数")
    private BigDecimal quantityUnqualifiedNum;

    /** 合格品数量 */
    @Excel(name = "合格品数量")
    private BigDecimal quantityQualifiedNum;

    /** 致命缺陷率 */
    private BigDecimal crRate;

    /** 严重缺陷率 */
    private BigDecimal majRate;

    /** 轻微缺陷率 */
    private BigDecimal minRate;

    /** 致命缺陷数量 */
    private BigDecimal crQuantityNum;

    /** 严重缺陷数量 */
    private BigDecimal majQuantityNum;

    /** 轻微缺陷数量 */
    private BigDecimal minQuantityNum;

    /** 检测结果 */
    @Excel(name = "检测结果")
    private String checkResult;

    /** 检测日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "检测日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date inspectDate;

    /** 检测人员 */
    private String inspector;

    /** 单据状态(1/合格，2/不合格，3/待检) */
    @Excel(name = "单据状态(1/合格，2/不合格，3/待检)")
    private String status;

    /** 预留字段1 */
    private String attr1;

    /** 预留字段2 */
    private String attr2;

    /** 预留字段3 */
    private Long attr3;

    /** 预留字段4 */
    private Long attr4;

    /** 删除标志（0代表存在 1代表删除） */
    private String isDelete;
}
