<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cssl.mapper.BasicProcessdetailsMapper">
    <resultMap id="selectBasicProcessdetails" type="com.cssl.pojo.BasicProcessdetails">
        <id column="processdetails_id" property="processdetails_id"/>
        <association property="basicWlgl" javaType="com.cssl.pojo.BasicWlgl">
            <id column="material_id" property="material_id"/>
        </association>
        <association property="basicProcess" javaType="com.cssl.pojo.BasicProcess">
            <id column="process_id" property="process_id"/>
        </association>
        <association property="basicStation" javaType="com.cssl.pojo.BasicStation">
            <id column="station_id" property="station_id"/>
        </association>
    </resultMap>
    <update id="delBasicProcessdetails">
        update basic_processdetails
        <trim prefix="SET" suffixOverrides=",">
            <if test="is_delete !=null">is_delete=1</if>
        </trim>
        where process_id = #{process_id}
    </update>
    <update id="delBatchBasicProcessdetails">
        update basic_processdetails set is_delete = '1' where processdetails_id in
        <foreach item="processIds" collection="list" open="(" separator="," close=")">
            #{processIds}
        </foreach>
    </update>
    <update id="delBatchBasicProcessdetailsByProcessId">
        update basic_processdetails set is_delete = '1' where process_id in
        <foreach item="processdetailsIds" collection="list" open="(" separator="," close=")">
            #{processdetailsIds}
        </foreach>
    </update>


    <select id="listBasicProcessdetails" resultType="com.cssl.pojo.BasicProcessdetails">
        SELECT * from basic_processdetails bd inner join basic_process bp on bd.process_id=bp.process_id inner join basic_wl bw on bd.material_id=bw.material_id inner join basic_station
            bs on bd.station_id=bs.station_id WHERE bd.is_delete=0 and bp.is_delete=0 and bw.is_detele=0 and bs.is_delete=0
        <if test="basicProcess !=null and basicProcess.process_id !=null">
            and bd.process_id=#{basicProcess.process_id}
        </if>

    </select>

    <select id="listBasicProcessdetailsByProcessId" resultType="com.cssl.pojo.BasicProcessdetails">
        SELECT * from basic_processdetails bd inner join basic_process bp on bd.process_id=bp.process_id inner join basic_wl bw on bd.material_id=bw.material_id inner join basic_station
        bs on bd.station_id=bs.station_id WHERE bd.is_delete=0 and bp.is_delete=0 and bw.is_detele=0 and bs.is_delete=0
        <if test="process_id !=null and process_id !=0">
            and bd.process_id=#{process_id}
        </if>
    </select>
    <select id="listBasicProcessdetailsvoBy" resultType="com.cssl.pojo.vo.BasicProcessdetailsVo">
        SELECT bd.processdetails_id, bd.material_id, bd.station_id, bd.process_id, bd.usage_num, bs.station_code, bs.station_name, bs.remarks, bw.material_code, bw.material_name, bw.material_sfn ,bw.material_unit, bd.create_by, bd.create_time,bd.update_by, bd.update_time from basic_processdetails bd  inner join basic_wl bw on bd.material_id=bw.material_id inner join basic_station
        bs on bd.station_id=bs.station_id WHERE bd.is_delete=0  and bw.is_detele=0 and bs.is_delete=0
        <if test="process_id !=null and process_id !=0">
            and bd.process_id=#{process_id}
        </if>
    </select>

</mapper>