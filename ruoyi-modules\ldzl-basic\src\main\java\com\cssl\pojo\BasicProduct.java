package com.cssl.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

@Data
@TableName("basic_products")
public class BasicProduct extends BaseEntity {
    @TableId(value ="product_id",type = IdType.AUTO)
    private Integer product_id;
    private String product_code; // 产品编码
    private String product_name; // 产品名称
    private String product_sfn; // 规格型号
    private String product_unit; // 单位名称
    private String product_type; // 产品类型
    private String product_status; // 状态
    private Date expirydate; // 有效时间
    private BigDecimal purchaseprice; // 采购价格
    private BigDecimal saleprice; // 销售价格
    private String remarks; // 备注
    private String is_delete; // 逻辑删除
    private String img; // 产品图片
    private String create_by;
    private Date create_time;
    private String update_by;
    private Date update_time;
    @TableField(exist = false)
    private BasicWlflz basicWlflz;
    private String productcategory;
    @TableField(exist = false)
    private BasicWlfl basicWlfl;
    private BigInteger material_subcategory_id;
    private BigInteger material_classification_id;

}
