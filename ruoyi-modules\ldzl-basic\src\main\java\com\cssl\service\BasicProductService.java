package com.cssl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cssl.pojo.BasicProduct;
import com.cssl.pojo.BasicUnits;

import java.util.List;

public interface BasicProductService  extends IService<BasicProduct> {
    //查询产品信息
    public List<BasicProduct> listBasicProductMapper(BasicProduct basicProduct);

    //添加产品信息
    public int addBasicProduct(BasicProduct basicProduct);

    //修改产品信息
    public int updateBasicProduct(BasicProduct basicProduct);

    //删除产品信息
    public int delBasicProduct(Long product_id);

    //批量删除产品信息
    public int delBasicProductByIds(List<Long> product_ids);

}
