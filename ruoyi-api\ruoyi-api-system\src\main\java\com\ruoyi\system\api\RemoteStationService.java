package com.ruoyi.system.api;
import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.system.api.domain.BasicStationDto;
import com.ruoyi.system.api.factory.RemoteStationFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import java.util.List;

@FeignClient(contextId = "remoteStationService", value = ServiceNameConstants.LDZL_BASIC_SERVICE, fallbackFactory = RemoteStationFallbackFactory.class)
public interface RemoteStationService {

    /**
     * 根据工位ID列表查询工位列表
     * @param stationIds 工位ID列表
     * @return 结果
     */
    @PostMapping("/sta/listByIds")
     R<List<BasicStationDto>> getStationListByIds(@RequestBody List<Long> stationIds);

    /**
     * 根据工序ID列表查询关联的工位列表
     * @param processIds 工序ID列表
     * @param source 请求来源
     * @return 结果
     */
    @PostMapping("/sta/listByProcessIds")
     R<List<BasicStationDto>> getStationListByProcessIds(@RequestBody List<Long> processIds, @RequestHeader(name = "from-source") String source);
} 