package com.cssl.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

@Data
@TableName("basic_customers")
public class BasicCustomers extends BaseEntity {
    @TableId(value ="customer_id",type = IdType.AUTO)
    private Integer customer_id;
    private String customer_code; // 客户编码
    private String customer_name; // 客户名称
    private String customer_type; // 客户类型
    private String customer_person; // 联系人
    private String customer_phone; // 联系电话
    private String cp_status; // 联系电话是否启用
    private String customer_status; // 客户是否启用
    private String remarks; // 备注
    private String is_detele; // 逻辑删除
    private String create_by;
    private Date create_time;
    private String update_by;
    private Date update_time;
}
