<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cssl.mapper.BasicTeamMapper">
    <resultMap id="selectBasicTeam" type="com.cssl.pojo.BasicTeam">
        <id column="team_id" property="team_id"/>
        <collection property="basicTeamdetails" ofType="com.cssl.pojo.BasicTeamdetails">
            <id column="teamdetails_id" property="teamdetails_id"/>
        </collection>

    </resultMap>
    <insert id="insertBasicTeam" useGeneratedKeys="true" keyProperty="team_id">
        INSERT into basic_team
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="team_code!= null and team_code!=''">team_code,</if>
            <if test="team_name != null and team_name != ''">team_name,</if>
            <if test="workshop_id != null and workshop_id !='' ">workshop_id,</if>
            <if test="workshop_name != null and workshop_name != ''" >workshop_name,</if>
            <if test="production_line_id != null and production_line_id !=''">production_line_id,</if>
            <if test="production_line_name != null and production_line_name !='' ">production_line_name,</if>
            <if test="team_status != null and team_status !='' ">team_status,</if>
            <if test="remarks != null and remarks !=''">remarks,</if>
            <if test="is_delete != null ">is_delete,</if>
            <if test="create_by != null and create_by !='' ">create_by,</if>
            <if test="create_time!= null ">create_time,</if>
            <if test="team_num !=null">team_num,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="team_code!= null and team_code!=''">#{team_code},</if>
            <if test="team_name != null and team_name != ''">#{team_name},</if>
            <if test="workshop_id != null and workshop_id !='' ">#{workshop_id},</if>
            <if test="workshop_name != null and workshop_name != ''" >#{workshop_name},</if>
            <if test="production_line_id != null and production_line_id !=''">#{production_line_id},</if>
            <if test="production_line_name != null and production_line_name !='' ">#{production_line_name},</if>
            <if test="team_status != null and team_status !='' ">#{team_status},</if>
            <if test="remarks != null and remarks !=''">#{remarks},</if>
            <if test="is_delete != null ">#{is_delete},</if>
            <if test="create_by != null and create_by !='' ">#{create_by},</if>
            <if test="create_time!= null ">#{create_time},</if>
            <if test="team_num !=null">#{team_num},</if>
        </trim>
    </insert>
    <update id="updateBasicTeam">
        update basic_team
        <trim prefix="SET" suffixOverrides=",">
            <if test="team_name != null and team_name != ''">team_name = #{team_name},</if>
            <if test="workshop_id != null ">workshop_id = #{workshop_id},</if>
            <if test="workshop_name != null and workshop_name !='' ">workshop_name = #{workshop_name},</if>
            <if test="production_line_id != null and production_line_id !=''">production_line_id = #{production_line_id},</if>
            <if test="production_line_name != null and production_line_name !='' ">production_line_name = #{production_line_name},</if>
            <if test="team_num !=null">team_num=#{team_num},</if>
            <if test="remarks != null and remarks !='' ">remarks = #{remarks},</if>
            <if test="update_by != null and update_by !='' ">update_by=#{update_by},</if>
            <if test="update_time!= null ">update_time=#{update_time},</if>
        </trim>
        where team_id = #{team_id}
    </update>
    <update id="delBasicTeam">
        update basic_team
        <trim prefix="SET" suffixOverrides=",">
            <if test="is_delete != null ">is_delete =1,</if>
        </trim>
        where team_id = #{team_id}
    </update>
    <update id="delBatchBasicTeam">
        update basic_team set is_delete = '1' where team_id in
        <foreach item="team_ids" collection="list" open="(" separator="," close=")">
            #{team_ids}
        </foreach>
    </update>


    <select id="listBasicTeam" resultType="com.cssl.pojo.BasicTeam">
        SELECT * from basic_team bt inner join basic_workshop bw on bt.workshop_id=bw.workshop_id inner join basic_productionline br on br.production_line_id=bt.production_line_id WHERE  bt.is_delete=0 and bw.is_delete=0 and br.is_delete=0
        <if test="team_name !=null and team_name !=''">and team_name like CONCAT('%',#{team_name},'%')</if>
        <if test="team_status !=null and team_status !=''">and team_status=#{team_status}</if>
    </select>
</mapper>