package com.ldzl.controller.inventory;

import com.ldzl.pojo.CkStockDetail;
import com.ldzl.pojo.CkWarning;
import com.ldzl.service.CkStockDetailService;
import com.ldzl.service.CkWarningService;
import com.ruoyi.common.core.domain.UR;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.system.api.domain.BasicWlfl;
import com.ruoyi.system.api.domain.BasicWlflz;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 库存明细控制器
 */
@RestController
@RequestMapping("/inventory")
public class InventoryController extends BaseController {

    @Autowired
    private CkStockDetailService sds;  //库存明细

    @Autowired
    private CkWarningService ws; //库存预警

    /**
     * 根据产品编号查看对应库存 和库存上限
     * @param code
     * @return
     */
    @GetMapping("/viewInventory/{code}")
    public UR viewInventory(@PathVariable("code") String code) {
        CkWarning warning = ws.selectWarning(code);
        Map<String, Object> map = new HashMap<>();
        map.put("当前库存", warning);
        return UR.ok("成功", map);
    }

    /**
     * 根据产品编号查看对应库存 和库存上限 批量
     * @param listCode
     * @return
     */
    @PostMapping("/viewInventoryList")
    public UR viewInventoryList(@RequestParam("listCode") List<String> listCode) {
        List<CkWarning> warningList = ws.selectWarningList(listCode);
        Map<String, Object> map = new HashMap<>();
        map.put("当前库存", warningList);
        return UR.ok("成功", map);
    }

    /**
     * 查询所有库存明细
     * @return
     */
    @GetMapping("/findAll")
    public TableDataInfo findAll(CkStockDetail stockDetail){
        startPage();
        return getDataTable(sds.selectStock(stockDetail));
    }

    /**
     * 查询所有分类数据
     * @return
     */
    @PostMapping("/findType")
    public List<BasicWlfl> findType(){
        return sds.findWlflz();
    }


    /**
     * 修改库存
     * @param item_code
     * @param quantity_num
     * @return
     */
    @PostMapping("/modifyInventory")
    public UR modifyInventory(@RequestParam("item_code") String item_code,@RequestParam("quantity_num") BigDecimal quantity_num){
        if(sds.updateInventory(item_code,quantity_num)){
            return UR.ok("修改成功");
        }else{
            return UR.fail("修改失败");
        }
    }

    /**
     * 修改库存 批量
     * @param map
     * @return
     */
    @PostMapping("/modifyInventoryBatch")
    public UR modifyInventoryBatch(@RequestBody Map<String, BigDecimal> map){
        if(sds.updateInventoryBatch(map)){
            return UR.ok("修改成功");
        }else{
            return UR.fail("修改失败");
        }
    }
}
