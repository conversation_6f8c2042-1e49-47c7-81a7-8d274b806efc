package com.ldzl.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 查询库存数量
 */
@Data
public class QueryInventoryNumDTO extends BaseEntity implements Serializable {

    /**
     * 商品编号
     */
    private String item_code;

    /**
     * 库存数量
     */
    private BigDecimal quantity_num;

    /**
     * 库存保留数量
     */
    private BigDecimal reserved_num;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
