package com.ruoyi.system.api.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 生产任务DTO
 * 用于质检模块查询工单、工序、任务信息
 *
 * <AUTHOR>
 */
@Data
public class ScTaskDto {
    
    /** 工序任务ID */
    private Long workOrderTaskId;
    
    /** 任务编号 */
    private String taskCode;
    
    /** 工序名称 */
    private String taskName;
    
    /** 工序编码 */
    private String processCode;
    
    /** 工单编号 */
    private String workOrderCode;
    
    /** 工单名称 */
    private String workOrderName;
    
    /** 产品名称 */
    private String productName;
    
    /** 车间名称 */
    private String workshopName;
    
    /** 工艺路线名称 */
    private String operationalName;
    
    /** 计划数量 */
    private BigDecimal planQuantity;
    
    /** 已完成数量 */
    private BigDecimal actualProduction;
    
    /** 任务状态 */
    private String status;
    
    /** 颜色标识 */
    private String color;
    
    /** 计划开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date planStartTime;
    
    /** 计划结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date planEndTime;
    
    /** 创建者 */
    private String createBy;
    
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;
    
    /** 更新者 */
    private String updateBy;
    
    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date updateTime;
}
